<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <CalculationMode>Interpretation</CalculationMode>
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="1">
      <hotelCheckInReport Ref="2" type="Stimulsoft.Report.Dictionary.StiJsonDatabase" isKey="true">
        <Alias>hotelCheckInReport</Alias>
        <HeadersString />
        <Key />
        <Name>hotelCheckInReport</Name>
        <PathData>D:\11\2\pms-report\public\reports\json\hotelCheckInReport.json</PathData>
      </hotelCheckInReport>
    </Databases>
    <DataSources isList="true" count="3">
      <root Ref="3" type="DataTableSource" isKey="true">
        <Alias>root</Alias>
        <Columns isList="true" count="4">
          <value>code,System.Decimal</value>
          <value>data,System.String</value>
          <value>msg,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>093952bc9e51451aa48e89972aaa134a</Key>
        <Name>root</Name>
        <NameInSource>hotelCheckInReport.root</NameInSource>
      </root>
      <root_data Ref="4" type="DataTableSource" isKey="true">
        <Alias>root_data</Alias>
        <Columns isList="true" count="7">
          <value>hname,System.String</value>
          <value>startTime,System.String</value>
          <value>endTime,System.String</value>
          <value>lastSelectTime,System.String</value>
          <value>operator,System.String</value>
          <value>list,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>bece1cb067114963bdcbe5a898552589</Key>
        <Name>root_data</Name>
        <NameInSource>hotelCheckInReport.root_data</NameInSource>
      </root_data>
      <root_data_list Ref="5" type="DataTableSource" isKey="true">
        <Alias>root_data_list</Alias>
        <Columns isList="true" count="32">
          <value>gcode,System.String</value>
          <value>hcode,System.String</value>
          <value>orderNo,System.String</value>
          <value>checkinTime,System.String</value>
          <value>planCheckoutTime,System.String</value>
          <value>nowPlanCheckoutTime,System.String</value>
          <value>creator,System.String</value>
          <value>creatorName,System.String</value>
          <value>createTime,System.String</value>
          <value>operateType,System.String</value>
          <value>operateTypeName,System.String</value>
          <value>shiftNo,System.String</value>
          <value>shiftName,System.String</value>
          <value>guestSrcType,System.String</value>
          <value>guestCode,System.String</value>
          <value>guestSrcTypeName,System.String</value>
          <value>levelOrCompanyName,System.String</value>
          <value>guestName,System.String</value>
          <value>checkinType,System.String</value>
          <value>hourCode,System.String</value>
          <value>rtCode,System.String</value>
          <value>seller,System.String</value>
          <value>sellerName,System.String</value>
          <value>statChannelCodeName,System.String</value>
          <value>checkinTypeName,System.String</value>
          <value>rtName,System.String</value>
          <value>price,System.Decimal</value>
          <value>roomFee,System.Decimal</value>
          <value>otherFee,System.Decimal</value>
          <value>consumeFee,System.Decimal</value>
          <value>rNo,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>f51f6bf8514f49959fe11c8c1b3e2dd0</Key>
        <Name>root_data_list</Name>
        <NameInSource>hotelCheckInReport.root_data_list</NameInSource>
      </root_data_list>
    </DataSources>
    <Relations isList="true" count="2">
      <root Ref="6" type="DataRelation" isKey="true">
        <Alias>root</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="4" />
        <Dictionary isRef="1" />
        <Name>root</Name>
        <NameInSource>root_data</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>data</value>
        </ParentColumns>
        <ParentSource isRef="3" />
      </root>
      <root_data Ref="7" type="DataRelation" isKey="true">
        <Alias>root_data</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="5" />
        <Dictionary isRef="1" />
        <Name>root_data</Name>
        <NameInSource>root_data_list</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>list</value>
        </ParentColumns>
        <ParentSource isRef="4" />
      </root_data>
    </Relations>
    <Report isRef="0" />
    <Resources isList="true" count="0" />
    <UserFunctions isList="true" count="0" />
    <Variables isList="true" count="0" />
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <Key>655c16018dc24433b370d962f50fc1b1</Key>
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="8" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="5">
        <页眉1 Ref="9" type="PageHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,44,0.8</ClientRectangle>
          <Components isList="true" count="2">
            <Text2 Ref="10" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,7,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Name>Text2</Name>
              <Page isRef="8" />
              <Parent isRef="9" />
              <Text>{root_data.hname}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text2>
            <Text8 Ref="11" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>38.4,0,5.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Name>Text8</Name>
              <Page isRef="8" />
              <Parent isRef="9" />
              <Text>第{PageNumber}页，共{TotalPageCount}页</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text8>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>页眉1</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
        </页眉1>
        <报表标题区1 Ref="12" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,2,44,2.2</ClientRectangle>
          <Components isList="true" count="2">
            <Text3 Ref="13" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>19.2,0,5.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,12,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text3</Name>
              <Page isRef="8" />
              <Parent isRef="12" />
              <Text>开房记录明细报表</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text3>
            <Text5 Ref="14" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.4,29.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Name>Text5</Name>
              <Page isRef="8" />
              <Parent isRef="12" />
              <Text>门店:{root_data.hname}  操作时间:{root_data.startTime}-{root_data.endTime} 最后查询时间：{root_data.lastSelectTime.Substring(0, 19).Replace("-", "/")}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text5>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>报表标题区1</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
        </报表标题区1>
        <页眉2 Ref="15" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,5,44,0.8</ClientRectangle>
          <Components isList="true" count="19">
            <Text6 Ref="16" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,1.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text6</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>序号</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text6>
            <文本1 Ref="17" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.6,0,1.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本1</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>客源</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本1>
            <文本2 Ref="18" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.2,0,2.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本2</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>会员级别/公司</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本2>
            <文本3 Ref="19" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5.8,0,1.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本3</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>统计渠道</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本3>
            <文本4 Ref="20" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.4,0,1.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本4</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>房号</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本4>
            <文本5 Ref="21" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9,0,2.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本5</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>房型</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本5>
            <文本6 Ref="22" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>11.6,0,1.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本6</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>客人姓名</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本6>
            <文本7 Ref="23" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.4,0,3.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Interaction Ref="24" type="Stimulsoft.Report.Components.StiInteraction" isKey="true">
                <SortingColumn>数据区1.checkinTime</SortingColumn>
              </Interaction>
              <Name>文本7</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>入住时间</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本7>
            <文本8 Ref="25" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>17,0,3.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Interaction Ref="26" type="Stimulsoft.Report.Components.StiInteraction" isKey="true">
                <SortingColumn>数据区1.planCheckoutTime</SortingColumn>
              </Interaction>
              <Name>文本8</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>原离店时间</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本8>
            <文本9 Ref="27" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>20.6,0,3.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Interaction Ref="28" type="Stimulsoft.Report.Components.StiInteraction" isKey="true">
                <SortingColumn>数据区1.nowPlanCheckoutTime</SortingColumn>
              </Interaction>
              <Name>文本9</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>现离店时间</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本9>
            <文本10 Ref="29" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>24.2,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本10</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>入住类型</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本10>
            <文本11 Ref="30" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>26.4,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本11</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>操作类型</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本11>
            <文本12 Ref="31" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>28.6,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Interaction Ref="32" type="Stimulsoft.Report.Components.StiInteraction" isKey="true">
                <SortingColumn>数据区1.price</SortingColumn>
              </Interaction>
              <Name>文本12</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>首日房价</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本12>
            <文本13 Ref="33" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>30.8,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Interaction Ref="34" type="Stimulsoft.Report.Components.StiInteraction" isKey="true">
                <SortingColumn>数据区1.roomFee</SortingColumn>
              </Interaction>
              <Name>文本13</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>房费</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本13>
            <文本14 Ref="35" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>33,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Interaction Ref="36" type="Stimulsoft.Report.Components.StiInteraction" isKey="true">
                <SortingColumn>数据区1.otherFee</SortingColumn>
              </Interaction>
              <Name>文本14</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>其他消费</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本14>
            <文本15 Ref="37" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>35.2,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Interaction Ref="38" type="Stimulsoft.Report.Components.StiInteraction" isKey="true">
                <SortingColumn>数据区1.consumeFee</SortingColumn>
              </Interaction>
              <Name>文本15</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>消费总计</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本15>
            <文本16 Ref="39" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>37.4,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本16</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>销售员</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本16>
            <文本17 Ref="40" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>39.8,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本17</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>操作员</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本17>
            <文本18 Ref="41" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>42.2,0,1.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本18</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>班次</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本18>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>页眉2</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
        </页眉2>
        <数据区1 Ref="42" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,6.6,44,0.8</ClientRectangle>
          <Components isList="true" count="19">
            <Text31 Ref="43" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,1.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,0,0,0</Margins>
              <Name>Text31</Name>
              <Page isRef="8" />
              <Parent isRef="42" />
              <Text>{Line}</Text>
              <TextBrush>Black</TextBrush>
              <Type>SystemVariables</Type>
              <VertAlignment>Center</VertAlignment>
            </Text31>
            <文本19 Ref="44" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.6,0,1.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,0,0,0</Margins>
              <Name>文本19</Name>
              <Page isRef="8" />
              <Parent isRef="42" />
              <Text>{root_data_list.guestSrcTypeName}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本19>
            <文本20 Ref="45" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.2,0,2.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,0,0,0</Margins>
              <Name>文本20</Name>
              <Page isRef="8" />
              <Parent isRef="42" />
              <Text>{root_data_list.levelOrCompanyName}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本20>
            <文本21 Ref="46" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5.8,0,1.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,0,0,0</Margins>
              <Name>文本21</Name>
              <Page isRef="8" />
              <Parent isRef="42" />
              <Text>{root_data_list.statChannelCodeName}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本21>
            <文本22 Ref="47" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.4,0,1.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,0,0,0</Margins>
              <Name>文本22</Name>
              <Page isRef="8" />
              <Parent isRef="42" />
              <Text>{root_data_list.rNo}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本22>
            <文本23 Ref="48" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9,0,2.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,0,0,0</Margins>
              <Name>文本23</Name>
              <Page isRef="8" />
              <Parent isRef="42" />
              <Text>{root_data_list.rtName}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本23>
            <文本24 Ref="49" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>11.6,0,1.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,0,0,0</Margins>
              <Name>文本24</Name>
              <Page isRef="8" />
              <Parent isRef="42" />
              <Text>{root_data_list.guestName}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本24>
            <文本25 Ref="50" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.4,0,3.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,0,0,0</Margins>
              <Name>文本25</Name>
              <Page isRef="8" />
              <Parent isRef="42" />
              <Text>{root_data_list.checkinTime}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="51" type="DateFormat" isKey="true">
                <StringFormat>G</StringFormat>
              </TextFormat>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本25>
            <文本26 Ref="52" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>17,0,3.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,0,0,0</Margins>
              <Name>文本26</Name>
              <Page isRef="8" />
              <Parent isRef="42" />
              <Text>{root_data_list.planCheckoutTime}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="53" type="DateFormat" isKey="true">
                <StringFormat>G</StringFormat>
              </TextFormat>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本26>
            <文本27 Ref="54" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>20.6,0,3.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,0,0,0</Margins>
              <Name>文本27</Name>
              <Page isRef="8" />
              <Parent isRef="42" />
              <Text>{root_data_list.nowPlanCheckoutTime}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="55" type="DateFormat" isKey="true">
                <StringFormat>G</StringFormat>
              </TextFormat>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本27>
            <文本28 Ref="56" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>24.2,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,0,0,0</Margins>
              <Name>文本28</Name>
              <Page isRef="8" />
              <Parent isRef="42" />
              <Text>{root_data_list.checkinTypeName}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本28>
            <文本29 Ref="57" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>26.4,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,0,0,0</Margins>
              <Name>文本29</Name>
              <Page isRef="8" />
              <Parent isRef="42" />
              <Text>{root_data_list.operateTypeName}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本29>
            <文本30 Ref="58" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>28.6,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本30</Name>
              <Page isRef="8" />
              <Parent isRef="42" />
              <Text>{root_data_list.price}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本30>
            <文本31 Ref="59" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>30.8,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本31</Name>
              <Page isRef="8" />
              <Parent isRef="42" />
              <Text>{root_data_list.roomFee}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本31>
            <文本32 Ref="60" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>33,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本32</Name>
              <Page isRef="8" />
              <Parent isRef="42" />
              <Text>{root_data_list.otherFee}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本32>
            <文本33 Ref="61" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>35.2,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本33</Name>
              <Page isRef="8" />
              <Parent isRef="42" />
              <Text>{root_data_list.consumeFee}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本33>
            <文本34 Ref="62" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>37.4,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,0,0,0</Margins>
              <Name>文本34</Name>
              <Page isRef="8" />
              <Parent isRef="42" />
              <Text>{root_data_list.sellerName}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本34>
            <文本35 Ref="63" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>39.8,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,0,0,0</Margins>
              <Name>文本35</Name>
              <Page isRef="8" />
              <Parent isRef="42" />
              <Text>{root_data_list.creatorName}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本35>
            <文本36 Ref="64" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>42.2,0,1.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,0,0,0</Margins>
              <Name>文本36</Name>
              <Page isRef="8" />
              <Parent isRef="42" />
              <Text>{root_data_list.shiftName}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本36>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>root_data_list</DataSourceName>
          <Expressions isList="true" count="0" />
          <Filters isList="true" count="0" />
          <Name>数据区1</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
          <Sort isList="true" count="0" />
        </数据区1>
        <页脚1 Ref="65" type="FooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,8.2,44,0.8</ClientRectangle>
          <Components isList="true" count="7">
            <文本37 Ref="66" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,30.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本37</Name>
              <Page isRef="8" />
              <Parent isRef="65" />
              <Text>总计</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本37>
            <文本38 Ref="67" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>30.8,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本38</Name>
              <Page isRef="8" />
              <Parent isRef="65" />
              <Text>{Sum(root_data_list.roomFee)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </文本38>
            <文本39 Ref="68" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>33,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本39</Name>
              <Page isRef="8" />
              <Parent isRef="65" />
              <Text>{Sum(root_data_list.otherFee)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </文本39>
            <文本40 Ref="69" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>35.2,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本40</Name>
              <Page isRef="8" />
              <Parent isRef="65" />
              <Text>{Sum(root_data_list.consumeFee)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </文本40>
            <文本41 Ref="70" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>37.4,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Margins>5,0,0,0</Margins>
              <Name>文本41</Name>
              <Page isRef="8" />
              <Parent isRef="65" />
              <TextBrush>Black</TextBrush>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </文本41>
            <文本42 Ref="71" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>39.8,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Margins>5,0,0,0</Margins>
              <Name>文本42</Name>
              <Page isRef="8" />
              <Parent isRef="65" />
              <TextBrush>Black</TextBrush>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </文本42>
            <文本43 Ref="72" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>42.2,0,1.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Margins>5,0,0,0</Margins>
              <Name>文本43</Name>
              <Page isRef="8" />
              <Parent isRef="65" />
              <TextBrush>Black</TextBrush>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </文本43>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>页脚1</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
        </页脚1>
      </Components>
      <Conditions isList="true" count="0" />
      <Expressions isList="true" count="0" />
      <Guid>3bd25e28dcc3462cbd08f861287c1d16</Guid>
      <Margins>1,1,1,1</Margins>
      <Name>Page1</Name>
      <PageHeight>29.7</PageHeight>
      <PageWidth>46</PageWidth>
      <Report isRef="0" />
    </Page1>
  </Pages>
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>报表</ReportAlias>
  <ReportAuthor>xiao lin</ReportAuthor>
  <ReportChanged>7/1/2025 2:41:45 PM</ReportChanged>
  <ReportCreated>4/17/2025 10:48:11 AM</ReportCreated>
  <ReportFile>D:\11\2\pms-report\public\reports\HotelCheckInReport.mrt</ReportFile>
  <ReportGuid>d48bc52da8e14c418cbed5689e73c576</ReportGuid>
  <ReportName>报表</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2024.3.5.0</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class 报表 : Stimulsoft.Report.StiReport
    {
        public 报表()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
		#endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>