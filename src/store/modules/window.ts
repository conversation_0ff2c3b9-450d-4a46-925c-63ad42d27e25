import useMenuStore from './menu'

export const useWindowStore = defineStore(
  // 唯一ID
  'windows',
  () => {
    const isAllWindowsVisible = ref(true)
    function toggleVisibleAllWindows(val = !isAllWindowsVisible.value) {
      isAllWindowsVisible.value = val
    }

    const list = ref<window[]>([])

    function add(data: string | window) {
      let preData: window
      if (typeof data === 'string') {
        const menuStore = useMenuStore()
        preData = {
          name: data,
          // width: '80%',
          width: menuStore.flatMenu[data].windowWidth,
          title: menuStore.flatMenu[data].title,
          noTitle: menuStore.flatMenu[data].noTitle,
          params: menuStore.flatMenu[data].params,
          breadcrumbNeste: menuStore.flatMenu[data].breadcrumbNeste,
          isMaximize: false,
          reload: false,
        }
      }
      else {
        preData = {
          name: data.name,
          width: data.width,
          title: data.title,
          noTitle: data.noTitle,
          params: data.params,
          breadcrumbNeste: [],
          isMaximize: false,
          reload: false,
        }
      }
      // 无则添加，有则更新
      const index = list.value.findIndex(item => item.name === preData.name)
      if (index < 0) {
        list.value.push(preData)
      }
      else {
        Object.assign(list.value[index], preData)
      }
      isAllWindowsVisible.value = true
    }
    function remove(name: string) {
      list.value = list.value.filter(item => item.name !== name)
    }
    function removeOtherSide(name: string) {
      list.value = list.value.filter(item => item.name === name)
    }
    function removeLeftSide(name: string) {
      // 查找指定路由对应在标签页列表里的下标
      const index = list.value.findIndex(item => item.name === name)
      list.value = list.value.filter((item, i) => {
        return i >= index
      })
    }
    function removeRightSide(name: string) {
      // 查找指定路由对应在标签页列表里的下标
      const index = list.value.findIndex(item => item.name === name)
      list.value = list.value.filter((item, i) => {
        return i <= index
      })
    }
    function removeAll() {
      list.value = []
    }
    function sort(data: window[]) {
      list.value = data
    }
    function toggleMaximize(name: string) {
      list.value.map((item) => {
        item.isMaximize = item.name === name
          ? !item.isMaximize
          : false
        return item
      })
    }
    function allMinimize() {
      list.value.some(item => item.isMaximize = false)
    }
    function reload(name: string) {
      list.value.map((item) => {
        if (item.name === name) {
          item.reload = !item.reload
        }
        return item
      })
    }

    return {
      isAllWindowsVisible,
      toggleVisibleAllWindows,
      list,
      add,
      remove,
      removeOtherSide,
      removeLeftSide,
      removeRightSide,
      removeAll,
      sort,
      toggleMaximize,
      allMinimize,
      reload,
    }
  },
)

export default useWindowStore
