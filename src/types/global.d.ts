type RecursiveRequired<T> = {
  [P in keyof T]-?: RecursiveRequired<T[P]>
}
type RecursivePartial<T> = {
  [P in keyof T]?: RecursivePartial<T[P]>
}

declare namespace Settings {
  interface app {
    /**
     * 亮色主题
     * @默认值 `'light'`
     * @可选值 可选择 `/themes/index.ts` 里所有 `color-scheme: light` 的主题
     */
    lightTheme?: string
    /**
     * 暗色主题
     * @默认值 `'dark'`
     * @可选值 可选择 `/themes/index.ts` 里所有 `color-scheme: dark` 的主题
     */
    darkTheme?: string
    /**
     * 颜色方案
     * @默认值 `''` 跟随系统
     * @可选值 `'light'` 明亮模式
     * @可选值 `'dark'` 暗黑模式
     */
    colorScheme?: '' | 'light' | 'dark'
    /**
     * 是否开启哀悼模式
     * @默认值 `false`
     */
    enableMournMode?: boolean
    /**
     * 是否开启色弱模式
     * @默认值 `false`
     */
    enableColorAmblyopiaMode?: boolean
    /**
     * 默认语言
     * @默认值 `''` 跟随浏览器语言设置
     * @可选值 参考 `/src/locales/index.ts` 里的语言列表
     */
    defaultLang?: string
    /**
     * 是否开启权限功能
     * @默认值 `false`
     */
    enablePermission?: boolean
    /**
     * localStorage/sessionStorage 前缀
     * @默认值 `'fa_'`
     */
    storagePrefix?: string
    /**
     * 是否开启页面水印
     * @默认值 `false`
     */
    enableWatermark?: boolean
    /**
     * 是否在非开发环境开启错误日志功能，具体业务代码在 `/src/utils/error.log.ts`
     * @默认值 `false`
     */
    enableErrorLog?: boolean
    /**
     * 文字方向
     * @默认值 `'ltr'` 从左到右
     * @可选值 `'rtl'` 从右到左
     */
    direction?: 'ltr' | 'rtl'
  }
  interface userPreferences {
    /**
     * 是否开启用户偏好设置
     * @默认值 `false`
     */
    enable?: boolean
    /**
     * 存储位置
     * @默认值 `'local'` 本地存储
     * @可选值 `'server'` 服务器存储
     */
    storageTo?: 'local' | 'server'
  }
  interface menu {
    /**
     * 导航栏数据来源，当 `app.routeBaseOn: 'filesystem'` 时生效
     * @默认值 `'frontend'` 前端
     * @可选值 `'backend'` 后端
     */
    baseOn?: 'frontend' | 'backend'
    /**
     * 导航栏模式
     * @默认值 `'side'` 侧边栏模式（有主导航）
     * @可选值 `'head'` 顶部模式
     * @可选值 `'single'` 侧边栏模式（无主导航）
     * @可选值 `'only-side'` 侧边栏精简模式
     * @可选值 `'only-head'` 顶部精简模式
     * @可选值 `'side-panel'` 侧边栏面板模式
     * @可选值 `'head-panel'` 顶部面板模式
     */
    mode?: 'side' | 'head' | 'single' | 'only-side' | 'only-head' | 'side-panel' | 'head-panel'
    /**
     * 导航栏是否圆角
     * @默认值 `false`
     */
    isRounded?: boolean
    /**
     * 导航栏风格
     * @默认值 `''`
     * @可选值 `'arrow'` 箭头
     * @可选值 `'line'` 线条
     * @可选值 `'dot'` 圆点
     */
    style?: '' | 'arrow' | 'line' | 'dot'
    /**
     * 切换主导航同时打开窗口
     * @默认值 `false`
     */
    switchMainMenuAndOpenWindow?: boolean
    /**
     * 次导航是否只保持一个子项的展开
     * @默认值 `true`
     */
    subMenuUniqueOpened?: boolean
    /**
     * 次导航是否收起
     * @默认值 `false`
     */
    subMenuCollapse?: boolean
    /**
     * 次导航是否自动收起
     * @默认值 `false`
     */
    subMenuAutoCollapse?: boolean
    /**
     * 是否开启次导航的展开/收起按钮
     * @默认值 `false`
     */
    enableSubMenuCollapseButton?: boolean
    /**
     * 是否开启主导航切换快捷键
     * @默认值 `false`
     */
    enableHotkeys?: boolean
  }
  interface favorites {
    /**
     * 存储位置
     * @默认值 `'local'` 本地存储
     * @可选值 `'server'` 服务器存储
     */
    storageTo?: 'local' | 'server'
  }
  interface toolbar {
    /**
     * 是否开启控制台切换
     * @默认值 `true`
     */
    dashboard?: boolean
    /**
     * 是否开启窗口预览
     * @默认值 `true`
     */
    previewWindows?: boolean
    /**
     * 是否开启收藏夹
     * @默认值 `false`
     */
    favorites?: boolean
    /**
     * 是否开启导航搜索
     * @默认值 `true`
     */
    navSearch?: boolean
    /**
     * 是否开启通知中心
     * @默认值 `false`
     */
    notification?: boolean
    /**
     * 是否开启国际化
     * @默认值 `false`
     */
    i18n?: boolean
    /**
     * 是否开启全屏
     * @默认值 `false`
     */
    fullscreen?: boolean
    /**
     * 是否开启颜色主题
     * @默认值 `false`
     */
    colorScheme?: boolean
    /**
     * 布局设置，可自定义摆放位置和顺序，其中 `->` 为分隔符，用于分隔左右两侧的工具栏。修改时请确保默认值里的所有值都存在，不可删减。
     * @默认值 `['dashboard', 'previewWindows', 'favorites', '->', 'navSearch', 'notification', 'i18n', 'fullscreen', 'colorScheme']`
     */
    layout?: (Exclude<keyof toolbar, 'layout'> | '->')[]
  }
  interface navSearch {
    /**
     * 是否开启导航搜索快捷键
     * @默认值 `true`
     */
    enableHotkeys?: boolean
  }
  interface window {
    /**
     * 窗口默认宽度，设置为数字时单位为 px
     * @默认值 `1000`
     */
    defaultWidth?: string | number
    /**
     * 滚动后窗口自动定位
     * @默认值 `''` 关闭
     * @可选值 `'start'` 左侧
     * @可选值 `'center'` 居中
     * @可选值 `'end'` 右侧
     */
    autoPosition?: '' | 'start' | 'center' | 'end'
    /**
     * 专注模式窗口最大数量，最小值为 2
     * @默认值 `4`
     */
    focusMaxNum?: number
    /**
     * 是否开启窗口快捷键
     * @默认值 `true`
     */
    enableHotkeys?: boolean
  }
  interface copyright {
    /**
     * 是否开启底部版权
     * @默认值 `false`
     */
    enable?: boolean
    /**
     * 网站运行日期
     * @默认值 `''`
     */
    dates?: string
    /**
     * 公司名称
     * @默认值 `''`
     */
    company?: string
    /**
     * 网站地址
     * @默认值 `''`
     */
    website?: string
    /**
     * 网站备案号
     * @默认值 `''`
     */
    beian?: string
  }
  interface all {
    /** 应用设置 */
    app?: app
    /** 用户偏好设置 */
    userPreferences?: userPreferences
    /** 导航栏设置 */
    menu?: menu
    /** 收藏夹 */
    favorites?: favorites
    /** 工具栏设置 */
    toolbar?: toolbar
    /** 导航搜索设置 */
    navSearch?: navSearch
    /** 窗口设置 */
    window?: window
    /** 底部版权设置 */
    copyright?: copyright
  }
}

declare namespace Menu {
  /** 原始 */
  interface recordRaw {
    title?: string | (() => string)
    noTitle?: boolean
    icon?: string
    auth?: string | string[]
    badge?: boolean | string | number | (() => boolean | string | number)
    params?: object
    windowName?: string
    windowWidth?: string | number
    breadcrumbNeste?: Menu.breadcrumb[]
    children?: recordRaw[]
  }
  /** 主导航 */
  interface recordMainRaw {
    title?: string | (() => string)
    icon?: string
    auth?: string | string[]
    children: recordRaw[]
  }
  interface breadcrumb {
    title?: string | (() => string)
  }
}

declare namespace Favorites {
  interface recordRaw {
    windowName: string
    title?: string | (() => string)
    i18n?: string
  }
}

interface window {
  name: string
  width?: string | number
  title?: string | (() => string)
  i18n?: string
  noTitle?: boolean
  params?: object
  breadcrumbNeste?: any[]
  isMaximize?: boolean
  reload?: boolean
}
