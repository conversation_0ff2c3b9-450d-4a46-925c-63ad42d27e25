<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <CalculationMode>Interpretation</CalculationMode>
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="1">
      <payAccount Ref="2" type="Stimulsoft.Report.Dictionary.StiJsonDatabase" isKey="true">
        <Alias>payAccount</Alias>
        <HeadersString />
        <Key />
        <Name>payAccount</Name>
        <PathData>D:\11\2\pms-report\public\reports\json\payAccount.json</PathData>
      </payAccount>
    </Databases>
    <DataSources isList="true" count="3">
      <root Ref="3" type="DataTableSource" isKey="true">
        <Alias>root</Alias>
        <Columns isList="true" count="4">
          <value>code,System.Decimal</value>
          <value>data,System.String</value>
          <value>msg,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>6724a6a030824cc4b4ea800a78d902e8</Key>
        <Name>root</Name>
        <NameInSource>payAccount.root</NameInSource>
      </root>
      <root_data Ref="4" type="DataTableSource" isKey="true">
        <Alias>root_data</Alias>
        <Columns isList="true" count="7">
          <value>hname,System.String</value>
          <value>startDate,System.String</value>
          <value>endDate,System.String</value>
          <value>lastSelectTime,System.String</value>
          <value>operator,System.String</value>
          <value>list,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>7e5eb0706acf404eb58d49925d099e1b</Key>
        <Name>root_data</Name>
        <NameInSource>payAccount.root_data</NameInSource>
      </root_data>
      <root_data_list Ref="5" type="DataTableSource" isKey="true">
        <Alias>root_data_list</Alias>
        <Columns isList="true" count="24">
          <value>id,System.String</value>
          <value>gcode,System.String</value>
          <value>hcode,System.String</value>
          <value>no,System.String</value>
          <value>noType,System.String</value>
          <value>url,System.String</value>
          <value>subCode,System.String</value>
          <value>subName,System.String</value>
          <value>guestName,System.String</value>
          <value>fee,System.Decimal</value>
          <value>guestSrcType,System.String</value>
          <value>guestSrcTypeName,System.String</value>
          <value>createChannel,System.String</value>
          <value>createChannelName,System.String</value>
          <value>statChannel,System.String</value>
          <value>statChannelName,System.String</value>
          <value>bizDate,System.String</value>
          <value>createTime,System.String</value>
          <value>shiftName,System.String</value>
          <value>recorder,System.String</value>
          <value>recorderName,System.String</value>
          <value>remark,System.String</value>
          <value>rNo,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>be3775ada89b494fb91bbe3139be36d9</Key>
        <Name>root_data_list</Name>
        <NameInSource>payAccount.root_data_list</NameInSource>
      </root_data_list>
    </DataSources>
    <Relations isList="true" count="2">
      <root Ref="6" type="DataRelation" isKey="true">
        <Alias>root</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="4" />
        <Dictionary isRef="1" />
        <Name>root</Name>
        <NameInSource>root_data</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>data</value>
        </ParentColumns>
        <ParentSource isRef="3" />
      </root>
      <root_data Ref="7" type="DataRelation" isKey="true">
        <Alias>root_data</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="5" />
        <Dictionary isRef="1" />
        <Name>root_data</Name>
        <NameInSource>root_data_list</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>list</value>
        </ParentColumns>
        <ParentSource isRef="4" />
      </root_data>
    </Relations>
    <Report isRef="0" />
    <Resources isList="true" count="1">
      <value>Name=test, Alias=test, Type=Json, Content=XxyQoEmRgQaI6LbklQ8KLS1qlI8h2d7HGvmPZXdMzqZ0es047_x002B_l_x002B_NNIY5uU3GUZLIWy5IeMqOmGNn7hcP_x002F_zWs7mY5lr_x002B_kq0HcryNGBJIZpzzvH9BSDqfOvRYLsQzch6JW_x002F_DdwytZlZj7o_x002F_pJfQDBP7_x002B_aMAXLmlnE8dz3EgyGoOogeR627aWHdw3gbo0jbjZH6mb4FAZzrrN1ur00QTB9eS9DOWzPeC9IesOsRVhifQ_x002B_DhUL6QVyTAxREUp525_x002B_wssnynolMQA7G3g4PpidATuk0iuRiZwszj_x002B_iRQB51JO6qt5rcUjerNKdBV_x002B_OQF2SJrqzr9D2y1YcwClLgkEuUip336CBHCWDLGG43YR8TutLCPtvHjoF2O_x002F_E40ltSv6Tnh6ShTxsZ91ZMnJCaCRoy8rhNoZu2hAYYmO_x002B_dcg3VPvgxynrsqv6xsJ0ibu_x002F_Xb6X2qM6W8O_x002F_q00RRqHf9p_x002B_DLGHGuxEiCE2AjnPUHxRRBopxuGxENQtyyCd60nJ5AdJbuSN5Ecn75t5vcMH7MMJ2IcqACD0r1Q3t1biyhyz7SPlS2TourbiKxFv3RVrGOVjggIlPb2CFr1oQuSFGOWk1eFwlEcYkz3XIvFSD_x002B_FCioF3a8mOYr6RT48n_x002B_jgOS0KBNcdCOxmxGTaAZlocA8dzpVRMfuaGxjJ_x002B_iVJslDLIQVc6sATMJxGeURdBKXiKmlNZFolEz8Qv_x002F_ZEulSUWuePYEmQuoWkvziHqfYxqbBjVZXMuJI4_x002B_u51bRkk_x002B_xNwLG6l_x002F_VKMBo8QB_x002B_W8yZST3L7RgtMdBPEDgEDnVsTV9ZN97y5lC5WjUdW4jmCNKEF9l_x002F_b4MEu_x002F_jR6BtIcmuKu3QitbiS1MlbbsuzfrH5oii3a1G1g9WzIJrcudLvZPzTiRAHQvNIY02olfzkrMWzBb63_x002F_d1lgyQl9IA5r7plr0vGOAXuZ7InBqIEpCiTF99Ld7o_x002F_bf48txQ0YGkXnGo2TMdSqWmBGWh6DfXfOaKOeEwQswXxw_x002F_hXbTFGqXLZTYziHzhmOW7LflGebY8r3kijM2T382_x002F__x002B_cDEe8jj7rCKGraq_x002B_2bHkHPVceS7QlDE8nYaHOV6Y9qnKZstj7XKyIcZYpuY2JeN1bq9DDydkVsz1DlMhfHB2Nu2RjSAVCR6_x002B_vlLJ61dgYoFxgkv3ft3hSWAGRsGUW5cD3eLw6W67tCXRd_x002F_h5lcIfHlo9MQpz0il4HrDtQtb7VzEXaX8YyFWw_x002B_VA35YGWdmTX5wEjYn7_x002F_wIgqp7CwBy6tocvU1AkKJwQW0LnbReYGx87d_x002F_BGE2_x002B_MhsM0Xvq_x002B_xmp8ns0VV5fq8s96bBdIvgkMabBJcFEpZ8W8prrvDCgyJRqQ6jvdmdvSmEMQfpFPnfhbMwkLb1Wm6hGuDPmup0hXxuTHjRqLGbO9aY4zNJMi3Y6Qdkt3oyU8SxT6ntJBp_x002F_2HeXnjk9cGTocPR48HkSfhYhZGtOLPoLwGpPQmWS7dOc_x002F_Gq_x002B_LeaGfIQS8bRub_x002F_tc5qJt5nUYbC42EdyTNR5CKYX5nvnJu3t2B_x002F_s8GfgKRSswoSdYjmfcXuVdPXO_x002F_0PcNnRjobXKILfJJ9km1dhr4jj_x002B_wRz66KfbgPx3sp5Y_x002F_J91wtJdHjDlJAVvSDnqg5F07dvywH6pyvZgoIbuQmtHu9Qd5EgMH49i6DiUZ_x002F_yVfgCJ3Z_x002B_h0XLhO_x002F_</value>
    </Resources>
    <UserFunctions isList="true" count="0" />
    <Variables isList="true" count="1">
      <value>分类</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <Key>7f2309bf07224b2b89ae5b38956322af</Key>
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="8" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="8">
        <PageHeaderBand1 Ref="9" type="PageHeaderBand" isKey="true">
          <Border>Bottom;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,30,0.8</ClientRectangle>
          <Components isList="true" count="2">
            <Text2 Ref="10" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0.01,0,7,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Name>Text2</Name>
              <Page isRef="8" />
              <Parent isRef="9" />
              <Text>{root_data.hname}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text2>
            <Text6 Ref="11" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>24.2,0,5.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Name>Text6</Name>
              <Page isRef="8" />
              <Parent isRef="9" />
              <Text>第{PageNumber}页，共{TotalPageCount}页</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text6>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>PageHeaderBand1</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
        </PageHeaderBand1>
        <PageFooterBand1 Ref="12" type="PageFooterBand" isKey="true">
          <Border>Bottom;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>0,18.2,30,0.8</ClientRectangle>
          <Components isList="true" count="2">
            <Text45 Ref="13" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.2,2.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <Name>Text45</Name>
              <Page isRef="8" />
              <Parent isRef="12" />
              <Text>{root_data.operator_}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text45>
            <Text46 Ref="14" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>26.2,0.2,3.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Name>Text46</Name>
              <Page isRef="8" />
              <Parent isRef="12" />
              <Text>{root_data.lastSelectTime.Substring(0, 19).Replace("-", "/")}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text46>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>PageFooterBand1</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
        </PageFooterBand1>
        <ReportTitleBand1 Ref="15" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,2,30,2</ClientRectangle>
          <Components isList="true" count="2">
            <Text1 Ref="16" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>12.3,0.4,3.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,12,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text1</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>付款明细报表</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text1>
            <Text5 Ref="17" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.2,23.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Name>Text5</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>门店:{root_data.hname}  营业日:{root_data.startDate.Substring(0, 10)} - {root_data.endDate.Substring(0, 10)}  最后查询时间：{root_data.lastSelectTime.Substring(0, 19).Replace("-", "/")}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text5>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>ReportTitleBand1</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
        </ReportTitleBand1>
        <ColumnHeaderBand1 Ref="18" type="Stimulsoft.Report.Components.StiColumnHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,4.8,30,0.8</ClientRectangle>
          <Components isList="true" count="12">
            <文本1 Ref="19" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,1.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本1</Name>
              <Page isRef="8" />
              <Parent isRef="18" />
              <Text>序号</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本1>
            <文本2 Ref="20" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.4,0,4.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本2</Name>
              <Page isRef="8" />
              <Parent isRef="18" />
              <Text>订单号
</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本2>
            <文本3 Ref="21" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9.2,0,2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Interaction Ref="22" type="Stimulsoft.Report.Components.StiInteraction" isKey="true">
                <SortingColumn>DataBand1.fee</SortingColumn>
              </Interaction>
              <Name>文本3</Name>
              <Page isRef="8" />
              <Parent isRef="18" />
              <Text>金额
</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本3>
            <文本4 Ref="23" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.2,0,4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Interaction Ref="24" type="Stimulsoft.Report.Components.StiInteraction" isKey="true">
                <SortingColumn>DataBand1.createTime</SortingColumn>
              </Interaction>
              <Name>文本4</Name>
              <Page isRef="8" />
              <Parent isRef="18" />
              <Text>操作时间
</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本4>
            <文本5 Ref="25" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>19.2,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本5</Name>
              <Page isRef="8" />
              <Parent isRef="18" />
              <Text>操作员</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本5>
            <文本6 Ref="26" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>21.4,0,1.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本6</Name>
              <Page isRef="8" />
              <Parent isRef="18" />
              <Text>班次</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本6>
            <文本7 Ref="27" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.4,0,1.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本7</Name>
              <Page isRef="8" />
              <Parent isRef="18" />
              <Text>姓名</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本7>
            <文本8 Ref="28" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>24.8,0,5.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本8</Name>
              <Page isRef="8" />
              <Parent isRef="18" />
              <Text>备注
</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本8>
            <文本9 Ref="29" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>12.8,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Interaction Ref="30" type="Stimulsoft.Report.Components.StiInteraction" isKey="true">
                <SortingColumn>DataBand1.bizDate</SortingColumn>
              </Interaction>
              <Name>文本9</Name>
              <Page isRef="8" />
              <Parent isRef="18" />
              <Text>营业日</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本9>
            <文本10 Ref="31" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>11.2,0,1.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本10</Name>
              <Page isRef="8" />
              <Parent isRef="18" />
              <Text>客源</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本10>
            <文本11 Ref="32" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>23,0,1.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本11</Name>
              <Page isRef="8" />
              <Parent isRef="18" />
              <Text>统计渠道</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本11>
            <文本13 Ref="33" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5.8,0,1.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>5,0,0,0</Margins>
              <Name>文本13</Name>
              <Page isRef="8" />
              <Parent isRef="18" />
              <Text>房号</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本13>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>ColumnHeaderBand1</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
        </ColumnHeaderBand1>
        <GroupHeaderBand1 Ref="34" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,6.4,30,0.8</ClientRectangle>
          <Components isList="true" count="1">
            <Text14 Ref="35" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,30,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text14</Name>
              <Page isRef="8" />
              <Parent isRef="34" />
              <Text>{root_data_list.subName}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text14>
          </Components>
          <Condition>{root_data_list.subName}</Condition>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>GroupHeaderBand1</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
        </GroupHeaderBand1>
        <DataBand1 Ref="36" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,8,30,0.8</ClientRectangle>
          <Components isList="true" count="12">
            <Text47 Ref="37" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,1.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text47</Name>
              <Page isRef="8" />
              <Parent isRef="36" />
              <Text>{Line}</Text>
              <TextBrush>Black</TextBrush>
              <Type>SystemVariables</Type>
              <VertAlignment>Center</VertAlignment>
            </Text47>
            <Text15 Ref="38" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.4,0,4.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text15</Name>
              <Page isRef="8" />
              <Parent isRef="36" />
              <Text>{root_data_list.no}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text15>
            <Text16 Ref="39" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9.2,0,2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Hyperlink>{(root_data_list.url) ?? ""}</Hyperlink>
              <Margins>0,5,0,0</Margins>
              <Name>Text16</Name>
              <Page isRef="8" />
              <Parent isRef="36" />
              <Text>{root_data_list.fee}</Text>
              <TextBrush>[0:176:240]</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text16>
            <Text17 Ref="40" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.2,0,4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text17</Name>
              <Page isRef="8" />
              <Parent isRef="36" />
              <Text>{root_data_list.createTime}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="41" type="DateFormat" isKey="true">
                <StringFormat>G</StringFormat>
              </TextFormat>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text17>
            <Text18 Ref="42" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>19.2,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text18</Name>
              <Page isRef="8" />
              <Parent isRef="36" />
              <Text>{root_data_list.recorderName}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text18>
            <Text19 Ref="43" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>21.4,0,1.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text19</Name>
              <Page isRef="8" />
              <Parent isRef="36" />
              <Text>{root_data_list.shiftName}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text19>
            <Text21 Ref="44" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>24.8,0,5.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text21</Name>
              <Page isRef="8" />
              <Parent isRef="36" />
              <Text>{root_data_list.remark}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text21>
            <Text22 Ref="45" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>12.8,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text22</Name>
              <Page isRef="8" />
              <Parent isRef="36" />
              <Text>{root_data_list.bizDate}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="46" type="DateFormat" isKey="true" />
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text22>
            <Text20 Ref="47" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.4,0,1.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text20</Name>
              <Page isRef="8" />
              <Parent isRef="36" />
              <Text>{root_data_list.guestName}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text20>
            <Text24 Ref="48" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>11.2,0,1.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="1">
                <value>TextBrush,SolidBrushValue_x0028__x0022__x0023_000000_x0022__x0029_</value>
              </Expressions>
              <Font>微软雅黑,10</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text24</Name>
              <Page isRef="8" />
              <Parent isRef="36" />
              <Text>{root_data_list.guestSrcTypeName}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text24>
            <Text26 Ref="49" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>23,0,1.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text26</Name>
              <Page isRef="8" />
              <Parent isRef="36" />
              <Text>{root_data_list.statChannelName}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text26>
            <文本12 Ref="50" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5.8,0,1.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Margins>5,0,0,0</Margins>
              <Name>文本12</Name>
              <Page isRef="8" />
              <Parent isRef="36" />
              <Text>{root_data_list.rNo}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本12>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>root_data_list</DataSourceName>
          <Expressions isList="true" count="0" />
          <Filters isList="true" count="0" />
          <Name>DataBand1</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
          <Sort isList="true" count="0" />
        </DataBand1>
        <GroupFooterBand1 Ref="51" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,9.6,30,0.8</ClientRectangle>
          <Components isList="true" count="9">
            <Text29 Ref="52" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9.2,0,2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>Text29</Name>
              <Page isRef="8" />
              <Parent isRef="51" />
              <Text>{Sum(root_data_list.fee)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text29>
            <Text30 Ref="53" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.2,0,4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,8</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text30</Name>
              <Page isRef="8" />
              <Parent isRef="51" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text30>
            <Text31 Ref="54" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>19.2,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,8</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text31</Name>
              <Page isRef="8" />
              <Parent isRef="51" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text31>
            <Text32 Ref="55" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>21.4,0,1.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,8</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text32</Name>
              <Page isRef="8" />
              <Parent isRef="51" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text32>
            <Text33 Ref="56" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>24.8,0,5.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,8</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text33</Name>
              <Page isRef="8" />
              <Parent isRef="51" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text33>
            <Text34 Ref="57" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>12.8,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,8</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text34</Name>
              <Page isRef="8" />
              <Parent isRef="51" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text34>
            <Text36 Ref="58" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>11.2,0,1.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,8</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text36</Name>
              <Page isRef="8" />
              <Parent isRef="51" />
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text36>
            <Text37 Ref="59" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>23,0,1.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,8</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text37</Name>
              <Page isRef="8" />
              <Parent isRef="51" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text37>
            <Text28 Ref="60" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,9.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text28</Name>
              <Page isRef="8" />
              <Parent isRef="51" />
              <Text>小计</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text28>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>GroupFooterBand1</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
        </GroupFooterBand1>
        <FooterBand1 Ref="61" type="FooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,11.2,30,0.8</ClientRectangle>
          <Components isList="true" count="9">
            <Text27 Ref="62" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9.2,0,2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>Text27</Name>
              <Page isRef="8" />
              <Parent isRef="61" />
              <Text>{Sum(root_data_list.fee)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text27>
            <Text35 Ref="63" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.2,0,4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,8</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text35</Name>
              <Page isRef="8" />
              <Parent isRef="61" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text35>
            <Text38 Ref="64" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>19.2,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,8</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text38</Name>
              <Page isRef="8" />
              <Parent isRef="61" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text38>
            <Text39 Ref="65" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>21.4,0,1.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,8</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text39</Name>
              <Page isRef="8" />
              <Parent isRef="61" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text39>
            <Text40 Ref="66" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>24.8,0,5.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,8</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text40</Name>
              <Page isRef="8" />
              <Parent isRef="61" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text40>
            <Text41 Ref="67" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>12.8,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,8</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text41</Name>
              <Page isRef="8" />
              <Parent isRef="61" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text41>
            <Text42 Ref="68" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>11.2,0,1.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,8</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text42</Name>
              <Page isRef="8" />
              <Parent isRef="61" />
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text42>
            <Text43 Ref="69" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>23,0,1.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,8</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text43</Name>
              <Page isRef="8" />
              <Parent isRef="61" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text43>
            <Text44 Ref="70" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,9.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text44</Name>
              <Page isRef="8" />
              <Parent isRef="61" />
              <Text>总计</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text44>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>FooterBand1</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
        </FooterBand1>
      </Components>
      <Conditions isList="true" count="0" />
      <Expressions isList="true" count="0" />
      <Guid>695b2a39df6d4d1082fa7197665400f1</Guid>
      <Margins>1,1,1,1</Margins>
      <Name>Page1</Name>
      <Orientation>Landscape</Orientation>
      <PageHeight>21</PageHeight>
      <PageWidth>32</PageWidth>
      <Report isRef="0" />
    </Page1>
  </Pages>
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>Report</ReportAlias>
  <ReportAuthor>Z zx</ReportAuthor>
  <ReportChanged>7/1/2025 2:45:13 PM</ReportChanged>
  <ReportCreated>7/5/2024 7:27:55 PM</ReportCreated>
  <ReportFile>D:\11\2\pms-report\public\reports\payReport.mrt</ReportFile>
  <ReportGuid>cab93480ae9942ea9d3e44ecc7076322</ReportGuid>
  <ReportName>Report</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2024.3.5.0</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
		#endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>