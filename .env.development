# 页面标题
VITE_APP_TITLE = 订单蚁来-酒店智能体
# 接口请求地址，会设置到 axios 的 baseURL 参数上
VITE_APP_API_BASEURL = http://*************:5000/
#VITE_APP_API_BASEURL = http://127.0.0.1:5000/
#VITE_APP_API_BASEURL = https://api.yiduohua.net/
# 前端链接前缀
VITE_BASE_PREFIX = /
# 体验账号和密码
VITE_APP_EXPERIENCE_ACCOUNT = ***********
VITE_APP_EXPERIENCE_PASSWORD = 123456
# 登录链接地址
VITE_APP_LOGIN_URL = http://localhost:9006/index.html#/login
# 返回酒店
VITE_APP_BACK_URL = http://localhost:9006/#/
# 调试工具，可设置 eruda 或 vconsole，如果不需要开启则留空
VITE_APP_DEBUG_TOOL =

# 是否开启代理房费日报表
VITE_OPEN_PROXY = true
