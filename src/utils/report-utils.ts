import useSettingsStore from '@/store/modules/settings.ts'

const Stimulsoft = window.Stimulsoft

export class ReportUtils {
  static mrtBasePath: string = `${import.meta.env.VITE_BASE_PREFIX}`
  static options: any = new Stimulsoft.Viewer.StiViewerOptions()

  static initOptions() {
    // 国际化
    Stimulsoft.Base.Localization.StiLocalization.setLocalizationFile(`${useSettingsStore().lang}.xml`, true)
    Stimulsoft.Base.StiLicense.loadFromFile(new URL('/src/assets/license.key', import.meta.url).href)

    // ----------------------------   配置    外观、工具栏按钮、导出功能等   ------------------------------------
    this.options.appearance.scrollbarsMode = false// 设置是否可以滚动
    // this.options.appearance.backgroundColor = Stimulsoft.System.Drawing.Color.dodgerBlue;//修改背景颜色
    this.options.appearance.showTooltips = false// 展示提示
    this.options.appearance.showTooltipsHelp = false// 展示帮助提示
    this.options.appearance.bookmarksTreeWidth = 0// 书签宽度
    this.options.toolbar.showPrintButton = true// 打印报表按钮
    this.options.toolbar.showDesignButton = false// 设计报表按钮
    this.options.toolbar.showAboutButton = false// stimulsoft report版本等信息展示按钮
    this.options.toolbar.showParametersButton = false
    this.options.toolbar.showEditorButton = true
    this.options.toolbar.showBookmarksButton = false // 书签按钮
    this.options.toolbar.showResourcesButton = false // 资源按钮
    this.options.toolbar.showOpenButton = false // 打开按钮

    this.options.exports.showExportToCsv = false
    this.options.exports.showExportToDocument = false
    this.options.exports.showExportToExcel2007 = true
    this.options.exports.showExportToHtml = false
    this.options.exports.showExportToHtml5 = false
    this.options.exports.showExportToImageSvg = true
    this.options.exports.showExportToJson = false
    this.options.exports.showExportToOpenDocumentCalc = false
    this.options.exports.showExportToOpenDocumentWriter = false
    this.options.exports.showExportToPdf = true
    this.options.exports.showExportToPowerPoint = false
    this.options.exports.showExportToText = false
    this.options.exports.showExportToWord2007 = true
    this.options.exports.showExportDialog = false// 是否显示导出对话框
    this.options.exports.storeExportSettings = true// 是否显示导出对话框
    this.options.toolbar.printDestination = Stimulsoft.Viewer.StiPrintDestination.Direct
  }

  static getViewer(viewerId: string): any {
    this.initOptions()

    // ----- 创建viewer 并将配置导入Viewer中 --------------
    return new Stimulsoft.Viewer.StiViewer(this.options, viewerId, false)
  }

  static getReportObj(fileName: string, data: any): any {
    const report = new Stimulsoft.Report.StiReport()
    report.loadFile(this.mrtBasePath + fileName)

    const dataSet = new Stimulsoft.System.Data.DataSet('JSON')
    dataSet.readJson(JSON.stringify(data))
    // 清除报表模板中的数据
    report.dictionary.databases.clear()
    // 注册数据集对象
    report.regData('JSON', 'JSON', dataSet)

    return report
  }

  static loadData(viewer: any, report: any, elementId: any) {
    report.renderAsync(() => {
      viewer.report = report
      const element = document.getElementById(elementId)
      if (!element) {
        return
      }
      viewer.renderHtml(elementId)
    })
  }
}
