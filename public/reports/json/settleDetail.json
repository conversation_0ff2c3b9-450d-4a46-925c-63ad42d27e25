{"code": 0, "data": {"hname": "订单蚁来酒店岳麓区店", "startDate": "2025-07-10", "endDate": "2025-07-10", "lastSelectTime": "2025-08-01 10:09:51", "operator": "***********", "list": [{"id": null, "gcode": "1864124623451594752", "hcode": "1864125215318220800", "orderNo": "1931519785938096129", "url": "/#/front/order/order_details?fullscreen=true&no=1931519785938096129&noType=order&modelValue=true&tabName=account", "togetherCode": "1931519785954873344", "name": "234234", "rtCode": "1925121211994083328", "rtName": "引流房型", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-06-08 09:12:58", "checkOutTime": "2025-07-10 14:13:22", "payTime": "2025-07-10 14:13:22", "payBizDate": "2025-07-10", "payType": "房间账", "consSubFee": 3200.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 3200.0, "paySubCash": 33295.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 0.0, "payTotalFee": 33295.0, "merge": -30095.0, "payOperator": "***********", "payOperatorName": "管理员", "payAccountList": null, "consumeAccountList": null, "rno": "99906", "gSrc": "walk_in", "gSrcName": "散客"}, {"id": null, "gcode": "1864124623451594752", "hcode": "1864125215318220800", "orderNo": "1889680675707576321", "url": "/#/front/order/order_details?fullscreen=true&no=1889680675707576321&noType=order&modelValue=true&tabName=account", "togetherCode": "1889680675724353536", "name": "张无忌", "rtCode": "1870467648542330880", "rtName": "几木双床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-02-12 22:19:16", "checkOutTime": "2025-07-10 14:13:22", "payTime": "2025-07-10 14:13:22", "payBizDate": "2025-07-10", "payType": "房间账", "consSubFee": 30095.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 30095.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 0.0, "payTotalFee": 0.0, "merge": 30095.0, "payOperator": "***********", "payOperatorName": "管理员", "payAccountList": null, "consumeAccountList": null, "rno": "6608", "gSrc": "member", "gSrcName": "会员"}, {"id": null, "gcode": "1864124623451594752", "hcode": "1864125215318220800", "orderNo": "1932380087290236929", "url": "/#/front/order/order_details?fullscreen=true&no=1932380087290236929&noType=order&modelValue=true&tabName=account", "togetherCode": "1932380087323791360", "name": "李密", "rtCode": "1881224727812939776", "rtName": "负离子大床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-06-10 18:11:29", "checkOutTime": "2025-07-10 09:39:54", "payTime": "2025-07-10 09:39:54", "payBizDate": "2025-07-10", "payType": "房间账", "consSubFee": 11400.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 11400.0, "paySubCash": 11400.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 0.0, "payTotalFee": 11400.0, "merge": 0.0, "payOperator": "yans<PERSON>zhanghao", "payOperatorName": "演示账号", "payAccountList": null, "consumeAccountList": null, "rno": "903", "gSrc": "walk_in", "gSrcName": "散客"}, {"id": null, "gcode": "1864124623451594752", "hcode": "1864125215318220800", "orderNo": "1943130552110063617", "url": "/#/front/order/order_details?fullscreen=true&no=1943130552110063617&noType=order&modelValue=true&tabName=account", "togetherCode": "1943130552139423744", "name": "李密", "rtCode": "1881224727812939776", "rtName": "负离子大床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-07-10 10:10:00", "checkOutTime": "2025-07-10 14:15:04", "payTime": "2025-07-10 14:15:04", "payBizDate": "2025-07-10", "payType": "房间账", "consSubFee": 0.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 0.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 0.0, "payTotalFee": 0.0, "merge": 0.0, "payOperator": "***********", "payOperatorName": "管理员", "payAccountList": null, "consumeAccountList": null, "rno": "903", "gSrc": "protocol", "gSrcName": "协议单位"}, {"id": null, "gcode": "1864124623451594752", "hcode": "1864125215318220800", "orderNo": "1929726851248951297", "url": "/#/front/order/order_details?fullscreen=true&no=1929726851248951297&noType=order&modelValue=true&tabName=account", "togetherCode": "1929754893358796800", "name": "李二傻", "rtCode": "1870467764661637120", "rtName": "高级双床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-06-03 12:19:54", "checkOutTime": "2025-07-10 14:12:11", "payTime": "2025-07-10 14:12:11", "payBizDate": "2025-07-10", "payType": "房间账", "consSubFee": 4070.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 4070.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 0.0, "payTotalFee": 0.0, "merge": 4070.0, "payOperator": "***********", "payOperatorName": "管理员", "payAccountList": null, "consumeAccountList": null, "rno": "F48", "gSrc": "walk_in", "gSrcName": "散客"}, {"id": null, "gcode": "1864124623451594752", "hcode": "1864125215318220800", "orderNo": "1938549007902826496", "url": "/#/front/order/order_details?fullscreen=true&no=1938549007902826496&noType=order&modelValue=true&tabName=account", "togetherCode": "1938549308613451776", "name": "6786888", "rtCode": "1870467412193300480", "rtName": "行政大床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-06-27 18:45:46", "checkOutTime": "2025-07-10 14:11:59", "payTime": "2025-07-10 14:11:59", "payBizDate": "2025-07-10", "payType": "房间账", "consSubFee": 6097.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 6097.0, "paySubCash": 6097.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 0.0, "payTotalFee": 6097.0, "merge": 0.0, "payOperator": "***********", "payOperatorName": "管理员", "payAccountList": null, "consumeAccountList": null, "rno": "C30", "gSrc": "walk_in", "gSrcName": "散客"}, {"id": null, "gcode": "1864124623451594752", "hcode": "1864125215318220800", "orderNo": "1931244210648420353", "url": "/#/front/order/order_details?fullscreen=true&no=1931244210648420353&noType=order&modelValue=true&tabName=account", "togetherCode": "1931244210665197568", "name": "李想", "rtCode": "1870467764661637120", "rtName": "高级双床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-06-07 14:57:55", "checkOutTime": "2025-07-10 11:30:42", "payTime": "2025-07-10 11:30:42", "payBizDate": "2025-07-10", "payType": "房间账", "consSubFee": 19437.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 19437.0, "paySubCash": 18879.0, "paySubDeposit": 558.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 0.0, "payTotalFee": 19437.0, "merge": 0.0, "payOperator": "yans<PERSON>zhanghao", "payOperatorName": "演示账号", "payAccountList": null, "consumeAccountList": null, "rno": "7709", "gSrc": "walk_in", "gSrcName": "散客"}, {"id": null, "gcode": "1864124623451594752", "hcode": "1864125215318220800", "orderNo": "1916451953202208769", "url": "/#/front/order/order_details?fullscreen=true&no=1916451953202208769&noType=order&modelValue=true&tabName=account", "togetherCode": "1916451953214791680", "name": "***********", "rtCode": "1881224727812939776", "rtName": "负离子大床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-04-27 19:18:46", "checkOutTime": "2025-07-10 14:11:37", "payTime": "2025-07-10 14:11:37", "payBizDate": "2025-07-10", "payType": "房间账", "consSubFee": 44252.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 44252.0, "paySubCash": 44252.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 0.0, "payTotalFee": 44252.0, "merge": 0.0, "payOperator": "***********", "payOperatorName": "管理员", "payAccountList": null, "consumeAccountList": null, "rno": "9802", "gSrc": "member", "gSrcName": "会员"}, {"id": null, "gcode": "1864124623451594752", "hcode": "1864125215318220800", "orderNo": "1906703320231751680", "url": "/#/front/order/order_details?fullscreen=true&no=1906703320231751680&noType=order&modelValue=true&tabName=account", "togetherCode": "1906703320248528896", "name": "2324", "rtCode": "1870467648542330880", "rtName": "几木双床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-03-31 21:41:11", "checkOutTime": "2025-07-10 15:28:44", "payTime": "2025-07-10 15:28:44", "payBizDate": "2025-07-10", "payType": "房间账", "consSubFee": 40198.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 40198.0, "paySubCash": 40198.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 0.0, "payTotalFee": 40198.0, "merge": 0.0, "payOperator": "yans<PERSON>zhanghao", "payOperatorName": "演示账号", "payAccountList": null, "consumeAccountList": null, "rno": "9910", "gSrc": "agent", "gSrcName": "中介"}, {"id": null, "gcode": "1864124623451594752", "hcode": "1864125215318220800", "orderNo": "1943151346705932289", "url": "/#/front/order/order_details?fullscreen=true&no=1943151346705932289&noType=order&modelValue=true&tabName=account", "togetherCode": "1943151346726903808", "name": "李想", "rtCode": "1881224727812939776", "rtName": "负离子大床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-07-10 11:32:38", "checkOutTime": "2025-07-10 14:15:04", "payTime": "2025-07-10 14:15:04", "payBizDate": "2025-07-10", "payType": "房间账", "consSubFee": 0.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 0.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 0.0, "payTotalFee": 0.0, "merge": 0.0, "payOperator": "***********", "payOperatorName": "管理员", "payAccountList": null, "consumeAccountList": null, "rno": "902", "gSrc": "walk_in", "gSrcName": "散客"}, {"id": null, "gcode": "1864124623451594752", "hcode": "1864125215318220800", "orderNo": "1929726851248951296", "url": "/#/front/order/order_details?fullscreen=true&no=1929726851248951296&noType=order&modelValue=true&tabName=account", "togetherCode": "1929754665369014272", "name": "李大傻", "rtCode": "1925121211994083328", "rtName": "引流房型", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-06-03 12:19:00", "checkOutTime": "2025-07-10 14:12:11", "payTime": "2025-07-10 14:12:11", "payBizDate": "2025-07-10", "payType": "房间账", "consSubFee": 3663.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 3663.0, "paySubCash": 7733.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 0.0, "payTotalFee": 7733.0, "merge": -4070.0, "payOperator": "***********", "payOperatorName": "管理员", "payAccountList": null, "consumeAccountList": null, "rno": "99905", "gSrc": "walk_in", "gSrcName": "散客"}, {"id": null, "gcode": "1864124623451594752", "hcode": "1864125215318220800", "orderNo": "1902540114248806401", "url": "/#/front/order/order_details?fullscreen=true&no=1902540114248806401&noType=order&modelValue=true&tabName=account", "togetherCode": "1902540114269777920", "name": "4234", "rtCode": "1881224727812939776", "rtName": "负离子大床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-03-20 09:58:05", "checkOutTime": "2025-07-10 14:41:20", "payTime": "2025-07-10 14:41:20", "payBizDate": "2025-07-10", "payType": "房间账", "consSubFee": 66578.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 1.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 123.0, "consumeTotalFee": 66702.0, "paySubCash": 11110.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 0.0, "payTotalFee": 11110.0, "merge": 55592.0, "payOperator": "***********", "payOperatorName": "管理员", "payAccountList": null, "consumeAccountList": null, "rno": "9812", "gSrc": "walk_in", "gSrcName": "散客"}, {"id": null, "gcode": "1864124623451594752", "hcode": "1864125215318220800", "orderNo": "1943133615659065345", "url": "/#/front/order/order_details?fullscreen=true&no=1943133615659065345&noType=order&modelValue=true&tabName=account", "togetherCode": "1943133615680036864", "name": "234234", "rtCode": "1870467764661637120", "rtName": "高级双床房", "inType": "hour_room", "inTypeName": "钟点房", "checkInTime": "2025-07-10 10:22:10", "checkOutTime": "2025-07-10 14:56:05", "payTime": "2025-07-10 14:56:05", "payBizDate": "2025-07-10", "payType": "房间账", "consSubFee": 0.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 0.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 0.0, "payTotalFee": 0.0, "merge": 0.0, "payOperator": "yans<PERSON>zhanghao", "payOperatorName": "演示账号", "payAccountList": null, "consumeAccountList": null, "rno": "7702", "gSrc": "walk_in", "gSrcName": "散客"}, {"id": null, "gcode": "1864124623451594752", "hcode": "1864125215318220800", "orderNo": "1901573862395183105", "url": "/#/front/order/order_details?fullscreen=true&no=1901573862395183105&noType=order&modelValue=true&tabName=account", "togetherCode": "1901573862416154624", "name": "23411", "rtCode": "1870467648542330880", "rtName": "几木双床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-03-17 17:58:33", "checkOutTime": "2025-07-10 14:11:52", "payTime": "2025-07-10 14:11:52", "payBizDate": "2025-07-10", "payType": "房间账", "consSubFee": 45770.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 99.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 45869.0, "paySubCash": 45869.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 0.0, "payTotalFee": 45869.0, "merge": 0.0, "payOperator": "***********", "payOperatorName": "管理员", "payAccountList": null, "consumeAccountList": null, "rno": "9902", "gSrc": "walk_in", "gSrcName": "散客"}, {"id": null, "gcode": "1864124623451594752", "hcode": "1864125215318220800", "orderNo": "1902540114248806401", "url": "/#/front/order/order_details?fullscreen=true&no=1902540114248806401&noType=order&modelValue=true&tabName=account", "togetherCode": "1902540187120644097", "name": "234234", "rtCode": "1881224727812939776", "rtName": "负离子大床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-03-20 09:58:23", "checkOutTime": "2025-07-10 14:41:20", "payTime": "2025-07-10 14:41:20", "payBizDate": "2025-07-10", "payType": "房间账", "consSubFee": 100.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 100.0, "paySubCash": 55692.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 0.0, "payTotalFee": 55692.0, "merge": -55592.0, "payOperator": "***********", "payOperatorName": "管理员", "payAccountList": null, "consumeAccountList": null, "rno": "9812", "gSrc": "walk_in", "gSrcName": "散客"}, {"id": null, "gcode": "1864124623451594752", "hcode": "1864125215318220800", "orderNo": "1943193298453233665", "url": "/#/front/order/order_details?fullscreen=true&no=1943193298453233665&noType=order&modelValue=true&tabName=account", "togetherCode": "1943193298461622272", "name": "11", "rtCode": "1870467764661637120", "rtName": "高级双床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-07-10 14:19:20", "checkOutTime": "2025-07-10 14:32:55", "payTime": "2025-07-10 14:32:55", "payBizDate": "2025-07-10", "payType": "房间账", "consSubFee": 0.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 0.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 0.0, "payTotalFee": 0.0, "merge": 0.0, "payOperator": "yans<PERSON>zhanghao", "payOperatorName": "演示账号", "payAccountList": null, "consumeAccountList": null, "rno": "7708", "gSrc": "walk_in", "gSrcName": "散客"}], "consumeNameList": null, "payNameList": null}, "msg": ""}