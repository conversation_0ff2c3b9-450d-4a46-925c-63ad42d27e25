<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <CalculationMode>Interpretation</CalculationMode>
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="1">
      <managerMonth Ref="2" type="Stimulsoft.Report.Dictionary.StiJsonDatabase" isKey="true">
        <Alias>managerMonth</Alias>
        <HeadersString />
        <Key />
        <Name>managerMonth</Name>
        <PathData>D:\11\2\pms-report\public\reports\json\managerMonth.json</PathData>
      </managerMonth>
    </Databases>
    <DataSources isList="true" count="19">
      <root Ref="3" type="DataTableSource" isKey="true">
        <Alias>root</Alias>
        <Columns isList="true" count="4">
          <value>code,System.Decimal</value>
          <value>data,System.String</value>
          <value>msg,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>6fc66c0623c3497296abf8d3e01f2f86</Key>
        <Name>root</Name>
        <NameInSource>managerMonth.root</NameInSource>
      </root>
      <root_data Ref="4" type="DataTableSource" isKey="true">
        <Alias>root_data</Alias>
        <Columns isList="true" count="8">
          <value>hname,System.String</value>
          <value>hcode,System.String</value>
          <value>startDate,System.String</value>
          <value>endDate,System.String</value>
          <value>lastSelectTime,System.String</value>
          <value>operator,System.String</value>
          <value>managerDailyMonthRespVO,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>740f24cdc35f4cccba6f8de2f99b9770</Key>
        <Name>root_data</Name>
        <NameInSource>managerMonth.root_data</NameInSource>
      </root_data>
      <root_data_managerDailyMonthRespVO Ref="5" type="DataTableSource" isKey="true">
        <Alias>root_data_managerDailyMonthRespVO</Alias>
        <Columns isList="true" count="12">
          <value>roomRevenueIndex,System.String</value>
          <value>orderSrcList,System.String</value>
          <value>orderSrcNameList,System.String</value>
          <value>rtCodeList,System.String</value>
          <value>rtCodeNameList,System.String</value>
          <value>gsrcList,System.String</value>
          <value>gsrcNameList,System.String</value>
          <value>statChannelList,System.String</value>
          <value>statChannelNameList,System.String</value>
          <value>inTypeList,System.String</value>
          <value>inTypeNameList,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>374c2ce11b7941ab9ec6b6c1582d63e1</Key>
        <Name>root_data_managerDailyMonthRespVO</Name>
        <NameInSource>managerMonth.root_data_managerDailyMonthRespVO</NameInSource>
      </root_data_managerDailyMonthRespVO>
      <root_data_managerDailyMonthRespVO_gsrcList Ref="6" type="DataTableSource" isKey="true">
        <Alias>root_data_managerDailyMonthRespVO_gsrcList</Alias>
        <Columns isList="true" count="5">
          <value>bizDate,System.String</value>
          <value>week,System.String</value>
          <value>cateGory,System.String</value>
          <value>data,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>44178847203b4a9f874812da051f7d8a</Key>
        <Name>root_data_managerDailyMonthRespVO_gsrcList</Name>
        <NameInSource>managerMonth.root_data_managerDailyMonthRespVO_gsrcList</NameInSource>
      </root_data_managerDailyMonthRespVO_gsrcList>
      <root_data_managerDailyMonthRespVO_gsrcList_data Ref="7" type="DataTableSource" isKey="true">
        <Alias>root_data_managerDailyMonthRespVO_gsrcList_data</Alias>
        <Columns isList="true" count="5">
          <value>classificationStatistics,System.String</value>
          <value>nightNum,System.Decimal</value>
          <value>occ,System.Decimal</value>
          <value>totalFee,System.Decimal</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>40cae98463da4a8ba280ed93ad699b67</Key>
        <Name>root_data_managerDailyMonthRespVO_gsrcList_data</Name>
        <NameInSource>managerMonth.root_data_managerDailyMonthRespVO_gsrcList_data</NameInSource>
      </root_data_managerDailyMonthRespVO_gsrcList_data>
      <root_data_managerDailyMonthRespVO_gsrcNameList Ref="8" type="DataTableSource" isKey="true">
        <Alias>root_data_managerDailyMonthRespVO_gsrcNameList</Alias>
        <Columns isList="true" count="5">
          <value>name,System.String</value>
          <value>nightNumSum,System.Decimal</value>
          <value>avgOcc,System.String</value>
          <value>totalFeeSum,System.Decimal</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>2fd06cc46c70422e9c0dcbf8b652ae87</Key>
        <Name>root_data_managerDailyMonthRespVO_gsrcNameList</Name>
        <NameInSource>managerMonth.root_data_managerDailyMonthRespVO_gsrcNameList</NameInSource>
      </root_data_managerDailyMonthRespVO_gsrcNameList>
      <root_data_managerDailyMonthRespVO_inTypeList Ref="9" type="DataTableSource" isKey="true">
        <Alias>root_data_managerDailyMonthRespVO_inTypeList</Alias>
        <Columns isList="true" count="5">
          <value>bizDate,System.String</value>
          <value>week,System.String</value>
          <value>cateGory,System.String</value>
          <value>data,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>e376b2efaf56404bb09ca594e09075a2</Key>
        <Name>root_data_managerDailyMonthRespVO_inTypeList</Name>
        <NameInSource>managerMonth.root_data_managerDailyMonthRespVO_inTypeList</NameInSource>
      </root_data_managerDailyMonthRespVO_inTypeList>
      <root_data_managerDailyMonthRespVO_inTypeList_data Ref="10" type="DataTableSource" isKey="true">
        <Alias>root_data_managerDailyMonthRespVO_inTypeList_data</Alias>
        <Columns isList="true" count="5">
          <value>classificationStatistics,System.String</value>
          <value>nightNum,System.Decimal</value>
          <value>occ,System.Decimal</value>
          <value>totalFee,System.Decimal</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>4168bad13c68479b979d8bdc81918fcf</Key>
        <Name>root_data_managerDailyMonthRespVO_inTypeList_data</Name>
        <NameInSource>managerMonth.root_data_managerDailyMonthRespVO_inTypeList_data</NameInSource>
      </root_data_managerDailyMonthRespVO_inTypeList_data>
      <root_data_managerDailyMonthRespVO_inTypeNameList Ref="11" type="DataTableSource" isKey="true">
        <Alias>root_data_managerDailyMonthRespVO_inTypeNameList</Alias>
        <Columns isList="true" count="5">
          <value>name,System.String</value>
          <value>nightNumSum,System.Decimal</value>
          <value>avgOcc,System.String</value>
          <value>totalFeeSum,System.Decimal</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>50d8cc897faa42ce932a7d11524a5699</Key>
        <Name>root_data_managerDailyMonthRespVO_inTypeNameList</Name>
        <NameInSource>managerMonth.root_data_managerDailyMonthRespVO_inTypeNameList</NameInSource>
      </root_data_managerDailyMonthRespVO_inTypeNameList>
      <root_data_managerDailyMonthRespVO_orderSrcList Ref="12" type="DataTableSource" isKey="true">
        <Alias>root_data_managerDailyMonthRespVO_orderSrcList</Alias>
        <Columns isList="true" count="5">
          <value>bizDate,System.String</value>
          <value>week,System.String</value>
          <value>cateGory,System.String</value>
          <value>data,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>3bea676180cd446abcd08d92b86e09bb</Key>
        <Name>root_data_managerDailyMonthRespVO_orderSrcList</Name>
        <NameInSource>managerMonth.root_data_managerDailyMonthRespVO_orderSrcList</NameInSource>
      </root_data_managerDailyMonthRespVO_orderSrcList>
      <root_data_managerDailyMonthRespVO_orderSrcList_data Ref="13" type="DataTableSource" isKey="true">
        <Alias>root_data_managerDailyMonthRespVO_orderSrcList_data</Alias>
        <Columns isList="true" count="5">
          <value>classificationStatistics,System.String</value>
          <value>nightNum,System.Decimal</value>
          <value>occ,System.Decimal</value>
          <value>totalFee,System.Decimal</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>0b24feb089dc498db380e7dd4fc29290</Key>
        <Name>root_data_managerDailyMonthRespVO_orderSrcList_data</Name>
        <NameInSource>managerMonth.root_data_managerDailyMonthRespVO_orderSrcList_data</NameInSource>
      </root_data_managerDailyMonthRespVO_orderSrcList_data>
      <root_data_managerDailyMonthRespVO_orderSrcNameList Ref="14" type="DataTableSource" isKey="true">
        <Alias>root_data_managerDailyMonthRespVO_orderSrcNameList</Alias>
        <Columns isList="true" count="5">
          <value>name,System.String</value>
          <value>nightNumSum,System.Decimal</value>
          <value>avgOcc,System.String</value>
          <value>totalFeeSum,System.Decimal</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>e9307d127795465b97f7369bf05b1525</Key>
        <Name>root_data_managerDailyMonthRespVO_orderSrcNameList</Name>
        <NameInSource>managerMonth.root_data_managerDailyMonthRespVO_orderSrcNameList</NameInSource>
      </root_data_managerDailyMonthRespVO_orderSrcNameList>
      <root_data_managerDailyMonthRespVO_roomRevenueIndex Ref="15" type="DataTableSource" isKey="true">
        <Alias>root_data_managerDailyMonthRespVO_roomRevenueIndex</Alias>
        <Columns isList="true" count="35">
          <value>id,System.Decimal</value>
          <value>gcode,System.String</value>
          <value>hcode,System.String</value>
          <value>nightNum,System.Decimal</value>
          <value>roomFee,System.Decimal</value>
          <value>revPar,System.Decimal</value>
          <value>avgRoomFee,System.Decimal</value>
          <value>occ,System.Decimal</value>
          <value>totalRoomNum,System.Decimal</value>
          <value>emptyNum,System.Decimal</value>
          <value>repairNum,System.Decimal</value>
          <value>selfNum,System.Decimal</value>
          <value>overnightNum,System.Decimal</value>
          <value>overnightOcc,System.Decimal</value>
          <value>openRoomNum,System.Decimal</value>
          <value>workInNum,System.Decimal</value>
          <value>bookInNum,System.Decimal</value>
          <value>cancelBookNum,System.Decimal</value>
          <value>noShowNum,System.Decimal</value>
          <value>memberCardSellNum,System.Decimal</value>
          <value>creditNum,System.Decimal</value>
          <value>freeUpNum,System.Decimal</value>
          <value>memberCardFee,System.String</value>
          <value>bizDate,System.String</value>
          <value>week,System.String</value>
          <value>memberCard,System.Decimal</value>
          <value>good,System.Decimal</value>
          <value>catering,System.Decimal</value>
          <value>indemnityFee,System.Decimal</value>
          <value>couponDeduction,System.Decimal</value>
          <value>otherFee,System.Decimal</value>
          <value>lobbyFee,System.Decimal</value>
          <value>memberRecharge,System.Decimal</value>
          <value>createTime,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>60d699d2ff134d79904f1da0cc2bab15</Key>
        <Name>root_data_managerDailyMonthRespVO_roomRevenueIndex</Name>
        <NameInSource>managerMonth.root_data_managerDailyMonthRespVO_roomRevenueIndex</NameInSource>
      </root_data_managerDailyMonthRespVO_roomRevenueIndex>
      <root_data_managerDailyMonthRespVO_rtCodeList Ref="16" type="DataTableSource" isKey="true">
        <Alias>root_data_managerDailyMonthRespVO_rtCodeList</Alias>
        <Columns isList="true" count="5">
          <value>bizDate,System.String</value>
          <value>week,System.String</value>
          <value>cateGory,System.String</value>
          <value>data,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>69a7eacac4db47569b9767d9a008aa15</Key>
        <Name>root_data_managerDailyMonthRespVO_rtCodeList</Name>
        <NameInSource>managerMonth.root_data_managerDailyMonthRespVO_rtCodeList</NameInSource>
      </root_data_managerDailyMonthRespVO_rtCodeList>
      <root_data_managerDailyMonthRespVO_rtCodeList_data Ref="17" type="DataTableSource" isKey="true">
        <Alias>root_data_managerDailyMonthRespVO_rtCodeList_data</Alias>
        <Columns isList="true" count="5">
          <value>classificationStatistics,System.String</value>
          <value>nightNum,System.Decimal</value>
          <value>occ,System.Decimal</value>
          <value>totalFee,System.Decimal</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>fd0ddd11efe04fb78ba1b9d192aeb89a</Key>
        <Name>root_data_managerDailyMonthRespVO_rtCodeList_data</Name>
        <NameInSource>managerMonth.root_data_managerDailyMonthRespVO_rtCodeList_data</NameInSource>
      </root_data_managerDailyMonthRespVO_rtCodeList_data>
      <root_data_managerDailyMonthRespVO_rtCodeNameList Ref="18" type="DataTableSource" isKey="true">
        <Alias>root_data_managerDailyMonthRespVO_rtCodeNameList</Alias>
        <Columns isList="true" count="5">
          <value>name,System.String</value>
          <value>nightNumSum,System.Decimal</value>
          <value>avgOcc,System.String</value>
          <value>totalFeeSum,System.Decimal</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>c59e55324e274bb3bbb9a1040621f4b6</Key>
        <Name>root_data_managerDailyMonthRespVO_rtCodeNameList</Name>
        <NameInSource>managerMonth.root_data_managerDailyMonthRespVO_rtCodeNameList</NameInSource>
      </root_data_managerDailyMonthRespVO_rtCodeNameList>
      <root_data_managerDailyMonthRespVO_statChannelList Ref="19" type="DataTableSource" isKey="true">
        <Alias>root_data_managerDailyMonthRespVO_statChannelList</Alias>
        <Columns isList="true" count="5">
          <value>bizDate,System.String</value>
          <value>week,System.String</value>
          <value>cateGory,System.String</value>
          <value>data,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>0e99e8cc8e13432da6209516fc19106d</Key>
        <Name>root_data_managerDailyMonthRespVO_statChannelList</Name>
        <NameInSource>managerMonth.root_data_managerDailyMonthRespVO_statChannelList</NameInSource>
      </root_data_managerDailyMonthRespVO_statChannelList>
      <root_data_managerDailyMonthRespVO_statChannelList_data Ref="20" type="DataTableSource" isKey="true">
        <Alias>root_data_managerDailyMonthRespVO_statChannelList_data</Alias>
        <Columns isList="true" count="5">
          <value>classificationStatistics,System.String</value>
          <value>nightNum,System.Decimal</value>
          <value>occ,System.Decimal</value>
          <value>totalFee,System.Decimal</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>183c0d17ee7a4cdca3be415bd54fbdb1</Key>
        <Name>root_data_managerDailyMonthRespVO_statChannelList_data</Name>
        <NameInSource>managerMonth.root_data_managerDailyMonthRespVO_statChannelList_data</NameInSource>
      </root_data_managerDailyMonthRespVO_statChannelList_data>
      <root_data_managerDailyMonthRespVO_statChannelNameList Ref="21" type="DataTableSource" isKey="true">
        <Alias>root_data_managerDailyMonthRespVO_statChannelNameList</Alias>
        <Columns isList="true" count="5">
          <value>name,System.String</value>
          <value>nightNumSum,System.Decimal</value>
          <value>avgOcc,System.String</value>
          <value>totalFeeSum,System.Decimal</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>29aa6df143bc47debf096258ee71f979</Key>
        <Name>root_data_managerDailyMonthRespVO_statChannelNameList</Name>
        <NameInSource>managerMonth.root_data_managerDailyMonthRespVO_statChannelNameList</NameInSource>
      </root_data_managerDailyMonthRespVO_statChannelNameList>
    </DataSources>
    <Relations isList="true" count="18">
      <root Ref="22" type="DataRelation" isKey="true">
        <Alias>root</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="4" />
        <Dictionary isRef="1" />
        <Name>root</Name>
        <NameInSource>root_data</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>data</value>
        </ParentColumns>
        <ParentSource isRef="3" />
      </root>
      <root_data Ref="23" type="DataRelation" isKey="true">
        <Alias>root_data</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="5" />
        <Dictionary isRef="1" />
        <Name>root_data</Name>
        <NameInSource>root_data_managerDailyMonthRespVO</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>managerDailyMonthRespVO</value>
        </ParentColumns>
        <ParentSource isRef="4" />
      </root_data>
      <root_data_managerDailyMonthRespVO Ref="24" type="DataRelation" isKey="true">
        <Alias>root_data_managerDailyMonthRespVO</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="6" />
        <Dictionary isRef="1" />
        <Name>root_data_managerDailyMonthRespVO</Name>
        <NameInSource>root_data_managerDailyMonthRespVO_gsrcList</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>gsrcList</value>
        </ParentColumns>
        <ParentSource isRef="5" />
      </root_data_managerDailyMonthRespVO>
      <root_data_managerDailyMonthRespVO_gsrcList Ref="25" type="DataRelation" isKey="true">
        <Alias>root_data_managerDailyMonthRespVO_gsrcList</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="7" />
        <Dictionary isRef="1" />
        <Name>root_data_managerDailyMonthRespVO_gsrcList</Name>
        <NameInSource>root_data_managerDailyMonthRespVO_gsrcList_data</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>data</value>
        </ParentColumns>
        <ParentSource isRef="6" />
      </root_data_managerDailyMonthRespVO_gsrcList>
      <root_data_managerDailyMonthRespVO Ref="26" type="DataRelation" isKey="true">
        <Alias>root_data_managerDailyMonthRespVO</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="8" />
        <Dictionary isRef="1" />
        <Name>root_data_managerDailyMonthRespVO</Name>
        <NameInSource>root_data_managerDailyMonthRespVO_gsrcNameList</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>gsrcNameList</value>
        </ParentColumns>
        <ParentSource isRef="5" />
      </root_data_managerDailyMonthRespVO>
      <root_data_managerDailyMonthRespVO Ref="27" type="DataRelation" isKey="true">
        <Alias>root_data_managerDailyMonthRespVO</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="9" />
        <Dictionary isRef="1" />
        <Name>root_data_managerDailyMonthRespVO</Name>
        <NameInSource>root_data_managerDailyMonthRespVO_inTypeList</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>inTypeList</value>
        </ParentColumns>
        <ParentSource isRef="5" />
      </root_data_managerDailyMonthRespVO>
      <root_data_managerDailyMonthRespVO_inTypeList Ref="28" type="DataRelation" isKey="true">
        <Alias>root_data_managerDailyMonthRespVO_inTypeList</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="10" />
        <Dictionary isRef="1" />
        <Name>root_data_managerDailyMonthRespVO_inTypeList</Name>
        <NameInSource>root_data_managerDailyMonthRespVO_inTypeList_data</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>data</value>
        </ParentColumns>
        <ParentSource isRef="9" />
      </root_data_managerDailyMonthRespVO_inTypeList>
      <root_data_managerDailyMonthRespVO Ref="29" type="DataRelation" isKey="true">
        <Alias>root_data_managerDailyMonthRespVO</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="11" />
        <Dictionary isRef="1" />
        <Name>root_data_managerDailyMonthRespVO</Name>
        <NameInSource>root_data_managerDailyMonthRespVO_inTypeNameList</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>inTypeNameList</value>
        </ParentColumns>
        <ParentSource isRef="5" />
      </root_data_managerDailyMonthRespVO>
      <root_data_managerDailyMonthRespVO Ref="30" type="DataRelation" isKey="true">
        <Alias>root_data_managerDailyMonthRespVO</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="12" />
        <Dictionary isRef="1" />
        <Name>root_data_managerDailyMonthRespVO</Name>
        <NameInSource>root_data_managerDailyMonthRespVO_orderSrcList</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>orderSrcList</value>
        </ParentColumns>
        <ParentSource isRef="5" />
      </root_data_managerDailyMonthRespVO>
      <root_data_managerDailyMonthRespVO_orderSrcList Ref="31" type="DataRelation" isKey="true">
        <Alias>root_data_managerDailyMonthRespVO_orderSrcList</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="13" />
        <Dictionary isRef="1" />
        <Name>root_data_managerDailyMonthRespVO_orderSrcList</Name>
        <NameInSource>root_data_managerDailyMonthRespVO_orderSrcList_data</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>data</value>
        </ParentColumns>
        <ParentSource isRef="12" />
      </root_data_managerDailyMonthRespVO_orderSrcList>
      <root_data_managerDailyMonthRespVO Ref="32" type="DataRelation" isKey="true">
        <Alias>root_data_managerDailyMonthRespVO</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="14" />
        <Dictionary isRef="1" />
        <Name>root_data_managerDailyMonthRespVO</Name>
        <NameInSource>root_data_managerDailyMonthRespVO_orderSrcNameList</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>orderSrcNameList</value>
        </ParentColumns>
        <ParentSource isRef="5" />
      </root_data_managerDailyMonthRespVO>
      <root_data_managerDailyMonthRespVO Ref="33" type="DataRelation" isKey="true">
        <Alias>root_data_managerDailyMonthRespVO</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="15" />
        <Dictionary isRef="1" />
        <Name>root_data_managerDailyMonthRespVO</Name>
        <NameInSource>root_data_managerDailyMonthRespVO_roomRevenueIndex</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>roomRevenueIndex</value>
        </ParentColumns>
        <ParentSource isRef="5" />
      </root_data_managerDailyMonthRespVO>
      <root_data_managerDailyMonthRespVO Ref="34" type="DataRelation" isKey="true">
        <Alias>root_data_managerDailyMonthRespVO</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="16" />
        <Dictionary isRef="1" />
        <Name>root_data_managerDailyMonthRespVO</Name>
        <NameInSource>root_data_managerDailyMonthRespVO_rtCodeList</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>rtCodeList</value>
        </ParentColumns>
        <ParentSource isRef="5" />
      </root_data_managerDailyMonthRespVO>
      <root_data_managerDailyMonthRespVO_rtCodeList Ref="35" type="DataRelation" isKey="true">
        <Alias>root_data_managerDailyMonthRespVO_rtCodeList</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="17" />
        <Dictionary isRef="1" />
        <Name>root_data_managerDailyMonthRespVO_rtCodeList</Name>
        <NameInSource>root_data_managerDailyMonthRespVO_rtCodeList_data</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>data</value>
        </ParentColumns>
        <ParentSource isRef="16" />
      </root_data_managerDailyMonthRespVO_rtCodeList>
      <root_data_managerDailyMonthRespVO Ref="36" type="DataRelation" isKey="true">
        <Alias>root_data_managerDailyMonthRespVO</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="18" />
        <Dictionary isRef="1" />
        <Name>root_data_managerDailyMonthRespVO</Name>
        <NameInSource>root_data_managerDailyMonthRespVO_rtCodeNameList</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>rtCodeNameList</value>
        </ParentColumns>
        <ParentSource isRef="5" />
      </root_data_managerDailyMonthRespVO>
      <root_data_managerDailyMonthRespVO Ref="37" type="DataRelation" isKey="true">
        <Alias>root_data_managerDailyMonthRespVO</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="19" />
        <Dictionary isRef="1" />
        <Name>root_data_managerDailyMonthRespVO</Name>
        <NameInSource>root_data_managerDailyMonthRespVO_statChannelList</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>statChannelList</value>
        </ParentColumns>
        <ParentSource isRef="5" />
      </root_data_managerDailyMonthRespVO>
      <root_data_managerDailyMonthRespVO_statChannelList Ref="38" type="DataRelation" isKey="true">
        <Alias>root_data_managerDailyMonthRespVO_statChannelList</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="20" />
        <Dictionary isRef="1" />
        <Name>root_data_managerDailyMonthRespVO_statChannelList</Name>
        <NameInSource>root_data_managerDailyMonthRespVO_statChannelList_data</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>data</value>
        </ParentColumns>
        <ParentSource isRef="19" />
      </root_data_managerDailyMonthRespVO_statChannelList>
      <root_data_managerDailyMonthRespVO Ref="39" type="DataRelation" isKey="true">
        <Alias>root_data_managerDailyMonthRespVO</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="21" />
        <Dictionary isRef="1" />
        <Name>root_data_managerDailyMonthRespVO</Name>
        <NameInSource>root_data_managerDailyMonthRespVO_statChannelNameList</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>statChannelNameList</value>
        </ParentColumns>
        <ParentSource isRef="5" />
      </root_data_managerDailyMonthRespVO>
    </Relations>
    <Report isRef="0" />
    <Resources isList="true" count="0" />
    <UserFunctions isList="true" count="0" />
    <Variables isList="true" count="1">
      <value>,TotalRoomNumSum,TotalRoomNumSum,,System.Int64,_x0030_,False,False,False,False,,_x0035_8c239d9181b4897b823b90deeb69aaa</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <Key>1881a2cd7f6245c9bf9dcf31a00c71e9</Key>
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="40" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="25">
        <PageHeaderBand1 Ref="41" type="PageHeaderBand" isKey="true">
          <Border>Bottom;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,51,0.8</ClientRectangle>
          <Components isList="true" count="2">
            <Text2 Ref="42" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0.01,0,7,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Name>Text2</Name>
              <Page isRef="40" />
              <Parent isRef="41" />
              <Text>{root_data.hname}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text2>
            <Text6 Ref="43" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>43.4,0,5.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Name>Text6</Name>
              <Page isRef="40" />
              <Parent isRef="41" />
              <Text>第{PageNumber}页,共{TotalPageCount}页</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text6>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>PageHeaderBand1</Name>
          <Page isRef="40" />
          <Parent isRef="40" />
        </PageHeaderBand1>
        <报表标题区1 Ref="44" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,2,51,1.4</ClientRectangle>
          <Components isList="true" count="2">
            <Text1 Ref="45" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>21.6,0,5.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,12,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text1</Name>
              <Page isRef="40" />
              <Parent isRef="44" />
              <Text>经理综合月报表</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text1>
            <Text5 Ref="46" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.8,24,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Name>Text5</Name>
              <Page isRef="40" />
              <Parent isRef="44" />
              <Text>门店:{root_data.hname}   营业日:{root_data.startDate} - {root_data.endDate}  最后查询时间：{root_data.lastSelectTime}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text5>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>报表标题区1</Name>
          <Page isRef="40" />
          <Parent isRef="40" />
        </报表标题区1>
        <页眉1 Ref="47" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,4.2,51,0.8</ClientRectangle>
          <Components isList="true" count="20">
            <Text3 Ref="48" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text3</Name>
              <Page isRef="40" />
              <Parent isRef="47" />
              <Text>营业日</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text3>
            <Text4 Ref="49" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.4,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text4</Name>
              <Page isRef="40" />
              <Parent isRef="47" />
              <Text>星期</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text4>
            <文本1 Ref="50" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.6,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本1</Name>
              <Page isRef="40" />
              <Parent isRef="47" />
              <Text>客房数</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本1>
            <文本2 Ref="51" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6.8,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本2</Name>
              <Page isRef="40" />
              <Parent isRef="47" />
              <Text>间夜数</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本2>
            <文本4 Ref="52" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本4</Name>
              <Page isRef="40" />
              <Parent isRef="47" />
              <Text>自用房</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本4>
            <文本5 Ref="53" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>11.2,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本5</Name>
              <Page isRef="40" />
              <Parent isRef="47" />
              <Text>维修房</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本5>
            <文本6 Ref="54" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.4,0,2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本6</Name>
              <Page isRef="40" />
              <Parent isRef="47" />
              <Text>过夜房</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本6>
            <文本7 Ref="55" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.4,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Interaction Ref="56" type="Stimulsoft.Report.Components.StiInteraction" isKey="true">
                <SortingColumn>数据区1.roomFee</SortingColumn>
              </Interaction>
              <Name>文本7</Name>
              <Page isRef="40" />
              <Parent isRef="47" />
              <Text>房费</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本7>
            <文本8 Ref="57" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>20.2,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Interaction Ref="58" type="Stimulsoft.Report.Components.StiInteraction" isKey="true">
                <SortingColumn>数据区1.avgRoomFee</SortingColumn>
              </Interaction>
              <Name>文本8</Name>
              <Page isRef="40" />
              <Parent isRef="47" />
              <Text>平均房价</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本8>
            <文本9 Ref="59" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>22.4,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本9</Name>
              <Page isRef="40" />
              <Parent isRef="47" />
              <Text>RevPar</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本9>
            <文本10 Ref="60" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>24.6,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本10</Name>
              <Page isRef="40" />
              <Parent isRef="47" />
              <Text>出租率</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本10>
            <文本11 Ref="61" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>26.8,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本11</Name>
              <Page isRef="40" />
              <Parent isRef="47" />
              <Text>过夜出租率</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本11>
            <文本12 Ref="62" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>29.2,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Interaction Ref="63" type="Stimulsoft.Report.Components.StiInteraction" isKey="true">
                <SortingColumn>数据区1.good</SortingColumn>
              </Interaction>
              <Name>文本12</Name>
              <Page isRef="40" />
              <Parent isRef="47" />
              <Text>小商品</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本12>
            <文本13 Ref="64" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>31.6,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Interaction Ref="65" type="Stimulsoft.Report.Components.StiInteraction" isKey="true">
                <SortingColumn>数据区1.memberCard</SortingColumn>
              </Interaction>
              <Name>文本13</Name>
              <Page isRef="40" />
              <Parent isRef="47" />
              <Text>会员卡</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本13>
            <文本14 Ref="66" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>34,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Interaction Ref="67" type="Stimulsoft.Report.Components.StiInteraction" isKey="true">
                <SortingColumn>数据区1.catering</SortingColumn>
              </Interaction>
              <Name>文本14</Name>
              <Page isRef="40" />
              <Parent isRef="47" />
              <Text>餐饮</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本14>
            <文本15 Ref="68" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>36.2,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Interaction Ref="69" type="Stimulsoft.Report.Components.StiInteraction" isKey="true">
                <SortingColumn>数据区1.otherFee</SortingColumn>
              </Interaction>
              <Name>文本15</Name>
              <Page isRef="40" />
              <Parent isRef="47" />
              <Text>其他消费</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本15>
            <文本16 Ref="70" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>38.4,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Interaction Ref="71" type="Stimulsoft.Report.Components.StiInteraction" isKey="true">
                <SortingColumn>数据区1.indemnityFee</SortingColumn>
              </Interaction>
              <Name>文本16</Name>
              <Page isRef="40" />
              <Parent isRef="47" />
              <Text>赔偿费</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本16>
            <文本17 Ref="72" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>40.6,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Interaction Ref="73" type="Stimulsoft.Report.Components.StiInteraction" isKey="true">
                <SortingColumn>数据区1.lobbyFee</SortingColumn>
              </Interaction>
              <Name>文本17</Name>
              <Page isRef="40" />
              <Parent isRef="47" />
              <Text>门店收入</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本17>
            <文本18 Ref="74" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>42.8,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Interaction Ref="75" type="Stimulsoft.Report.Components.StiInteraction" isKey="true">
                <SortingColumn>数据区1.memberRecharge</SortingColumn>
              </Interaction>
              <Name>文本18</Name>
              <Page isRef="40" />
              <Parent isRef="47" />
              <Text>会员充值</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本18>
            <文本151 Ref="76" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>17.8,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Interaction Ref="77" type="Stimulsoft.Report.Components.StiInteraction" isKey="true">
                <SortingColumn>数据区1.couponDeduction</SortingColumn>
              </Interaction>
              <Name>文本151</Name>
              <Page isRef="40" />
              <Parent isRef="47" />
              <Text>优惠卷抵扣</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本151>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>页眉1</Name>
          <Page isRef="40" />
          <Parent isRef="40" />
        </页眉1>
        <数据区1 Ref="78" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,5.8,51,0.8</ClientRectangle>
          <Components isList="true" count="20">
            <文本20 Ref="79" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本20</Name>
              <Page isRef="40" />
              <Parent isRef="78" />
              <Text>{root_data_managerDailyMonthRespVO_roomRevenueIndex.bizDate}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="80" type="DateFormat" isKey="true" />
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本20>
            <文本21 Ref="81" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.4,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>文本21</Name>
              <Page isRef="40" />
              <Parent isRef="78" />
              <Text>{root_data_managerDailyMonthRespVO_roomRevenueIndex.week}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本21>
            <文本22 Ref="82" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.6,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <Margins>9,0,0,0</Margins>
              <Name>文本22</Name>
              <Page isRef="40" />
              <Parent isRef="78" />
              <Text>{root_data_managerDailyMonthRespVO_roomRevenueIndex.totalRoomNum}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本22>
            <文本23 Ref="83" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6.8,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <Hyperlink>#/?name=StoreDailyReviewRoomRate&amp;hcode={root_data.hcode}&amp;startDate={root_data_managerDailyMonthRespVO_roomRevenueIndex.bizDate}&amp;endDate={root_data_managerDailyMonthRespVO_roomRevenueIndex.bizDate}</Hyperlink>
              <Margins>9,0,0,0</Margins>
              <Name>文本23</Name>
              <Page isRef="40" />
              <Parent isRef="78" />
              <Text>{root_data_managerDailyMonthRespVO_roomRevenueIndex.nightNum}</Text>
              <TextBrush>[0:176:240]</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本23>
            <文本25 Ref="84" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <Margins>9,0,0,0</Margins>
              <Name>文本25</Name>
              <Page isRef="40" />
              <Parent isRef="78" />
              <Text>{root_data_managerDailyMonthRespVO_roomRevenueIndex.selfNum}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本25>
            <文本26 Ref="85" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>11.2,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <Margins>9,0,0,0</Margins>
              <Name>文本26</Name>
              <Page isRef="40" />
              <Parent isRef="78" />
              <Text>{root_data_managerDailyMonthRespVO_roomRevenueIndex.repairNum}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本26>
            <文本27 Ref="86" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.4,0,2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <Margins>9,0,0,0</Margins>
              <Name>文本27</Name>
              <Page isRef="40" />
              <Parent isRef="78" />
              <Text>{root_data_managerDailyMonthRespVO_roomRevenueIndex.overnightNum}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本27>
            <文本28 Ref="87" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.4,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Right</HorAlignment>
              <Hyperlink>#/?name=StoreDailyReviewRoomRate&amp;hcode={root_data.hcode}&amp;startDate={root_data_managerDailyMonthRespVO_roomRevenueIndex.bizDate}&amp;endDate={root_data_managerDailyMonthRespVO_roomRevenueIndex.bizDate}</Hyperlink>
              <Margins>0,5,0,0</Margins>
              <Name>文本28</Name>
              <Page isRef="40" />
              <Parent isRef="78" />
              <Text>{root_data_managerDailyMonthRespVO_roomRevenueIndex.roomFee}</Text>
              <TextBrush>[0:176:240]</TextBrush>
              <TextFormat Ref="88" type="CurrencyFormat" isKey="true">
                <DecimalDigits>2</DecimalDigits>
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>2</NegativePattern>
                <PositivePattern>0</PositivePattern>
                <Symbol>¥</Symbol>
              </TextFormat>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本28>
            <文本29 Ref="89" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>20.2,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本29</Name>
              <Page isRef="40" />
              <Parent isRef="78" />
              <Text>{root_data_managerDailyMonthRespVO_roomRevenueIndex.avgRoomFee}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本29>
            <文本30 Ref="90" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>22.4,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <Margins>9,0,0,0</Margins>
              <Name>文本30</Name>
              <Page isRef="40" />
              <Parent isRef="78" />
              <Text>{root_data_managerDailyMonthRespVO_roomRevenueIndex.revPar}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本30>
            <文本31 Ref="91" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>24.6,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <Margins>9,0,0,0</Margins>
              <Name>文本31</Name>
              <Page isRef="40" />
              <Parent isRef="78" />
              <Text>{root_data_managerDailyMonthRespVO_roomRevenueIndex.occ}%</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本31>
            <文本32 Ref="92" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>26.8,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>文本32</Name>
              <Page isRef="40" />
              <Parent isRef="78" />
              <Text>{root_data_managerDailyMonthRespVO_roomRevenueIndex.overnightOcc}%</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本32>
            <文本33 Ref="93" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>29.2,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本33</Name>
              <Page isRef="40" />
              <Parent isRef="78" />
              <Text>{root_data_managerDailyMonthRespVO_roomRevenueIndex.good}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本33>
            <文本34 Ref="94" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>31.6,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本34</Name>
              <Page isRef="40" />
              <Parent isRef="78" />
              <Text>{root_data_managerDailyMonthRespVO_roomRevenueIndex.memberCard}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本34>
            <文本35 Ref="95" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>34,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本35</Name>
              <Page isRef="40" />
              <Parent isRef="78" />
              <Text>{root_data_managerDailyMonthRespVO_roomRevenueIndex.catering}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本35>
            <文本36 Ref="96" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>36.2,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本36</Name>
              <Page isRef="40" />
              <Parent isRef="78" />
              <Text>{root_data_managerDailyMonthRespVO_roomRevenueIndex.otherFee}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本36>
            <文本37 Ref="97" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>38.4,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本37</Name>
              <Page isRef="40" />
              <Parent isRef="78" />
              <Text>{root_data_managerDailyMonthRespVO_roomRevenueIndex.indemnityFee}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本37>
            <文本38 Ref="98" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>40.6,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本38</Name>
              <Page isRef="40" />
              <Parent isRef="78" />
              <Text>{root_data_managerDailyMonthRespVO_roomRevenueIndex.lobbyFee}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本38>
            <文本39 Ref="99" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>42.8,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本39</Name>
              <Page isRef="40" />
              <Parent isRef="78" />
              <Text>{root_data_managerDailyMonthRespVO_roomRevenueIndex.memberRecharge}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本39>
            <文本152 Ref="100" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>17.8,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本152</Name>
              <Page isRef="40" />
              <Parent isRef="78" />
              <Text>{root_data_managerDailyMonthRespVO_roomRevenueIndex.couponDeduction}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本152>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>root_data_managerDailyMonthRespVO_roomRevenueIndex</DataSourceName>
          <Expressions isList="true" count="0" />
          <Filters isList="true" count="0" />
          <Name>数据区1</Name>
          <Page isRef="40" />
          <Parent isRef="40" />
          <Sort isList="true" count="0" />
        </数据区1>
        <页脚1 Ref="101" type="FooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,7.4,51,0.8</ClientRectangle>
          <Components isList="true" count="19">
            <文本3 Ref="102" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,4.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本3</Name>
              <Page isRef="40" />
              <Parent isRef="101" />
              <Text>合计</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="103" type="DateFormat" isKey="true" />
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本3>
            <文本19 Ref="104" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6.8,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <Margins>9,0,0,0</Margins>
              <Name>文本19</Name>
              <Page isRef="40" />
              <Parent isRef="101" />
              <Text>{Sum(root_data_managerDailyMonthRespVO_roomRevenueIndex.nightNum)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </文本19>
            <文本24 Ref="105" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.6,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <Margins>9,0,0,0</Margins>
              <Name>文本24</Name>
              <Page isRef="40" />
              <Parent isRef="101" />
              <Text>{TotalRoomNumSum=Sum(root_data_managerDailyMonthRespVO_roomRevenueIndex.totalRoomNum)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本24>
            <文本40 Ref="106" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>24.6,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <Margins>9,0,0,0</Margins>
              <Name>文本40</Name>
              <Page isRef="40" />
              <Parent isRef="101" />
              <Text>{Sum(root_data_managerDailyMonthRespVO_roomRevenueIndex.nightNum)/ Sum(root_data_managerDailyMonthRespVO_roomRevenueIndex.totalRoomNum)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="107" type="PercentageFormat" isKey="true">
                <DecimalDigits>2</DecimalDigits>
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <PositivePattern>1</PositivePattern>
                <Symbol>%</Symbol>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本40>
            <文本111 Ref="108" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <Margins>9,0,0,0</Margins>
              <Name>文本111</Name>
              <Page isRef="40" />
              <Parent isRef="101" />
              <Text>{Sum(root_data_managerDailyMonthRespVO_roomRevenueIndex.selfNum)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </文本111>
            <文本112 Ref="109" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>11.2,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <Margins>9,0,0,0</Margins>
              <Name>文本112</Name>
              <Page isRef="40" />
              <Parent isRef="101" />
              <Text>{Sum(root_data_managerDailyMonthRespVO_roomRevenueIndex.repairNum)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </文本112>
            <文本113 Ref="110" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.4,0,2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <Margins>9,0,0,0</Margins>
              <Name>文本113</Name>
              <Page isRef="40" />
              <Parent isRef="101" />
              <Text>{Sum(root_data_managerDailyMonthRespVO_roomRevenueIndex.overnightNum)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </文本113>
            <文本114 Ref="111" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.4,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本114</Name>
              <Page isRef="40" />
              <Parent isRef="101" />
              <Text>{Sum(root_data_managerDailyMonthRespVO_roomRevenueIndex.roomFee)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="112" type="CurrencyFormat" isKey="true">
                <DecimalDigits>2</DecimalDigits>
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>2</NegativePattern>
                <PositivePattern>0</PositivePattern>
                <Symbol>¥</Symbol>
              </TextFormat>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </文本114>
            <文本115 Ref="113" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>20.2,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本115</Name>
              <Page isRef="40" />
              <Parent isRef="101" />
              <Text>{Sum(root_data_managerDailyMonthRespVO_roomRevenueIndex.roomFee)/Sum(root_data_managerDailyMonthRespVO_roomRevenueIndex.nightNum)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="114" type="NumberFormat" isKey="true">
                <DecimalDigits>2</DecimalDigits>
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本115>
            <文本116 Ref="115" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>22.4,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <Margins>9,0,0,0</Margins>
              <Name>文本116</Name>
              <Page isRef="40" />
              <Parent isRef="101" />
              <Text>{Sum(root_data_managerDailyMonthRespVO_roomRevenueIndex.roomFee)/ Sum(root_data_managerDailyMonthRespVO_roomRevenueIndex.totalRoomNum)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="116" type="NumberFormat" isKey="true">
                <DecimalDigits>2</DecimalDigits>
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本116>
            <文本117 Ref="117" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>26.8,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>文本117</Name>
              <Page isRef="40" />
              <Parent isRef="101" />
              <Text>{Sum(root_data_managerDailyMonthRespVO_roomRevenueIndex.overnightNum)/ Sum(root_data_managerDailyMonthRespVO_roomRevenueIndex.totalRoomNum)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="118" type="PercentageFormat" isKey="true">
                <DecimalDigits>2</DecimalDigits>
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <PositivePattern>1</PositivePattern>
                <Symbol>%</Symbol>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本117>
            <文本118 Ref="119" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>29.2,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本118</Name>
              <Page isRef="40" />
              <Parent isRef="101" />
              <Text>{Sum(root_data_managerDailyMonthRespVO_roomRevenueIndex.good)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </文本118>
            <文本119 Ref="120" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>31.6,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本119</Name>
              <Page isRef="40" />
              <Parent isRef="101" />
              <Text>{Sum(root_data_managerDailyMonthRespVO_roomRevenueIndex.memberCard)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </文本119>
            <文本120 Ref="121" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>34,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本120</Name>
              <Page isRef="40" />
              <Parent isRef="101" />
              <Text>{Sum(root_data_managerDailyMonthRespVO_roomRevenueIndex.catering)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </文本120>
            <文本121 Ref="122" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>36.2,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本121</Name>
              <Page isRef="40" />
              <Parent isRef="101" />
              <Text>{Sum(root_data_managerDailyMonthRespVO_roomRevenueIndex.otherFee)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </文本121>
            <文本122 Ref="123" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>38.4,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本122</Name>
              <Page isRef="40" />
              <Parent isRef="101" />
              <Text>{Sum(root_data_managerDailyMonthRespVO_roomRevenueIndex.indemnityFee)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </文本122>
            <文本123 Ref="124" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>40.6,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本123</Name>
              <Page isRef="40" />
              <Parent isRef="101" />
              <Text>{Sum(root_data_managerDailyMonthRespVO_roomRevenueIndex.lobbyFee)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </文本123>
            <文本124 Ref="125" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>42.8,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本124</Name>
              <Page isRef="40" />
              <Parent isRef="101" />
              <Text>{Sum(root_data_managerDailyMonthRespVO_roomRevenueIndex.memberRecharge)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </文本124>
            <文本153 Ref="126" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>17.8,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本153</Name>
              <Page isRef="40" />
              <Parent isRef="101" />
              <Text>{Sum(root_data_managerDailyMonthRespVO_roomRevenueIndex.couponDeduction)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </文本153>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>页脚1</Name>
          <Page isRef="40" />
          <Parent isRef="40" />
        </页脚1>
        <页眉2 Ref="127" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,9,51,1.2</ClientRectangle>
          <Components isList="true" count="1">
            <文本41 Ref="128" type="Text" isKey="true">
              <Border>Left, Right, Bottom;Black;1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,3,1.2</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <Margins>5,0,0,5</Margins>
              <Name>文本41</Name>
              <Page isRef="40" />
              <Parent isRef="127" />
              <Text>客源</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </文本41>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>页眉2</Name>
          <Page isRef="40" />
          <Parent isRef="40" />
        </页眉2>
        <分组页眉1 Ref="129" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,11,51,1.6</ClientRectangle>
          <Components isList="true" count="3">
            <文本43 Ref="130" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.4,0,2.2,1.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本43</Name>
              <Page isRef="40" />
              <Parent isRef="129" />
              <Text>星期</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本43>
            <面板2 Ref="131" type="Panel" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.6,0,11.6,1.6</ClientRectangle>
              <Components isList="true" count="1">
                <交叉_数据区2 Ref="132" type="CrossDataBand" isKey="true">
                  <Brush>Transparent</Brush>
                  <BusinessObjectGuid isNull="true" />
                  <ClientRectangle>0,0,8.8,1.6</ClientRectangle>
                  <Components isList="true" count="5">
                    <文本42 Ref="133" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0,8.8,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本42</Name>
                      <Page isRef="40" />
                      <Parent isRef="132" />
                      <Text>{root_data_managerDailyMonthRespVO_gsrcNameList.name}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本42>
                    <文本44 Ref="134" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0.8,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本44</Name>
                      <Page isRef="40" />
                      <Parent isRef="132" />
                      <Text>间夜数</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本44>
                    <文本45 Ref="135" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>2.2,0.8,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本45</Name>
                      <Page isRef="40" />
                      <Parent isRef="132" />
                      <Text>房费</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本45>
                    <文本49 Ref="136" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>4.4,0.8,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本49</Name>
                      <Page isRef="40" />
                      <Parent isRef="132" />
                      <Text>平均房价</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本49>
                    <文本50 Ref="137" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>6.6,0.8,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本50</Name>
                      <Page isRef="40" />
                      <Parent isRef="132" />
                      <Text>出租率</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本50>
                  </Components>
                  <Conditions isList="true" count="0" />
                  <DataRelationName isNull="true" />
                  <DataSourceName>root_data_managerDailyMonthRespVO_gsrcNameList</DataSourceName>
                  <Expressions isList="true" count="0" />
                  <Filters isList="true" count="0" />
                  <MasterComponent isRef="139" />
                  <Name>交叉_数据区2</Name>
                  <Page isRef="40" />
                  <Parent isRef="131" />
                  <Sort isList="true" count="0" />
                </交叉_数据区2>
              </Components>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Name>面板2</Name>
              <Page isRef="40" />
              <Parent isRef="129" />
            </面板2>
            <文本51 Ref="138" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2.4,1.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本51</Name>
              <Page isRef="40" />
              <Parent isRef="129" />
              <Text>营业日</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本51>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>分组页眉1</Name>
          <Page isRef="40" />
          <Parent isRef="40" />
        </分组页眉1>
        <数据区2 Ref="139" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,13.4,51,0.8</ClientRectangle>
          <Components isList="true" count="3">
            <文本53 Ref="140" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本53</Name>
              <Page isRef="40" />
              <Parent isRef="139" />
              <Text>{root_data_managerDailyMonthRespVO_gsrcList.bizDate}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="141" type="DateFormat" isKey="true" />
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本53>
            <面板1 Ref="142" type="Panel" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.6,0,8.8,0.8</ClientRectangle>
              <Components isList="true" count="1">
                <交叉_数据区1 Ref="143" type="CrossDataBand" isKey="true">
                  <Brush>Transparent</Brush>
                  <BusinessObjectGuid isNull="true" />
                  <ClientRectangle>0,0,8.8,0.8</ClientRectangle>
                  <Components isList="true" count="4">
                    <文本46 Ref="144" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <Margins>9,0,0,0</Margins>
                      <Name>文本46</Name>
                      <Page isRef="40" />
                      <Parent isRef="143" />
                      <Text>{root_data_managerDailyMonthRespVO_gsrcList_data.nightNum}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本46>
                    <文本47 Ref="145" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>2.2,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Right</HorAlignment>
                      <Margins>0,5,0,0</Margins>
                      <Name>文本47</Name>
                      <Page isRef="40" />
                      <Parent isRef="143" />
                      <Text>{root_data_managerDailyMonthRespVO_gsrcList_data.totalFee}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本47>
                    <文本48 Ref="146" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>4.4,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Right</HorAlignment>
                      <Margins>0,5,0,0</Margins>
                      <Name>文本48</Name>
                      <Page isRef="40" />
                      <Parent isRef="143" />
                      <Text>{Format("{0:F2}", root_data_managerDailyMonthRespVO_gsrcList_data.totalFee / root_data_managerDailyMonthRespVO_gsrcList_data.nightNum)}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本48>
                    <文本52 Ref="147" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>6.6,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <Margins>9,0,0,0</Margins>
                      <Name>文本52</Name>
                      <Page isRef="40" />
                      <Parent isRef="143" />
                      <Text>{root_data_managerDailyMonthRespVO_gsrcList_data.occ}%</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本52>
                  </Components>
                  <Conditions isList="true" count="0" />
                  <DataRelationName>root_data_managerDailyMonthRespVO_gsrcList_data</DataRelationName>
                  <DataSourceName>root_data_managerDailyMonthRespVO_gsrcList_data</DataSourceName>
                  <Expressions isList="true" count="0" />
                  <FilterOn>False</FilterOn>
                  <Filters isList="true" count="0" />
                  <MasterComponent isRef="139" />
                  <Name>交叉_数据区1</Name>
                  <Page isRef="40" />
                  <Parent isRef="142" />
                  <Sort isList="true" count="0" />
                </交叉_数据区1>
              </Components>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Name>面板1</Name>
              <Page isRef="40" />
              <Parent isRef="139" />
            </面板1>
            <文本54 Ref="148" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.4,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>文本54</Name>
              <Page isRef="40" />
              <Parent isRef="139" />
              <Text>{root_data_managerDailyMonthRespVO_gsrcList.week}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本54>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>root_data_managerDailyMonthRespVO_gsrcList</DataSourceName>
          <Expressions isList="true" count="0" />
          <FilterOn>False</FilterOn>
          <Filters isList="true" count="1">
            <value>cateGory,EqualTo,客源,,String</value>
          </Filters>
          <Name>数据区2</Name>
          <Page isRef="40" />
          <Parent isRef="40" />
          <Sort isList="true" count="0" />
        </数据区2>
        <分组页脚1 Ref="149" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,15,51,1.2</ClientRectangle>
          <Components isList="true" count="2">
            <文本125 Ref="150" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,4.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本125</Name>
              <Page isRef="40" />
              <Parent isRef="149" />
              <Text>合计</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="151" type="DateFormat" isKey="true" />
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本125>
            <面板11 Ref="152" type="Panel" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.6,0,9,1.2</ClientRectangle>
              <Components isList="true" count="1">
                <交叉_数据区11 Ref="153" type="CrossDataBand" isKey="true">
                  <Brush>Transparent</Brush>
                  <BusinessObjectGuid isNull="true" />
                  <ClientRectangle>0,0,8.8,1.2</ClientRectangle>
                  <Components isList="true" count="4">
                    <文本126 Ref="154" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <Margins>9,0,0,0</Margins>
                      <Name>文本126</Name>
                      <Page isRef="40" />
                      <Parent isRef="153" />
                      <Text>{root_data_managerDailyMonthRespVO_gsrcNameList.nightNumSum}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本126>
                    <文本127 Ref="155" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>2.2,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Right</HorAlignment>
                      <Margins>0,5,0,0</Margins>
                      <Name>文本127</Name>
                      <Page isRef="40" />
                      <Parent isRef="153" />
                      <Text>{root_data_managerDailyMonthRespVO_gsrcNameList.totalFeeSum}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本127>
                    <文本129 Ref="156" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>4.4,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Right</HorAlignment>
                      <Margins>0,5,0,0</Margins>
                      <Name>文本129</Name>
                      <Page isRef="40" />
                      <Parent isRef="153" />
                      <Text>{root_data_managerDailyMonthRespVO_gsrcNameList.totalFeeSum/root_data_managerDailyMonthRespVO_gsrcNameList.nightNumSum}</Text>
                      <TextBrush>Black</TextBrush>
                      <TextFormat Ref="157" type="NumberFormat" isKey="true">
                        <DecimalDigits>2</DecimalDigits>
                        <GroupSeparator>,</GroupSeparator>
                        <NegativePattern>1</NegativePattern>
                      </TextFormat>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本129>
                    <文本130 Ref="158" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>6.6,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <Margins>9,0,0,0</Margins>
                      <Name>文本130</Name>
                      <Page isRef="40" />
                      <Parent isRef="153" />
                      <Text>{root_data_managerDailyMonthRespVO_gsrcNameList.nightNumSum/TotalRoomNumSum}</Text>
                      <TextBrush>Black</TextBrush>
                      <TextFormat Ref="159" type="PercentageFormat" isKey="true">
                        <DecimalDigits>2</DecimalDigits>
                        <GroupSeparator>,</GroupSeparator>
                        <NegativePattern>1</NegativePattern>
                        <PositivePattern>1</PositivePattern>
                        <Symbol>%</Symbol>
                      </TextFormat>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本130>
                  </Components>
                  <Conditions isList="true" count="0" />
                  <DataRelationName isNull="true" />
                  <DataSourceName>root_data_managerDailyMonthRespVO_gsrcNameList</DataSourceName>
                  <Expressions isList="true" count="0" />
                  <FilterOn>False</FilterOn>
                  <Filters isList="true" count="0" />
                  <MasterComponent isRef="139" />
                  <Name>交叉_数据区11</Name>
                  <Page isRef="40" />
                  <Parent isRef="152" />
                  <Sort isList="true" count="0" />
                </交叉_数据区11>
              </Components>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Name>面板11</Name>
              <Page isRef="40" />
              <Parent isRef="149" />
            </面板11>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>分组页脚1</Name>
          <Page isRef="40" />
          <Parent isRef="40" />
        </分组页脚1>
        <页眉3 Ref="160" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,17,51,1.2</ClientRectangle>
          <Components isList="true" count="1">
            <文本55 Ref="161" type="Text" isKey="true">
              <Border>Left, Right, Bottom;Black;1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2.4,1.2</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <Margins>5,0,0,5</Margins>
              <Name>文本55</Name>
              <Page isRef="40" />
              <Parent isRef="160" />
              <Text>入住类型</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </文本55>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>页眉3</Name>
          <Page isRef="40" />
          <Parent isRef="40" />
        </页眉3>
        <分组页眉2 Ref="162" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,19,51,1.6</ClientRectangle>
          <Components isList="true" count="3">
            <文本56 Ref="163" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.4,0,2.2,1.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本56</Name>
              <Page isRef="40" />
              <Parent isRef="162" />
              <Text>星期</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本56>
            <面板3 Ref="164" type="Panel" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.6,0,11.6,1.6</ClientRectangle>
              <Components isList="true" count="1">
                <交叉_数据区3 Ref="165" type="CrossDataBand" isKey="true">
                  <Brush>Transparent</Brush>
                  <BusinessObjectGuid isNull="true" />
                  <ClientRectangle>0,0,8.8,1.6</ClientRectangle>
                  <Components isList="true" count="5">
                    <文本57 Ref="166" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0,8.8,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本57</Name>
                      <Page isRef="40" />
                      <Parent isRef="165" />
                      <Text>{root_data_managerDailyMonthRespVO_inTypeNameList.name}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本57>
                    <文本58 Ref="167" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0.8,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本58</Name>
                      <Page isRef="40" />
                      <Parent isRef="165" />
                      <Text>间夜数</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本58>
                    <文本59 Ref="168" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>2.2,0.8,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本59</Name>
                      <Page isRef="40" />
                      <Parent isRef="165" />
                      <Text>房费</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本59>
                    <文本60 Ref="169" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>4.4,0.8,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本60</Name>
                      <Page isRef="40" />
                      <Parent isRef="165" />
                      <Text>平均房价</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本60>
                    <文本61 Ref="170" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>6.6,0.8,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本61</Name>
                      <Page isRef="40" />
                      <Parent isRef="165" />
                      <Text>出租率</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本61>
                  </Components>
                  <Conditions isList="true" count="0" />
                  <DataRelationName isNull="true" />
                  <DataSourceName>root_data_managerDailyMonthRespVO_inTypeNameList</DataSourceName>
                  <Expressions isList="true" count="0" />
                  <Filters isList="true" count="0" />
                  <Name>交叉_数据区3</Name>
                  <Page isRef="40" />
                  <Parent isRef="164" />
                  <Sort isList="true" count="0" />
                </交叉_数据区3>
              </Components>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Name>面板3</Name>
              <Page isRef="40" />
              <Parent isRef="162" />
            </面板3>
            <文本62 Ref="171" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2.4,1.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本62</Name>
              <Page isRef="40" />
              <Parent isRef="162" />
              <Text>营业日</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本62>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>分组页眉2</Name>
          <Page isRef="40" />
          <Parent isRef="40" />
        </分组页眉2>
        <数据区3 Ref="172" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,21.4,51,0.8</ClientRectangle>
          <Components isList="true" count="3">
            <文本63 Ref="173" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本63</Name>
              <Page isRef="40" />
              <Parent isRef="172" />
              <Text>{root_data_managerDailyMonthRespVO_inTypeList.bizDate}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="174" type="DateFormat" isKey="true" />
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本63>
            <面板4 Ref="175" type="Panel" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.6,0,11.6,0.8</ClientRectangle>
              <Components isList="true" count="1">
                <交叉_数据区4 Ref="176" type="CrossDataBand" isKey="true">
                  <Brush>Transparent</Brush>
                  <BusinessObjectGuid isNull="true" />
                  <ClientRectangle>0,0,8.8,0.8</ClientRectangle>
                  <Components isList="true" count="4">
                    <文本64 Ref="177" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <Margins>9,0,0,0</Margins>
                      <Name>文本64</Name>
                      <Page isRef="40" />
                      <Parent isRef="176" />
                      <Text>{root_data_managerDailyMonthRespVO_inTypeList_data.nightNum}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本64>
                    <文本65 Ref="178" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>2.2,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Right</HorAlignment>
                      <Margins>0,5,0,0</Margins>
                      <Name>文本65</Name>
                      <Page isRef="40" />
                      <Parent isRef="176" />
                      <Text>{root_data_managerDailyMonthRespVO_inTypeList_data.totalFee}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本65>
                    <文本66 Ref="179" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>4.4,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Right</HorAlignment>
                      <Margins>0,5,0,0</Margins>
                      <Name>文本66</Name>
                      <Page isRef="40" />
                      <Parent isRef="176" />
                      <Text>{Format("{0:F2}", root_data_managerDailyMonthRespVO_inTypeList_data.totalFee / root_data_managerDailyMonthRespVO_inTypeList_data.nightNum)}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本66>
                    <文本67 Ref="180" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>6.6,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <Margins>9,0,0,0</Margins>
                      <Name>文本67</Name>
                      <Page isRef="40" />
                      <Parent isRef="176" />
                      <Text>{root_data_managerDailyMonthRespVO_inTypeList_data.occ}%</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本67>
                  </Components>
                  <Conditions isList="true" count="0" />
                  <DataRelationName>root_data_managerDailyMonthRespVO_inTypeList_data</DataRelationName>
                  <DataSourceName>root_data_managerDailyMonthRespVO_inTypeList_data</DataSourceName>
                  <Expressions isList="true" count="0" />
                  <FilterOn>False</FilterOn>
                  <Filters isList="true" count="0" />
                  <MasterComponent isRef="172" />
                  <Name>交叉_数据区4</Name>
                  <Page isRef="40" />
                  <Parent isRef="175" />
                  <Sort isList="true" count="0" />
                </交叉_数据区4>
              </Components>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Name>面板4</Name>
              <Page isRef="40" />
              <Parent isRef="172" />
            </面板4>
            <文本68 Ref="181" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.4,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>文本68</Name>
              <Page isRef="40" />
              <Parent isRef="172" />
              <Text>{root_data_managerDailyMonthRespVO_inTypeList.week}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本68>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>root_data_managerDailyMonthRespVO_inTypeList</DataSourceName>
          <Expressions isList="true" count="0" />
          <FilterOn>False</FilterOn>
          <Filters isList="true" count="0" />
          <Name>数据区3</Name>
          <Page isRef="40" />
          <Parent isRef="40" />
          <Sort isList="true" count="0" />
        </数据区3>
        <分组页脚2 Ref="182" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,23,51,1</ClientRectangle>
          <Components isList="true" count="2">
            <文本128 Ref="183" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,4.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本128</Name>
              <Page isRef="40" />
              <Parent isRef="182" />
              <Text>合计</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="184" type="DateFormat" isKey="true" />
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本128>
            <面板12 Ref="185" type="Panel" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.6,0,9,1</ClientRectangle>
              <Components isList="true" count="1">
                <交叉_数据区12 Ref="186" type="CrossDataBand" isKey="true">
                  <Brush>Transparent</Brush>
                  <BusinessObjectGuid isNull="true" />
                  <ClientRectangle>0,0,8.8,1</ClientRectangle>
                  <Components isList="true" count="4">
                    <文本131 Ref="187" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <Margins>9,0,0,0</Margins>
                      <Name>文本131</Name>
                      <Page isRef="40" />
                      <Parent isRef="186" />
                      <Text>{root_data_managerDailyMonthRespVO_inTypeNameList.nightNumSum}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本131>
                    <文本132 Ref="188" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>2.2,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Right</HorAlignment>
                      <Margins>0,5,0,0</Margins>
                      <Name>文本132</Name>
                      <Page isRef="40" />
                      <Parent isRef="186" />
                      <Text>{root_data_managerDailyMonthRespVO_inTypeNameList.totalFeeSum}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本132>
                    <文本133 Ref="189" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>4.4,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Right</HorAlignment>
                      <Margins>0,5,0,0</Margins>
                      <Name>文本133</Name>
                      <Page isRef="40" />
                      <Parent isRef="186" />
                      <Text>{root_data_managerDailyMonthRespVO_inTypeNameList.totalFeeSum/root_data_managerDailyMonthRespVO_inTypeNameList.nightNumSum}</Text>
                      <TextBrush>Black</TextBrush>
                      <TextFormat Ref="190" type="NumberFormat" isKey="true">
                        <DecimalDigits>2</DecimalDigits>
                        <GroupSeparator>,</GroupSeparator>
                        <NegativePattern>1</NegativePattern>
                      </TextFormat>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本133>
                    <文本134 Ref="191" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>6.6,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <Margins>9,0,0,0</Margins>
                      <Name>文本134</Name>
                      <Page isRef="40" />
                      <Parent isRef="186" />
                      <Text>{root_data_managerDailyMonthRespVO_inTypeNameList.nightNumSum/TotalRoomNumSum}</Text>
                      <TextBrush>Black</TextBrush>
                      <TextFormat Ref="192" type="PercentageFormat" isKey="true">
                        <DecimalDigits>2</DecimalDigits>
                        <GroupSeparator>,</GroupSeparator>
                        <NegativePattern>1</NegativePattern>
                        <PositivePattern>1</PositivePattern>
                        <Symbol>%</Symbol>
                      </TextFormat>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本134>
                  </Components>
                  <Conditions isList="true" count="0" />
                  <DataRelationName isNull="true" />
                  <DataSourceName>root_data_managerDailyMonthRespVO_inTypeNameList</DataSourceName>
                  <Expressions isList="true" count="0" />
                  <FilterOn>False</FilterOn>
                  <Filters isList="true" count="0" />
                  <MasterComponent isRef="172" />
                  <Name>交叉_数据区12</Name>
                  <Page isRef="40" />
                  <Parent isRef="185" />
                  <Sort isList="true" count="0" />
                </交叉_数据区12>
              </Components>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Name>面板12</Name>
              <Page isRef="40" />
              <Parent isRef="182" />
            </面板12>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>分组页脚2</Name>
          <Page isRef="40" />
          <Parent isRef="40" />
        </分组页脚2>
        <页眉4 Ref="193" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,24.8,51,1.2</ClientRectangle>
          <Components isList="true" count="1">
            <文本69 Ref="194" type="Text" isKey="true">
              <Border>Left, Right, Bottom;Black;1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2.4,1.2</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <Margins>5,0,0,5</Margins>
              <Name>文本69</Name>
              <Page isRef="40" />
              <Parent isRef="193" />
              <Text>订单来源</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </文本69>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>页眉4</Name>
          <Page isRef="40" />
          <Parent isRef="40" />
        </页眉4>
        <分组页眉3 Ref="195" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,26.8,51,1.6</ClientRectangle>
          <Components isList="true" count="3">
            <文本70 Ref="196" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.4,0,2.2,1.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本70</Name>
              <Page isRef="40" />
              <Parent isRef="195" />
              <Text>星期</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本70>
            <面板5 Ref="197" type="Panel" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.6,0,11.6,1.6</ClientRectangle>
              <Components isList="true" count="1">
                <交叉_数据区5 Ref="198" type="CrossDataBand" isKey="true">
                  <Brush>Transparent</Brush>
                  <BusinessObjectGuid isNull="true" />
                  <ClientRectangle>0,0,8.8,1.6</ClientRectangle>
                  <Components isList="true" count="5">
                    <文本71 Ref="199" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0,8.8,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本71</Name>
                      <Page isRef="40" />
                      <Parent isRef="198" />
                      <Text>{root_data_managerDailyMonthRespVO_orderSrcNameList.name}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本71>
                    <文本72 Ref="200" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0.8,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本72</Name>
                      <Page isRef="40" />
                      <Parent isRef="198" />
                      <Text>间夜数</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本72>
                    <文本73 Ref="201" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>2.2,0.8,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本73</Name>
                      <Page isRef="40" />
                      <Parent isRef="198" />
                      <Text>房费</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本73>
                    <文本74 Ref="202" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>4.4,0.8,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本74</Name>
                      <Page isRef="40" />
                      <Parent isRef="198" />
                      <Text>平均房价</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本74>
                    <文本75 Ref="203" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>6.6,0.8,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本75</Name>
                      <Page isRef="40" />
                      <Parent isRef="198" />
                      <Text>出租率</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本75>
                  </Components>
                  <Conditions isList="true" count="0" />
                  <DataRelationName isNull="true" />
                  <DataSourceName>root_data_managerDailyMonthRespVO_orderSrcNameList</DataSourceName>
                  <Expressions isList="true" count="0" />
                  <Filters isList="true" count="0" />
                  <Name>交叉_数据区5</Name>
                  <Page isRef="40" />
                  <Parent isRef="197" />
                  <Sort isList="true" count="0" />
                </交叉_数据区5>
              </Components>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Name>面板5</Name>
              <Page isRef="40" />
              <Parent isRef="195" />
            </面板5>
            <文本76 Ref="204" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2.4,1.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本76</Name>
              <Page isRef="40" />
              <Parent isRef="195" />
              <Text>营业日</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本76>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>分组页眉3</Name>
          <Page isRef="40" />
          <Parent isRef="40" />
        </分组页眉3>
        <数据区4 Ref="205" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,29.2,51,0.8</ClientRectangle>
          <Components isList="true" count="3">
            <文本77 Ref="206" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本77</Name>
              <Page isRef="40" />
              <Parent isRef="205" />
              <Text>{root_data_managerDailyMonthRespVO_orderSrcList.bizDate}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="207" type="DateFormat" isKey="true" />
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本77>
            <面板6 Ref="208" type="Panel" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.6,0,11.6,0.8</ClientRectangle>
              <Components isList="true" count="1">
                <交叉_数据区6 Ref="209" type="CrossDataBand" isKey="true">
                  <Brush>Transparent</Brush>
                  <BusinessObjectGuid isNull="true" />
                  <ClientRectangle>0,0,8.8,0.8</ClientRectangle>
                  <Components isList="true" count="4">
                    <文本78 Ref="210" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <Margins>9,0,0,0</Margins>
                      <Name>文本78</Name>
                      <Page isRef="40" />
                      <Parent isRef="209" />
                      <Text>{root_data_managerDailyMonthRespVO_orderSrcList_data.nightNum}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本78>
                    <文本79 Ref="211" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>2.2,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Right</HorAlignment>
                      <Margins>0,5,0,0</Margins>
                      <Name>文本79</Name>
                      <Page isRef="40" />
                      <Parent isRef="209" />
                      <Text>{root_data_managerDailyMonthRespVO_orderSrcList_data.totalFee}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本79>
                    <文本80 Ref="212" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>4.4,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Right</HorAlignment>
                      <Margins>0,5,0,0</Margins>
                      <Name>文本80</Name>
                      <Page isRef="40" />
                      <Parent isRef="209" />
                      <Text>{Format("{0:F2}", root_data_managerDailyMonthRespVO_orderSrcList_data.totalFee / root_data_managerDailyMonthRespVO_orderSrcList_data.nightNum)}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本80>
                    <文本81 Ref="213" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>6.6,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <Margins>9,0,0,0</Margins>
                      <Name>文本81</Name>
                      <Page isRef="40" />
                      <Parent isRef="209" />
                      <Text>{root_data_managerDailyMonthRespVO_orderSrcList_data.occ}%</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本81>
                  </Components>
                  <Conditions isList="true" count="0" />
                  <DataRelationName>root_data_managerDailyMonthRespVO_orderSrcList_data</DataRelationName>
                  <DataSourceName>root_data_managerDailyMonthRespVO_orderSrcList_data</DataSourceName>
                  <Expressions isList="true" count="0" />
                  <FilterOn>False</FilterOn>
                  <Filters isList="true" count="0" />
                  <MasterComponent isRef="205" />
                  <Name>交叉_数据区6</Name>
                  <Page isRef="40" />
                  <Parent isRef="208" />
                  <Sort isList="true" count="0" />
                </交叉_数据区6>
              </Components>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Name>面板6</Name>
              <Page isRef="40" />
              <Parent isRef="205" />
            </面板6>
            <文本82 Ref="214" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.4,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>文本82</Name>
              <Page isRef="40" />
              <Parent isRef="205" />
              <Text>{root_data_managerDailyMonthRespVO_orderSrcList.week}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本82>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>root_data_managerDailyMonthRespVO_orderSrcList</DataSourceName>
          <Expressions isList="true" count="0" />
          <FilterOn>False</FilterOn>
          <Filters isList="true" count="0" />
          <Name>数据区4</Name>
          <Page isRef="40" />
          <Parent isRef="40" />
          <Sort isList="true" count="0" />
        </数据区4>
        <分组页脚3 Ref="215" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,30.8,51,0.8</ClientRectangle>
          <Components isList="true" count="2">
            <文本135 Ref="216" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,4.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本135</Name>
              <Page isRef="40" />
              <Parent isRef="215" />
              <Text>合计</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="217" type="DateFormat" isKey="true" />
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本135>
            <面板13 Ref="218" type="Panel" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.6,0,9,1</ClientRectangle>
              <Components isList="true" count="1">
                <交叉_数据区13 Ref="219" type="CrossDataBand" isKey="true">
                  <Brush>Transparent</Brush>
                  <BusinessObjectGuid isNull="true" />
                  <ClientRectangle>0,0,8.8,1</ClientRectangle>
                  <Components isList="true" count="4">
                    <文本136 Ref="220" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <Margins>9,0,0,0</Margins>
                      <Name>文本136</Name>
                      <Page isRef="40" />
                      <Parent isRef="219" />
                      <Text>{root_data_managerDailyMonthRespVO_orderSrcNameList.nightNumSum}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本136>
                    <文本137 Ref="221" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>2.2,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Right</HorAlignment>
                      <Margins>0,5,0,0</Margins>
                      <Name>文本137</Name>
                      <Page isRef="40" />
                      <Parent isRef="219" />
                      <Text>{root_data_managerDailyMonthRespVO_orderSrcNameList.totalFeeSum}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本137>
                    <文本138 Ref="222" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>4.4,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Right</HorAlignment>
                      <Margins>0,5,0,0</Margins>
                      <Name>文本138</Name>
                      <Page isRef="40" />
                      <Parent isRef="219" />
                      <Text>{root_data_managerDailyMonthRespVO_orderSrcNameList.totalFeeSum/root_data_managerDailyMonthRespVO_orderSrcNameList.nightNumSum}</Text>
                      <TextBrush>Black</TextBrush>
                      <TextFormat Ref="223" type="NumberFormat" isKey="true">
                        <DecimalDigits>2</DecimalDigits>
                        <GroupSeparator>,</GroupSeparator>
                        <NegativePattern>1</NegativePattern>
                      </TextFormat>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本138>
                    <文本139 Ref="224" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>6.6,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <Margins>9,0,0,0</Margins>
                      <Name>文本139</Name>
                      <Page isRef="40" />
                      <Parent isRef="219" />
                      <Text>{root_data_managerDailyMonthRespVO_orderSrcNameList.nightNumSum/TotalRoomNumSum}</Text>
                      <TextBrush>Black</TextBrush>
                      <TextFormat Ref="225" type="PercentageFormat" isKey="true">
                        <DecimalDigits>2</DecimalDigits>
                        <GroupSeparator>,</GroupSeparator>
                        <NegativePattern>1</NegativePattern>
                        <PositivePattern>1</PositivePattern>
                        <Symbol>%</Symbol>
                      </TextFormat>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本139>
                  </Components>
                  <Conditions isList="true" count="0" />
                  <DataRelationName isNull="true" />
                  <DataSourceName>root_data_managerDailyMonthRespVO_orderSrcNameList</DataSourceName>
                  <Expressions isList="true" count="0" />
                  <FilterOn>False</FilterOn>
                  <Filters isList="true" count="0" />
                  <Name>交叉_数据区13</Name>
                  <Page isRef="40" />
                  <Parent isRef="218" />
                  <Sort isList="true" count="0" />
                </交叉_数据区13>
              </Components>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Name>面板13</Name>
              <Page isRef="40" />
              <Parent isRef="215" />
            </面板13>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>分组页脚3</Name>
          <Page isRef="40" />
          <Parent isRef="40" />
        </分组页脚3>
        <页眉5 Ref="226" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,32.4,51,1.2</ClientRectangle>
          <Components isList="true" count="1">
            <文本83 Ref="227" type="Text" isKey="true">
              <Border>Left, Right, Bottom;Black;1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2.4,1.2</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <Margins>5,0,0,5</Margins>
              <Name>文本83</Name>
              <Page isRef="40" />
              <Parent isRef="226" />
              <Text>房型</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </文本83>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>页眉5</Name>
          <Page isRef="40" />
          <Parent isRef="40" />
        </页眉5>
        <分组页眉4 Ref="228" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,34.4,51,1.6</ClientRectangle>
          <Components isList="true" count="3">
            <文本84 Ref="229" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.4,0,2.2,1.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本84</Name>
              <Page isRef="40" />
              <Parent isRef="228" />
              <Text>星期</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本84>
            <面板7 Ref="230" type="Panel" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.6,0,11.6,1.6</ClientRectangle>
              <Components isList="true" count="1">
                <交叉_数据区7 Ref="231" type="CrossDataBand" isKey="true">
                  <Brush>Transparent</Brush>
                  <BusinessObjectGuid isNull="true" />
                  <ClientRectangle>0,0,8.8,1.6</ClientRectangle>
                  <Components isList="true" count="5">
                    <文本85 Ref="232" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0,8.8,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本85</Name>
                      <Page isRef="40" />
                      <Parent isRef="231" />
                      <Text>{root_data_managerDailyMonthRespVO_rtCodeNameList.name}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本85>
                    <文本86 Ref="233" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0.8,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本86</Name>
                      <Page isRef="40" />
                      <Parent isRef="231" />
                      <Text>间夜数</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本86>
                    <文本87 Ref="234" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>2.2,0.8,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本87</Name>
                      <Page isRef="40" />
                      <Parent isRef="231" />
                      <Text>房费</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本87>
                    <文本88 Ref="235" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>4.4,0.8,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本88</Name>
                      <Page isRef="40" />
                      <Parent isRef="231" />
                      <Text>平均房价</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本88>
                    <文本89 Ref="236" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>6.6,0.8,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本89</Name>
                      <Page isRef="40" />
                      <Parent isRef="231" />
                      <Text>出租率</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本89>
                  </Components>
                  <Conditions isList="true" count="0" />
                  <DataRelationName isNull="true" />
                  <DataSourceName>root_data_managerDailyMonthRespVO_rtCodeNameList</DataSourceName>
                  <Expressions isList="true" count="0" />
                  <Filters isList="true" count="0" />
                  <Name>交叉_数据区7</Name>
                  <Page isRef="40" />
                  <Parent isRef="230" />
                  <Sort isList="true" count="0" />
                </交叉_数据区7>
              </Components>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Name>面板7</Name>
              <Page isRef="40" />
              <Parent isRef="228" />
            </面板7>
            <文本90 Ref="237" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2.4,1.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本90</Name>
              <Page isRef="40" />
              <Parent isRef="228" />
              <Text>营业日</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本90>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>分组页眉4</Name>
          <Page isRef="40" />
          <Parent isRef="40" />
        </分组页眉4>
        <数据区5 Ref="238" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,36.8,51,0.8</ClientRectangle>
          <Components isList="true" count="3">
            <文本91 Ref="239" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本91</Name>
              <Page isRef="40" />
              <Parent isRef="238" />
              <Text>{root_data_managerDailyMonthRespVO_rtCodeList.bizDate}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="240" type="DateFormat" isKey="true" />
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本91>
            <面板8 Ref="241" type="Panel" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.6,0,11.6,0.8</ClientRectangle>
              <Components isList="true" count="1">
                <交叉_数据区8 Ref="242" type="CrossDataBand" isKey="true">
                  <Brush>Transparent</Brush>
                  <BusinessObjectGuid isNull="true" />
                  <ClientRectangle>0,0,8.8,0.8</ClientRectangle>
                  <Components isList="true" count="4">
                    <文本92 Ref="243" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <Margins>9,0,0,0</Margins>
                      <Name>文本92</Name>
                      <Page isRef="40" />
                      <Parent isRef="242" />
                      <Text>{root_data_managerDailyMonthRespVO_rtCodeList_data.nightNum}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本92>
                    <文本93 Ref="244" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>2.2,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Right</HorAlignment>
                      <Margins>0,5,0,0</Margins>
                      <Name>文本93</Name>
                      <Page isRef="40" />
                      <Parent isRef="242" />
                      <Text>{root_data_managerDailyMonthRespVO_rtCodeList_data.totalFee}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本93>
                    <文本94 Ref="245" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>4.4,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Right</HorAlignment>
                      <Margins>0,5,0,0</Margins>
                      <Name>文本94</Name>
                      <Page isRef="40" />
                      <Parent isRef="242" />
                      <Text>{Format("{0:F2}", root_data_managerDailyMonthRespVO_rtCodeList_data.totalFee / root_data_managerDailyMonthRespVO_rtCodeList_data.nightNum)}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本94>
                    <文本95 Ref="246" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>6.6,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <Margins>9,0,0,0</Margins>
                      <Name>文本95</Name>
                      <Page isRef="40" />
                      <Parent isRef="242" />
                      <Text>{root_data_managerDailyMonthRespVO_rtCodeList_data.occ}%</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本95>
                  </Components>
                  <Conditions isList="true" count="0" />
                  <DataRelationName>root_data_managerDailyMonthRespVO_rtCodeList_data</DataRelationName>
                  <DataSourceName>root_data_managerDailyMonthRespVO_rtCodeList_data</DataSourceName>
                  <Expressions isList="true" count="0" />
                  <FilterOn>False</FilterOn>
                  <Filters isList="true" count="0" />
                  <MasterComponent isRef="238" />
                  <Name>交叉_数据区8</Name>
                  <Page isRef="40" />
                  <Parent isRef="241" />
                  <Sort isList="true" count="0" />
                </交叉_数据区8>
              </Components>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Name>面板8</Name>
              <Page isRef="40" />
              <Parent isRef="238" />
            </面板8>
            <文本96 Ref="247" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.4,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>文本96</Name>
              <Page isRef="40" />
              <Parent isRef="238" />
              <Text>{root_data_managerDailyMonthRespVO_rtCodeList.week}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本96>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>root_data_managerDailyMonthRespVO_rtCodeList</DataSourceName>
          <Expressions isList="true" count="0" />
          <FilterOn>False</FilterOn>
          <Filters isList="true" count="0" />
          <Name>数据区5</Name>
          <Page isRef="40" />
          <Parent isRef="40" />
          <Sort isList="true" count="0" />
        </数据区5>
        <分组页脚4 Ref="248" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,38.4,51,0.8</ClientRectangle>
          <Components isList="true" count="2">
            <文本140 Ref="249" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,4.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本140</Name>
              <Page isRef="40" />
              <Parent isRef="248" />
              <Text>合计</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="250" type="DateFormat" isKey="true" />
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本140>
            <面板14 Ref="251" type="Panel" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.6,0,9,1</ClientRectangle>
              <Components isList="true" count="1">
                <交叉_数据区14 Ref="252" type="CrossDataBand" isKey="true">
                  <Brush>Transparent</Brush>
                  <BusinessObjectGuid isNull="true" />
                  <ClientRectangle>0,0,8.8,1</ClientRectangle>
                  <Components isList="true" count="4">
                    <文本141 Ref="253" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <Margins>9,0,0,0</Margins>
                      <Name>文本141</Name>
                      <Page isRef="40" />
                      <Parent isRef="252" />
                      <Text>{root_data_managerDailyMonthRespVO_rtCodeNameList.nightNumSum}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本141>
                    <文本142 Ref="254" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>2.2,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Right</HorAlignment>
                      <Margins>0,5,0,0</Margins>
                      <Name>文本142</Name>
                      <Page isRef="40" />
                      <Parent isRef="252" />
                      <Text>{root_data_managerDailyMonthRespVO_rtCodeNameList.totalFeeSum}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本142>
                    <文本143 Ref="255" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>4.4,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Right</HorAlignment>
                      <Margins>0,5,0,0</Margins>
                      <Name>文本143</Name>
                      <Page isRef="40" />
                      <Parent isRef="252" />
                      <Text>{root_data_managerDailyMonthRespVO_rtCodeNameList.totalFeeSum/root_data_managerDailyMonthRespVO_rtCodeNameList.nightNumSum}</Text>
                      <TextBrush>Black</TextBrush>
                      <TextFormat Ref="256" type="NumberFormat" isKey="true">
                        <DecimalDigits>2</DecimalDigits>
                        <GroupSeparator>,</GroupSeparator>
                        <NegativePattern>1</NegativePattern>
                      </TextFormat>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本143>
                    <文本144 Ref="257" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>6.6,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <Margins>9,0,0,0</Margins>
                      <Name>文本144</Name>
                      <Page isRef="40" />
                      <Parent isRef="252" />
                      <Text>{root_data_managerDailyMonthRespVO_rtCodeNameList.nightNumSum/TotalRoomNumSum}</Text>
                      <TextBrush>Black</TextBrush>
                      <TextFormat Ref="258" type="PercentageFormat" isKey="true">
                        <DecimalDigits>2</DecimalDigits>
                        <GroupSeparator>,</GroupSeparator>
                        <NegativePattern>1</NegativePattern>
                        <PositivePattern>1</PositivePattern>
                        <Symbol>%</Symbol>
                      </TextFormat>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本144>
                  </Components>
                  <Conditions isList="true" count="0" />
                  <DataRelationName isNull="true" />
                  <DataSourceName>root_data_managerDailyMonthRespVO_rtCodeNameList</DataSourceName>
                  <Expressions isList="true" count="0" />
                  <FilterOn>False</FilterOn>
                  <Filters isList="true" count="0" />
                  <Name>交叉_数据区14</Name>
                  <Page isRef="40" />
                  <Parent isRef="251" />
                  <Sort isList="true" count="0" />
                </交叉_数据区14>
              </Components>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Name>面板14</Name>
              <Page isRef="40" />
              <Parent isRef="248" />
            </面板14>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>分组页脚4</Name>
          <Page isRef="40" />
          <Parent isRef="40" />
        </分组页脚4>
        <页眉6 Ref="259" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,40,51,1.2</ClientRectangle>
          <Components isList="true" count="1">
            <文本97 Ref="260" type="Text" isKey="true">
              <Border>Left, Right, Bottom;Black;1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2.4,1.2</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <Margins>5,0,0,5</Margins>
              <Name>文本97</Name>
              <Page isRef="40" />
              <Parent isRef="259" />
              <Text>渠道</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </文本97>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>页眉6</Name>
          <Page isRef="40" />
          <Parent isRef="40" />
        </页眉6>
        <分组页眉5 Ref="261" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,42,51,1.6</ClientRectangle>
          <Components isList="true" count="3">
            <文本98 Ref="262" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.4,0,2.2,1.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本98</Name>
              <Page isRef="40" />
              <Parent isRef="261" />
              <Text>星期</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本98>
            <面板9 Ref="263" type="Panel" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.6,0,11.6,1.6</ClientRectangle>
              <Components isList="true" count="1">
                <交叉_数据区9 Ref="264" type="CrossDataBand" isKey="true">
                  <Brush>Transparent</Brush>
                  <BusinessObjectGuid isNull="true" />
                  <ClientRectangle>0,0,8.8,1.6</ClientRectangle>
                  <Components isList="true" count="5">
                    <文本99 Ref="265" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0,8.8,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本99</Name>
                      <Page isRef="40" />
                      <Parent isRef="264" />
                      <Text>{root_data_managerDailyMonthRespVO_statChannelNameList.name}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本99>
                    <文本100 Ref="266" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0.8,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本100</Name>
                      <Page isRef="40" />
                      <Parent isRef="264" />
                      <Text>间夜数</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本100>
                    <文本101 Ref="267" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>2.2,0.8,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本101</Name>
                      <Page isRef="40" />
                      <Parent isRef="264" />
                      <Text>房费</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本101>
                    <文本102 Ref="268" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>4.4,0.8,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本102</Name>
                      <Page isRef="40" />
                      <Parent isRef="264" />
                      <Text>平均房价</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本102>
                    <文本103 Ref="269" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>6.6,0.8,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本103</Name>
                      <Page isRef="40" />
                      <Parent isRef="264" />
                      <Text>出租率</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本103>
                  </Components>
                  <Conditions isList="true" count="0" />
                  <DataRelationName isNull="true" />
                  <DataSourceName>root_data_managerDailyMonthRespVO_statChannelNameList</DataSourceName>
                  <Expressions isList="true" count="0" />
                  <Filters isList="true" count="0" />
                  <Name>交叉_数据区9</Name>
                  <Page isRef="40" />
                  <Parent isRef="263" />
                  <Sort isList="true" count="0" />
                </交叉_数据区9>
              </Components>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Name>面板9</Name>
              <Page isRef="40" />
              <Parent isRef="261" />
            </面板9>
            <文本104 Ref="270" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2.4,1.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本104</Name>
              <Page isRef="40" />
              <Parent isRef="261" />
              <Text>营业日</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本104>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>分组页眉5</Name>
          <Page isRef="40" />
          <Parent isRef="40" />
        </分组页眉5>
        <数据区6 Ref="271" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,44.4,51,0.8</ClientRectangle>
          <Components isList="true" count="3">
            <文本105 Ref="272" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本105</Name>
              <Page isRef="40" />
              <Parent isRef="271" />
              <Text>{root_data_managerDailyMonthRespVO_statChannelList.bizDate}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="273" type="DateFormat" isKey="true" />
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本105>
            <面板10 Ref="274" type="Panel" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.6,0,11.6,0.8</ClientRectangle>
              <Components isList="true" count="1">
                <交叉_数据区10 Ref="275" type="CrossDataBand" isKey="true">
                  <Brush>Transparent</Brush>
                  <BusinessObjectGuid isNull="true" />
                  <ClientRectangle>0,0,8.8,0.8</ClientRectangle>
                  <Components isList="true" count="4">
                    <文本106 Ref="276" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <Margins>9,0,0,0</Margins>
                      <Name>文本106</Name>
                      <Page isRef="40" />
                      <Parent isRef="275" />
                      <Text>{root_data_managerDailyMonthRespVO_statChannelList_data.nightNum}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本106>
                    <文本107 Ref="277" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>2.2,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Right</HorAlignment>
                      <Margins>0,5,0,0</Margins>
                      <Name>文本107</Name>
                      <Page isRef="40" />
                      <Parent isRef="275" />
                      <Text>{root_data_managerDailyMonthRespVO_statChannelList_data.totalFee}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本107>
                    <文本108 Ref="278" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>4.4,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Right</HorAlignment>
                      <Margins>0,5,0,0</Margins>
                      <Name>文本108</Name>
                      <Page isRef="40" />
                      <Parent isRef="275" />
                      <Text>{Format("{0:F2}", root_data_managerDailyMonthRespVO_statChannelList_data.totalFee / root_data_managerDailyMonthRespVO_statChannelList_data.nightNum)}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本108>
                    <文本109 Ref="279" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>6.6,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <Margins>9,0,0,0</Margins>
                      <Name>文本109</Name>
                      <Page isRef="40" />
                      <Parent isRef="275" />
                      <Text>{root_data_managerDailyMonthRespVO_statChannelList_data.occ}%</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本109>
                  </Components>
                  <Conditions isList="true" count="0" />
                  <DataRelationName>root_data_managerDailyMonthRespVO_statChannelList_data</DataRelationName>
                  <DataSourceName>root_data_managerDailyMonthRespVO_statChannelList_data</DataSourceName>
                  <Expressions isList="true" count="0" />
                  <FilterOn>False</FilterOn>
                  <Filters isList="true" count="0" />
                  <MasterComponent isRef="271" />
                  <Name>交叉_数据区10</Name>
                  <Page isRef="40" />
                  <Parent isRef="274" />
                  <Sort isList="true" count="0" />
                </交叉_数据区10>
              </Components>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Name>面板10</Name>
              <Page isRef="40" />
              <Parent isRef="271" />
            </面板10>
            <文本110 Ref="280" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.4,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>文本110</Name>
              <Page isRef="40" />
              <Parent isRef="271" />
              <Text>{root_data_managerDailyMonthRespVO_statChannelList.week}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本110>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>root_data_managerDailyMonthRespVO_statChannelList</DataSourceName>
          <Expressions isList="true" count="0" />
          <FilterOn>False</FilterOn>
          <Filters isList="true" count="0" />
          <Name>数据区6</Name>
          <Page isRef="40" />
          <Parent isRef="40" />
          <Sort isList="true" count="0" />
        </数据区6>
        <分组页脚5 Ref="281" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,46,51,0.8</ClientRectangle>
          <Components isList="true" count="2">
            <文本145 Ref="282" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,4.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本145</Name>
              <Page isRef="40" />
              <Parent isRef="281" />
              <Text>合计</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="283" type="DateFormat" isKey="true" />
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本145>
            <面板15 Ref="284" type="Panel" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.6,0,9,1</ClientRectangle>
              <Components isList="true" count="1">
                <交叉_数据区15 Ref="285" type="CrossDataBand" isKey="true">
                  <Brush>Transparent</Brush>
                  <BusinessObjectGuid isNull="true" />
                  <ClientRectangle>0,0,8.8,1</ClientRectangle>
                  <Components isList="true" count="4">
                    <文本146 Ref="286" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <Margins>9,0,0,0</Margins>
                      <Name>文本146</Name>
                      <Page isRef="40" />
                      <Parent isRef="285" />
                      <Text>{root_data_managerDailyMonthRespVO_statChannelNameList.nightNumSum}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本146>
                    <文本147 Ref="287" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>2.2,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Right</HorAlignment>
                      <Margins>0,5,0,0</Margins>
                      <Name>文本147</Name>
                      <Page isRef="40" />
                      <Parent isRef="285" />
                      <Text>{root_data_managerDailyMonthRespVO_statChannelNameList.totalFeeSum}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本147>
                    <文本148 Ref="288" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>4.4,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Right</HorAlignment>
                      <Margins>0,5,0,0</Margins>
                      <Name>文本148</Name>
                      <Page isRef="40" />
                      <Parent isRef="285" />
                      <Text>{root_data_managerDailyMonthRespVO_statChannelNameList.totalFeeSum/root_data_managerDailyMonthRespVO_statChannelNameList.nightNumSum}</Text>
                      <TextBrush>Black</TextBrush>
                      <TextFormat Ref="289" type="NumberFormat" isKey="true">
                        <DecimalDigits>2</DecimalDigits>
                        <GroupSeparator>,</GroupSeparator>
                        <NegativePattern>1</NegativePattern>
                      </TextFormat>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本148>
                    <文本149 Ref="290" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>6.6,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <Margins>9,0,0,0</Margins>
                      <Name>文本149</Name>
                      <Page isRef="40" />
                      <Parent isRef="285" />
                      <Text>{root_data_managerDailyMonthRespVO_statChannelNameList.nightNumSum/TotalRoomNumSum}</Text>
                      <TextBrush>Black</TextBrush>
                      <TextFormat Ref="291" type="PercentageFormat" isKey="true">
                        <DecimalDigits>2</DecimalDigits>
                        <GroupSeparator>,</GroupSeparator>
                        <NegativePattern>1</NegativePattern>
                        <PositivePattern>1</PositivePattern>
                        <Symbol>%</Symbol>
                      </TextFormat>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本149>
                  </Components>
                  <Conditions isList="true" count="0" />
                  <DataRelationName isNull="true" />
                  <DataSourceName>root_data_managerDailyMonthRespVO_statChannelNameList</DataSourceName>
                  <Expressions isList="true" count="0" />
                  <FilterOn>False</FilterOn>
                  <Filters isList="true" count="0" />
                  <Name>交叉_数据区15</Name>
                  <Page isRef="40" />
                  <Parent isRef="284" />
                  <Sort isList="true" count="0" />
                </交叉_数据区15>
              </Components>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Name>面板15</Name>
              <Page isRef="40" />
              <Parent isRef="281" />
            </面板15>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>分组页脚5</Name>
          <Page isRef="40" />
          <Parent isRef="40" />
        </分组页脚5>
      </Components>
      <Conditions isList="true" count="0" />
      <Expressions isList="true" count="0" />
      <Guid>d0be4e4c23a04bc29d4b0a1a4899e10b</Guid>
      <Margins>1,1,1,1</Margins>
      <Name>Page1</Name>
      <PageHeight>34.7</PageHeight>
      <PageWidth>53</PageWidth>
      <Report isRef="0" />
    </Page1>
  </Pages>
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>报表</ReportAlias>
  <ReportAuthor>xiao lin</ReportAuthor>
  <ReportChanged>7/17/2025 9:19:49 AM</ReportChanged>
  <ReportCreated>2/7/2025 9:56:26 AM</ReportCreated>
  <ReportFile>D:\11\2\pms-report\public\reports\managerMonth.mrt</ReportFile>
  <ReportGuid>1661177879d04c9e9861ab7ddc701f36</ReportGuid>
  <ReportName>报表</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2024.3.5.0</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class 报表 : Stimulsoft.Report.StiReport
    {
        public 报表()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
		#endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>