<i18n>
{
  "en": {
    "search": {
      "today": "Today",
      "thisWeek": "This Week",
      "thisMonth": "This Month",
      "bizDate": "Biz Date",
      "store": "Store",
      "operator": "Operator",
      "search": "Search",
      "selectDate": "Select Date",
      "selectStore": "Please select a store",
      "selectOperator": "Please select",
      "statType": "Statistics Type",
      "selectStatType": "Please select statistics type"
    },
    "statTypes": {
      "guest_src": "Guest Source",
      "check_in_type": "Check-in Type",
      "order_source": "Order Source",
      "room_type": "Room Type",
      "channel": "Channel"
    }
  },
  "zh": {
    "search": {
      "today": "今天",
      "thisWeek": "本周",
      "thisMonth": "本月",
      "bizDate": "营业日",
      "store": "门店",
      "operator": "操作员",
      "search": "查询",
      "selectDate": "选择日期",
      "selectStore": "请选择门店",
      "selectOperator": "请选择",
      "statType": "统计类型",
      "selectStatType": "请选择统计类型"
    },
    "statTypes": {
      "guest_src": "客源",
      "check_in_type": "入住类型",
      "order_source": "订单来源",
      "room_type": "房型",
      "channel": "渠道"
    }
  },
  "km": {
    "search": {
      "today": "ថ្ងៃនេះ",
      "thisWeek": "សប្តាហ៍នេះ",
      "thisMonth": "ខែនេះ",
      "bizDate": "កាលបរិច្ឆេទអាជីវកម្ម",
      "store": "ហាង",
      "operator": "ប្រតិបត្តិករ",
      "search": "ស្វែងរក",
      "selectDate": "ជ្រើសរើសកាលបរិច្ឆេទ",
      "selectStore": "សូមជ្រើសរើសហាង",
      "selectOperator": "សូមជ្រើសរើស",
      "statType": "ប្រភេទស្ថិតិ",
      "selectStatType": "សូមជ្រើសរើសប្រភេទស្ថិតិ"
    },
    "statTypes": {
      "guest_src": "ប្រភពភ្ញៀវ",
      "check_in_type": "ប្រភេទចុះឈ្មោះ",
      "order_source": "ប្រភពការបញ្ជាទិញ",
      "room_type": "ប្រភេទបន្ទប់",
      "channel": "ឆានែល"
    }
  }
}
</i18n>

<script setup lang="ts">
import { dayjs } from 'element-plus'
import { useI18n } from 'vue-i18n'
import {
  generateBusinessDailyReport,
  managerApi,
  managerDailyApi,
  simpleListApi
} from '@/api/modules/pms/shiftTimeApi.ts'
import useUserStore from '@/store/modules/user.ts'
import { getFirstDayOfMonth, getMonday } from '@/utils/tool'
import { ReportUtils } from '@/utils/report-utils.ts'

const userStore = useUserStore()
const { t } = useI18n()
const data = ref({
  searchFold: false,
  // Auto-adjust table height
  tableAutoHeight: true,
  search: {
    gcode: userStore.gcode,
    hcode: userStore.hcode,
    bizDate: dayjs().subtract(1, 'day').toDate(), // Set to previous day
    statTypeList: [],
  },
})

const simpleList = ref<{ hcode: string, hname: string }[]>([])
const statTypeList = computed(() => [
  { statType: 'guest_src', statTypeName: t('statTypes.guest_src') },
  { statType: 'check_in_type', statTypeName: t('statTypes.check_in_type') },
  { statType: 'order_source', statTypeName: t('statTypes.order_source') },
  { statType: 'room_type', statTypeName: t('statTypes.room_type') },
  { statType: 'channel', statTypeName: t('statTypes.channel') },
])
const shortcuts = [
  {
    text: t('search.today'),
    value: () => {
      const end = new Date()
      const start = new Date()
      return [start, end]
    },
  },
  {
    text: t('search.thisWeek'),
    value: () => {
      const end = new Date()
      const start = getMonday()
      return [start, end]
    },
  },
  {
    text: t('search.thisMonth'),
    value: () => {
      const end = new Date()
      const start = getFirstDayOfMonth()
      return [start, end]
    },
  },
]

function onSearch() {
  generateBusinessDailyReport({
    gcode: userStore.gcode,
    hcode: data.value.search.hcode,
    bizDate: dayjs(data.value.search.bizDate).format('YYYY-MM-DD'),
    statTypeList: data.value.search.statTypeList.join(','),
  }).then((res) => {
    nextTick(() => {
      setJson(res)
    })
  }).catch()
}

async function setJson(json: any) {
  const viewer = ReportUtils.getViewer('businessDailyStiViewer')
  const report = ReportUtils.getReportObj('reports/businessDailyReport.mrt', json)

  ReportUtils.loadData(viewer, report, 'businessDailyReport')
}

async function getsimpleList() {
  const params = {
    gcode: userStore.gcode,
    hcode: userStore.hcode,
  }
  await simpleListApi(params).then((res) => {
    simpleList.value = res.data
    data.value.search.hcode = userStore.hcode
  })
}

onMounted(async () => {
  await getsimpleList()
  onSearch()
})
</script>

<template>
  <div :class="{ 'absolute-container': data.tableAutoHeight }">
    <page-main>
      <search-bar :fold="data.searchFold" :show-toggle="false">
        <el-form :model="data.search" size="default" label-width="80px" inline-message inline class="search-form">
          <el-form-item :label="t('search.bizDate')">
            <el-date-picker
              v-model="data.search.bizDate" type="date" :placeholder="t('search.selectDate')" style="margin-left: 10px;"
              :shortcuts="shortcuts" :clearable="false"
            />
          </el-form-item>
          <el-form-item :label="t('search.store')" style="width: 300px;">
            <el-select v-model="data.search.hcode" :placeholder="t('search.selectStore')">
              <el-option v-for="item in simpleList" :key="item.hcode" :label="item.hname" :value="item.hcode" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSearch">
              {{ t('search.search') }}
            </el-button>
          </el-form-item>
        </el-form>
      </search-bar>
      <div id="businessDailyReport" />
    </page-main>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-radio) {
  margin-right: 14px;
}
</style>
