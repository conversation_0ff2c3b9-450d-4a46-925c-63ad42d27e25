<script setup lang="ts">
import { ElMessage, ElMessageBox } from 'element-plus'
import FormMode from './components/FormMode/index.vue'
import api from '@/api/modules/{{ kebabCase name }}'

defineOptions({
  name: '{{ properCase name }}',
})

const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()

const tableRef = ref()
const data = ref({
  loading: false,
  // 表格是否自适应高度
  tableAutoHeight: false,
  /**
   * 详情展示模式
   * dialog 对话框
   * drawer 抽屉
   */
  formMode: 'dialog' as 'dialog' | 'drawer',
  // 详情
  formModeProps: {
    visible: false,
    id: '',
  },
  // 搜索
  search: {
    title: '',
  },
  // 批量操作
  batch: {
    enable: false,
    selectionDataList: [],
  },
  // 列表数据
  dataList: [],
})

onMounted(() => {
  getDataList()
})

function getDataList() {
  data.value.loading = true
  const params = {
    ...getParams(),
    ...(data.value.search.title && { title: data.value.search.title }),
  }
  api.list(params).then((res: any) => {
    data.value.loading = false
    data.value.dataList = res.data.list
    pagination.value.total = res.data.total
  })
}

// 每页数量切换
function sizeChange(size: number) {
  onSizeChange(size).then(() => getDataList())
}

// 当前页码切换（翻页）
function currentChange(page = 1) {
  onCurrentChange(page).then(() => getDataList())
}

// 字段排序
function sortChange({ prop, order }: { prop: string, order: string }) {
  onSortChange(prop, order).then(() => getDataList())
}

function onCreate() {
  data.value.formModeProps.id = ''
  data.value.formModeProps.visible = true
}

function onEdit(row: any) {
  data.value.formModeProps.id = row.id
  data.value.formModeProps.visible = true
}

function onDel(row: any) {
  ElMessageBox.confirm(`确认删除「${row.title}」吗？`, '确认信息').then(() => {
    api.delete(row.id).then(() => {
      getDataList()
      ElMessage.success({
        message: '模拟删除成功',
        center: true,
      })
    })
  }).catch(() => {})
}
</script>

<template>
  <div>
    <page-main>
      <search-bar :show-toggle="false">
        <template #default="{ fold, toggle }">
          <el-form :model="data.search" size="default" label-width="100px" inline inline-message class="search-form">
            <el-form-item label="标题">
              <el-input v-model="data.search.title" placeholder="请输入标题，支持模糊查询" clearable @keydown.enter="getDataList" @clear="getDataList" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="currentChange()">
                <template #icon>
                  <SvgIcon name="i-ep:search" />
                </template>
                筛选
              </el-button>
              <el-button link @click="toggle">
                <template #icon>
                  <SvgIcon :name="fold ? 'i-ep:caret-bottom' : 'i-ep:caret-top' " />
                </template>
                \{{ fold ? '展开' : '收起' }}
              </el-button>
            </el-form-item>
          </el-form>
        </template>
      </search-bar>
      <el-divider border-style="dashed" />
      <el-space wrap>
        <el-button type="primary" size="default" @click="onCreate">
          <template #icon>
            <SvgIcon name="i-ep:plus" />
          </template>
          新增{{ cname }}
        </el-button>
        <el-button v-if="data.batch.enable" size="default" :disabled="!data.batch.selectionDataList.length">
          单个批量操作按钮
        </el-button>
        <el-button-group v-if="data.batch.enable">
          <el-button size="default" :disabled="!data.batch.selectionDataList.length">
            批量操作按钮组1
          </el-button>
          <el-button size="default" :disabled="!data.batch.selectionDataList.length">
            批量操作按钮组2
          </el-button>
        </el-button-group>
      </el-space>
      <el-table ref="tableRef" v-loading="data.loading" class="my-4" :data="data.dataList" border stripe highlight-current-row height="100%" @sort-change="sortChange" @selection-change="data.batch.selectionDataList = $event">
        <el-table-column v-if="data.batch.enable" type="selection" align="center" fixed />
        <el-table-column prop="title" label="标题" />
        <el-table-column label="操作" width="250" align="center" fixed="right">
          <template #default="scope">
            <el-button type="primary" size="small" plain @click="onEdit(scope.row)">
              编辑
            </el-button>
            <el-button type="danger" size="small" plain @click="onDel(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size" :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false" class="pagination" background @size-change="sizeChange" @current-change="currentChange" />
    </page-main>
    <FormMode :id="data.formModeProps.id" v-model="data.formModeProps.visible" :mode="data.formMode" @success="getDataList" />
  </div>
</template>

<style scoped>
.absolute-container {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  /* 让 page-main 的高度自适应 */
  .page-main {
    flex: 1;
    overflow: auto;

    :deep(.main-container) {
      flex: 1;
      overflow: auto;
      display: flex;
      flex-direction: column;
    }
  }
}

.page-header {
  margin-bottom: 0;
}

.page-main {
  .search-form {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(330px, 1fr));
    margin-bottom: -18px;

    :deep(.el-form-item) {
      grid-column: auto / span 1;

      &:last-child {
        grid-column-end: -1;

        .el-form-item__content {
          justify-content: flex-end;
        }
      }
    }
  }

  .el-divider {
    margin-inline: -20px;
    width: calc(100% + 40px);
  }
}
</style>
