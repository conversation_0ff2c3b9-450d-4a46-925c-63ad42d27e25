/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ActionContainer: typeof import('./../components/ActionContainer/index.vue')['default']
    Auth: typeof import('./../components/Auth/index.vue')['default']
    AuthAll: typeof import('./../components/AuthAll/index.vue')['default']
    Chip: typeof import('./../components/Chip/index.vue')['default']
    ColorfulCard: typeof import('./../components/ColorfulCard/index.vue')['default']
    ComponentBasicExampleAlert: typeof import('./../components/ComponentBasicExampleAlert/index.vue')['default']
    ComponentPluginExampleAlert: typeof import('./../components/ComponentPluginExampleAlert/index.vue')['default']
    FileUpload: typeof import('./../components/FileUpload/index.vue')['default']
    HBadge: typeof import('./../views/ui-kit/HBadge.vue')['default']
    HButton: typeof import('./../views/ui-kit/HButton.vue')['default']
    HCheckList: typeof import('./../views/ui-kit/HCheckList.vue')['default']
    HDialog: typeof import('./../views/ui-kit/HDialog.vue')['default']
    HDropdown: typeof import('./../views/ui-kit/HDropdown.vue')['default']
    HDropdownMenu: typeof import('./../views/ui-kit/HDropdownMenu.vue')['default']
    HInput: typeof import('./../views/ui-kit/HInput.vue')['default']
    HKbd: typeof import('./../views/ui-kit/HKbd.vue')['default']
    HSelect: typeof import('./../views/ui-kit/HSelect.vue')['default']
    HSlideover: typeof import('./../views/ui-kit/HSlideover.vue')['default']
    HTabList: typeof import('./../views/ui-kit/HTabList.vue')['default']
    HToggle: typeof import('./../views/ui-kit/HToggle.vue')['default']
    HTooltip: typeof import('./../views/ui-kit/HTooltip.vue')['default']
    IconPicker: typeof import('./../components/IconPicker/index.vue')['default']
    ImagePreview: typeof import('./../components/ImagePreview/index.vue')['default']
    ImagesUpload: typeof import('./../components/ImagesUpload/index.vue')['default']
    ImageUpload: typeof import('./../components/ImageUpload/index.vue')['default']
    LoginForm: typeof import('./../components/LoginForm/index.vue')['default']
    PageHeader: typeof import('./../components/PageHeader/index.vue')['default']
    PageMain: typeof import('./../components/PageMain/index.vue')['default']
    PcasCascader: typeof import('./../components/PcasCascader/index.vue')['default']
    RegisterForm: typeof import('./../components/RegisterForm/index.vue')['default']
    ResetPasswordForm: typeof import('./../components/ResetPasswordForm/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SearchBar: typeof import('./../components/SearchBar/index.vue')['default']
    Sparkline: typeof import('./../components/Sparkline/index.vue')['default']
    StorageBox: typeof import('./../components/StorageBox/index.vue')['default']
    SvgIcon: typeof import('./../components/SvgIcon/index.vue')['default']
    SystemInfo: typeof import('./../components/SystemInfo/index.vue')['default']
    Trend: typeof import('./../components/Trend/index.vue')['default']
  }
}
