<i18n>
{
  "zh": {
    "search": {
      "store": "门店",
      "selectStore": "请选择门店",
      "bizDate": "营业日",
      "selectBizDate": "选择日期",
      "roomType": "房型",
      "selectRoomType": "请选择房型",
      "building": "楼栋",
      "selectBuilding": "请选择楼栋",
      "query": "查询",
      "today": "今天",
      "thisWeek": "本周",
      "thisMonth": "本月",
      "thisQuarter": "本季",
      "thisYear": "本年",
      "startDate": "开始日期",
      "endDate": "结束日期",
      "to": "至"
    }
  },
  "en": {
    "search": {
      "store": "Store",
      "selectStore": "Select Store",
      "bizDate": "Biz Date",
      "selectBizDate": "Select Date",
      "roomType": "Room Type",
      "selectRoomType": "Select Room Type",
      "building": "Building",
      "selectBuilding": "Select Building",
      "query": "Search",
      "today": "Today",
      "thisWeek": "This Week",
      "thisMonth": "This Month",
      "thisQuarter": "This Quarter",
      "thisYear": "This Year",
      "startDate": "Start Date",
      "endDate": "End Date",
      "to": "to"
    }
  },
  "km": {
    "search": {
      "store": "ហាង",
      "selectStore": "សូមជ្រើសរើសហាង",
      "bizDate": "ថ្ងៃធ្វើអាជីវកម្ម",
      "selectBizDate": "ជ្រើសរើសកាលបរិច្ឆេទ",
      "roomType": "ប្រភេទបន្ទប់",
      "selectRoomType": "សូមជ្រើសរើសប្រភេទបន្ទប់",
      "building": "អាគារ",
      "selectBuilding": "សូមជ្រើសរើសអាគារ",
      "query": "ស្វែងរក",
      "today": "ថ្ងៃនេះ",
      "thisWeek": "សប្តាហ៍នេះ",
      "thisMonth": "ខែនេះ",
      "thisQuarter": "ត្រីមាសនេះ",
      "thisYear": "ឆ្នាំនេះ",
      "startDate": "កាលបរិច្ឆេទចាប់ផ្តើម",
      "endDate": "កាលបរិច្ឆេទបញ្ចប់",
      "to": "ទៅ"
    }
  }
}
</i18n>

<script setup lang="ts">
import { dayjs } from 'element-plus'
import { useI18n } from 'vue-i18n'
import {
  buildFloorTreeApi,
  roomStatApi,
  roomTypeSimpleListApi,
  simpleListApi,
} from '@/api/modules/pms/shiftTimeApi.ts'
import useUserStore from '@/store/modules/user.ts'
import { getFirstDayOfMonth, getMonday } from '@/utils/tool'
import { ReportUtils } from '@/utils/report-utils.ts'

const userStore = useUserStore()
const { t } = useI18n()
const route = useRoute()

const data = ref({
  searchFold: false,
  // 表格是否自适应高度
  tableAutoHeight: true,
  labelState: '1',
  bizDate: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')] as any, // 默认为当天
  buildingCode: '', // 只保留楼栋，不用楼层
  search: {
    gcode: userStore.gcode,
    hcode: userStore.hcode,
    rtCode: '',
  },
})

const shortcuts = [
  {
    text: t('search.today'),
    value: () => {
      const end = new Date()
      const start = new Date()
      return [start, end]
    },
  },
  {
    text: t('search.thisWeek'),
    value: () => {
      const end = new Date()
      const start = getMonday()
      return [start, end]
    },
  },
  {
    text: t('search.thisMonth'),
    value: () => {
      const end = new Date()
      const start = getFirstDayOfMonth()
      return [start, end]
    },
  },
]

// 数据字典选项
const simpleList = ref<{ hcode: string, hname: string }[]>([])
const roomTypeList = ref<{ rtCode: string, rtName: string }[]>([])
const buildingList = ref<{ code: string, name: string }[]>([])

// 监听门店变化，重新查询房型和楼栋
watch(() => data.value.search.hcode, (newHcode) => {
  if (newHcode) {
    getDictData()
  }
})
const loading = ref(false)
function onSearch() {
  loading.value = true
  roomStatApi({
    ...data.value.search,
    startDate: dayjs(data.value.bizDate[0]).format('YYYY-MM-DD'),
    endDate: dayjs(data.value.bizDate[1]).format('YYYY-MM-DD'),
    buildingCode: data.value.buildingCode,
  }).then((res) => {
    nextTick(() => {
      setJson(res)
      loading.value = false
    })
  }).catch(() => {
    loading.value = false
  })
}

async function setJson(json: any) {
  const viewer = ReportUtils.getViewer('roomStatStiViewer')
  const report = ReportUtils.getReportObj('reports/roomStat.mrt', json)
  ReportUtils.loadData(viewer, report, 'roomStat')
}

// 获取门店列表
async function getSimpleList() {
  const params = {
    gcode: userStore.gcode,
    hcode: data.value.search.hcode,
  }
  await simpleListApi(params).then((res) => {
    simpleList.value = res.data
  })
}

// 获取数据字典
async function getDictData() {
  // 获取房型列表
  roomTypeSimpleListApi({
    gcode: userStore.gcode,
    hcode: data.value.search.hcode,
  }).then((res) => {
    roomTypeList.value = res.data || []
  })

  // 获取楼栋列表（只获取楼栋）
  buildFloorTreeApi({
    gcode: userStore.gcode,
    hcode: data.value.search.hcode,
  }).then((res) => {
    // 只提取楼栋数据，不包含楼层
    if (res.data && Array.isArray(res.data)) {
      buildingList.value = res.data.map((item) => {
        return {
          code: item.code,
          name: item.name,
        }
      })
    }
    else {
      buildingList.value = []
    }
  })
}

async function getQuery() {
  const query = route.query
  if (!query || JSON.stringify(query) === '{}') {
    return
  }
  if (!query.name || query.name !== 'roomStat') {
    return
  }
  if (query && query.startDate && query.endDate) {
    data.value.bizDate = [query.startDate, query.endDate]
  }
}

onMounted(async () => {
  await getQuery()
  await getSimpleList()
  await getDictData()
  onSearch()
})

// -----------最多只能选择一个月，且只能选今天之前的-----------------
const startVal = ref(null)
/** 选择第一个时间的当月天数 */
const daysInMonth = ref(0)
function calendarChange(e) {
  if (e[0] && !e[1]) {
    startVal.value = e[0]
    daysInMonth.value = dayjs(e[0]).daysInMonth()
  }
}
/** 选择第一个时间 */
function visibleChange(e) {
  if (e) {
    startVal.value = null
  }
}
/** 禁止选择一个上下自然月之外时间 */
function disabledDate(time) {
  const flag_yestodayAfter = dayjs(time).isAfter(dayjs().subtract(0, 'day'), 'day')
  if (!startVal.value) {
    return flag_yestodayAfter
  }
  const range = [dayjs(startVal.value).subtract(daysInMonth.value, 'day'), dayjs(startVal.value).add(daysInMonth.value, 'day')]
  const cur = dayjs(time)
  return cur < range[0] || cur > range[1] || flag_yestodayAfter
}
// -----------最多只能选择一个月，且只能选今天之前的-----------------
</script>

<template>
  <div :class="{ 'absolute-container': data.tableAutoHeight }">
    <page-main>
      <search-bar :fold="data.searchFold" :show-toggle="false">
        <el-form :model="data.search" size="default" label-width="80px" inline-message inline class="search-form">
          <el-form-item :label="t('search.store')" style="width: 260px;">
            <el-select v-model="data.search.hcode" :placeholder="t('search.selectStore')" class="w-[180px]">
              <el-option v-for="item in simpleList" :key="item.hcode" :label="item.hname" :value="item.hcode" />
            </el-select>
          </el-form-item>
          <el-form-item :label="t('search.bizDate')" style="width: 440px;">
            <el-date-picker
              v-model="data.bizDate" type="daterange" :range-separator="t('search.to')"
              :start-placeholder="t('search.startDate')"
              :end-placeholder="t('search.endDate')"
              :shortcuts="shortcuts" clearable
              :disabled-date="disabledDate"
              @calendar-change="calendarChange"
              @visible-change="visibleChange"
            />
          </el-form-item>
          <el-form-item :label="t('search.building')" style="width: 320px;">
            <el-select v-model="data.buildingCode" :placeholder="t('search.selectBuilding')" clearable class="w-[240px]">
              <el-option v-for="item in buildingList" :key="item.code" :label="item.name" :value="item.code" />
            </el-select>
          </el-form-item>
          <el-form-item :label="t('search.roomType')" style="width: 260px;">
            <el-select v-model="data.search.rtCode" :placeholder="t('search.selectRoomType')" clearable class="w-[180px]">
              <el-option v-for="item in roomTypeList" :key="item.rtCode" :label="item.rtName" :value="item.rtCode" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSearch">
              {{ t('search.query') }}
            </el-button>
          </el-form-item>
        </el-form>
      </search-bar>
      <div
        id="roomStat"
      >
        <el-skeleton :loading="loading" animated :rows="30" />
      </div>
    </page-main>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-radio) {
  margin-right: 14px;
  overflow: scroll auto;
  white-space: nowrap;
}

:deep(.el-date-editor.el-input) {
  width: 160px;
}

:deep(.el-date-editor--daterange) {
  width: 320px;
}
</style>
