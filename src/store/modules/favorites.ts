import useSettingsStore from './settings'
import useUserStore from './user'
import useMenuStore from './menu'
import apiUser from '@/api/modules/user'
import storage from '@/utils/storage'

const useFavoritesStore = defineStore(
  // 唯一ID
  'favorites',
  () => {
    const settingsStore = useSettingsStore()
    const userStore = useUserStore()
    const menuStore = useMenuStore()

    const list = ref<Favorites.recordRaw[]>([])

    function getSourceList(arr: Menu.recordRaw[]) {
      const list: Menu.recordRaw[] = []
      arr.forEach((item) => {
        if (item.children && item.children.length > 0) {
          list.push(...getSourceList(item.children))
        }
        else {
          list.push(item)
        }
      })
      return list
    }

    const flatSidebarMenu = computed(() => {
      const list: Menu.recordRaw[] = []
      menuStore.menus.forEach((item) => {
        list.push(...getSourceList(item.children))
      })
      return list
    })

    // 判断路由是否可添加进收藏夹
    function canAdd(windowName: Favorites.recordRaw['windowName']) {
      return flatSidebarMenu.value.some(item => item.windowName === windowName)
    }
    // 判断路由是否已经添加进收藏夹
    function isAdd(windowName: Favorites.recordRaw['windowName']) {
      return list.value.some(item => item.windowName === windowName)
    }
    // 新增收藏
    function add(windowName: Favorites.recordRaw['windowName']) {
      const fineItem = flatSidebarMenu.value.find(item => item.windowName === windowName)
      if (fineItem && !list.value.find(item => item.windowName === windowName)) {
        list.value.push({
          windowName,
          title: fineItem.title,
        })
      }
      updateStorage()
    }
    // 删除收藏
    function remove(windowName: Favorites.recordRaw['windowName']) {
      list.value = list.value.filter((item) => {
        return item.windowName !== windowName
      })
      updateStorage()
    }
    // 拖拽排序
    function sort(newIndex: number, oldIndex: number) {
      list.value.splice(newIndex, 0, list.value.splice(oldIndex, 1)[0])
      updateStorage()
    }
    // 更新 storage 数据
    async function updateStorage() {
      if (settingsStore.settings.favorites.storageTo === 'local') {
        const favoritesData = storage.local.has('favoritesData') ? JSON.parse(storage.local.get('favoritesData') as string) : {}
        favoritesData[userStore.account] = list.value
        storage.local.set('favoritesData', JSON.stringify(favoritesData))
      }
      else if (settingsStore.settings.favorites.storageTo === 'server') {
        await apiUser.favoritesEdit(JSON.stringify(list.value))
      }
    }
    // 根据 storage 数据复原当前帐号的标签页
    async function recoveryStorage() {
      if (settingsStore.settings.favorites.storageTo === 'local') {
        if (storage.local.has('favoritesData')) {
          list.value = JSON.parse(storage.local.get('favoritesData') as string)[userStore.account] || []
        }
      }
      else if (settingsStore.settings.favorites.storageTo === 'server') {
        const res = await apiUser.favorites()
        list.value = JSON.parse(res.data.favorites || '[]')
      }
    }

    return {
      list,
      canAdd,
      isAdd,
      add,
      remove,
      sort,
      recoveryStorage,
    }
  },
)

export default useFavoritesStore
