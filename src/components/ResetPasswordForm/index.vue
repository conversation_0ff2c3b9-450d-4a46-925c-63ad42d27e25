<i18n>
{
  "zh-cn": {
    "intro": "忘记密码了? 🔒",
    "login": "去登录",
    "form": {
      "account": "用户名",
      "captcha": "验证码",
      "sendCaptcha": "发送验证码",
      "newPassword": "新密码",
      "confirm": "确认"
    },
    "rules": {
      "account": "请输入用户名",
      "captcha": "请输入验证码",
      "newPassword": "请输入新密码",
      "newPasswordLength": "密码长度为6到18位"
    }
  },
  "zh-tw": {
    "intro": "忘記密碼了? 🔒",
    "login": "去登錄",
    "form": {
      "account": "用戶名",
      "captcha": "驗證碼",
      "sendCaptcha": "發送驗證碼",
      "newPassword": "新密碼",
      "confirm": "確認"
    },
    "rules": {
      "account": "請輸入用戶名",
      "captcha": "請輸入驗證碼",
      "newPassword": "請輸入新密碼",
      "newPasswordLength": "密碼長度為6到18位"
    }
  },
  "en": {
    "intro": "Forgot password? 🔒",
    "login": "Go to login",
    "form": {
      "account": "Account",
      "captcha": "Captcha",
      "sendCaptcha": "Send Captcha",
      "newPassword": "New Password",
      "confirm": "Confirm"
    },
    "rules": {
      "account": "Please enter account",
      "captcha": "Please enter captcha",
      "newPassword": "Please enter new password",
      "newPasswordLength": "The length of the password is 6 to 18 bits"
    }
  }
}
</i18n>

<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'
import storage from '@/utils/storage'

defineOptions({
  name: 'ResetPasswordForm',
})

const props = defineProps<{
  account?: string
}>()

const emits = defineEmits<{
  onLogin: [account: string]
  onResetPassword: [account: string]
}>()

const { t } = useI18n()

const loading = ref(false)

const formRef = ref<FormInstance>()
const form = ref({
  account: props.account ?? storage.local.get('login_account') ?? '',
  captcha: '',
  newPassword: '',
})
const rules = ref<FormRules>({
  account: [
    { required: true, trigger: 'blur', message: () => t('rules.account') },
  ],
  captcha: [
    { required: true, trigger: 'blur', message: () => t('rules.captcha') },
  ],
  newPassword: [
    { required: true, trigger: 'blur', message: () => t('rules.newPassword') },
    { min: 6, max: 18, trigger: 'blur', message: () => t('rules.newPasswordLength') },
  ],
})
function handleReset() {
  ElMessage({
    message: '重置密码仅提供界面演示，无实际功能，需开发者自行扩展',
    type: 'info',
  })
  formRef.value?.validate((valid) => {
    if (valid) {
      // 这里编写业务代码
      emits('onResetPassword', form.value.account)
    }
  })
}
</script>

<template>
  <ElForm ref="formRef" :model="form" :rules="rules" class="min-h-500px w-full flex-col-stretch-center p-12">
    <h3 class="mb-8 text-xl color-[var(--el-text-color-primary)] font-bold">
      {{ t('intro') }}
    </h3>
    <div>
      <ElFormItem prop="account">
        <ElInput v-model="form.account" :placeholder="t('form.account')" type="text" tabindex="1">
          <template #prefix>
            <SvgIcon name="i-ri:user-3-fill" />
          </template>
        </ElInput>
      </ElFormItem>
      <ElFormItem prop="captcha">
        <ElInput v-model="form.captcha" :placeholder="t('form.captcha')" type="text" tabindex="2">
          <template #prefix>
            <SvgIcon name="i-ic:baseline-verified-user" />
          </template>
          <template #append>
            <ElButton>{{ t('form.sendCaptcha') }}</ElButton>
          </template>
        </ElInput>
      </ElFormItem>
      <ElFormItem prop="newPassword">
        <ElInput v-model="form.newPassword" type="password" :placeholder="t('form.newPassword')" tabindex="3" show-password>
          <template #prefix>
            <SvgIcon name="i-ri:lock-2-fill" />
          </template>
        </ElInput>
      </ElFormItem>
    </div>
    <ElButton :loading="loading" type="primary" size="large" style="width: 100%; margin-top: 20px;" @click.prevent="handleReset">
      {{ t('form.confirm') }}
    </ElButton>
    <div class="mt-4 flex-center gap-2 text-sm color-[var(--el-text-color-secondary)]">
      <ElLink type="primary" :underline="false" @click="emits('onLogin', form.account)">
        {{ t('login') }}
      </ElLink>
    </div>
  </ElForm>
</template>

<style scoped>
:deep(input[type="password"]::-ms-reveal) {
  display: none;
}

.el-form-item {
  margin-bottom: 24px;

  :deep(.el-input) {
    width: 100%;
    height: 48px;
    line-height: inherit;

    input {
      height: 48px;
    }

    .el-input__prefix,
    .el-input__suffix {
      display: flex;
      align-items: center;
    }

    .el-input__prefix {
      left: 10px;
    }

    .el-input__suffix {
      right: 10px;
    }
  }
}
</style>
