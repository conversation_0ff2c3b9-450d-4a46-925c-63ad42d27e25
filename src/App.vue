<script setup lang="ts">
import hotkeys from 'hotkeys-js'
import { useI18n } from 'vue-i18n'
import eventBus from './utils/eventBus'
import Provider from './ui-provider/index.vue'
import dayjs from '@/utils/dayjs'
import useSettingsStore from '@/store/modules/settings'

const settingsStore = useSettingsStore()

const { locale } = useI18n()

watch(() => settingsStore.title, () => {
  document.title = import.meta.env.VITE_APP_TITLE
}, {
  immediate: true,
})

onMounted(() => {
  hotkeys('alt+i', () => {
    eventBus.emit('global-system-info-toggle')
  })
})

watch(() => settingsStore.lang, () => {
  locale.value = settingsStore.lang
})

watch(() => settingsStore.lang, () => {
  switch (settingsStore.lang) {
    case 'zh-cn':
      dayjs.locale('zh-cn')
      break
    // case 'zh-tw':
    //   dayjs.locale('zh-tw')
    //   break
    case 'en':
      dayjs.locale('en')
      break
    case 'km':
      dayjs.locale('km')
      break
  }
}, {
  immediate: true,
})
</script>

<template>
  <Provider>
    <RouterView />
    <SystemInfo />
  </Provider>
</template>
