import { defaultsDeep } from 'lodash-es'
import settingsDefault from '@/settings.default'

const globalSettings: Settings.all = {
  app: {
    enablePermission: false,
    enableErrorLog: true,
    enableWatermark: true,
  },
  userPreferences: {
    enable: true,
  },
  menu: {
    isRounded: true,
    style: 'dot',
    enableSubMenuCollapseButton: true,
    subMenuUniqueOpened: false,
    enableHotkeys: true,
    mode: 'head', // 顶部模式
  },
  toolbar: {
    previewWindows: false,
    favorites: true,
    navSearch: true,
    notification: false,
    i18n: true,
    fullscreen: true,
    colorScheme: false,
  },
  copyright: {
    enable: false,
    dates: '2023-present',
    company: '长沙奇智信息技术有限公司',
    website: 'https://www.dingdanyilai.com',
  },
}

export default defaultsDeep(globalSettings, settingsDefault) as RecursiveRequired<Settings.all>
