<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <CalculationMode>Interpretation</CalculationMode>
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="1">
      <roomClassPredictionReport Ref="2" type="Stimulsoft.Report.Dictionary.StiJsonDatabase" isKey="true">
        <Alias>roomClassPredictionReport</Alias>
        <HeadersString />
        <Key />
        <Name>roomClassPredictionReport</Name>
        <PathData>C:\Users\<USER>\Desktop\需求文档\2\房类预测\roomClassPredictionReport.json</PathData>
      </roomClassPredictionReport>
    </Databases>
    <DataSources isList="true" count="3">
      <root Ref="3" type="DataTableSource" isKey="true">
        <Alias>root</Alias>
        <Columns isList="true" count="4">
          <value>code,System.Decimal</value>
          <value>data,System.String</value>
          <value>msg,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>fb1dc4a1978d4730a3441180508c75a7</Key>
        <Name>root</Name>
        <NameInSource>roomClassPredictionReport.root</NameInSource>
      </root>
      <root_data Ref="4" type="DataTableSource" isKey="true">
        <Alias>root_data</Alias>
        <Columns isList="true" count="7">
          <value>hname,System.String</value>
          <value>startDate,System.String</value>
          <value>endDate,System.String</value>
          <value>lastSelectTime,System.String</value>
          <value>operator,System.String</value>
          <value>list,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>059882be8ec8425ba60ff8c4bdcdf0c3</Key>
        <Name>root_data</Name>
        <NameInSource>roomClassPredictionReport.root_data</NameInSource>
      </root_data>
      <root_data_list Ref="5" type="DataTableSource" isKey="true">
        <Alias>root_data_list</Alias>
        <Columns isList="true" count="18">
          <value>predictDate,System.String</value>
          <value>week,System.Decimal</value>
          <value>hcode,System.String</value>
          <value>hname,System.String</value>
          <value>hotelRoomTypeCode,System.String</value>
          <value>hotelRoomTypeName,System.String</value>
          <value>roomCount,System.Decimal</value>
          <value>repairRoomCount,System.Decimal</value>
          <value>canSellCount,System.Decimal</value>
          <value>soldCount,System.Decimal</value>
          <value>predictRevenue,System.Decimal</value>
          <value>predictOccupancyRate,System.Decimal</value>
          <value>predictAvgRoomPrice,System.Decimal</value>
          <value>predictRevPar,System.Decimal</value>
          <value>bookingCount,System.Decimal</value>
          <value>inhouseCount,System.Decimal</value>
          <value>canOverCount,System.Decimal</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>a1151da1fcc44753a7b3a1156d3445b5</Key>
        <Name>root_data_list</Name>
        <NameInSource>roomClassPredictionReport.root_data_list</NameInSource>
      </root_data_list>
    </DataSources>
    <Relations isList="true" count="2">
      <root Ref="6" type="DataRelation" isKey="true">
        <Alias>root</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="4" />
        <Dictionary isRef="1" />
        <Name>root</Name>
        <NameInSource>root_data</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>data</value>
        </ParentColumns>
        <ParentSource isRef="3" />
      </root>
      <root_data Ref="7" type="DataRelation" isKey="true">
        <Alias>root_data</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="5" />
        <Dictionary isRef="1" />
        <Name>root_data</Name>
        <NameInSource>root_data_list</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>list</value>
        </ParentColumns>
        <ParentSource isRef="4" />
      </root_data>
    </Relations>
    <Report isRef="0" />
    <Resources isList="true" count="0" />
    <UserFunctions isList="true" count="0" />
    <Variables isList="true" count="3">
      <value>,soldCount,soldCount,,System.Decimal,_x0030_,False,False,False,False</value>
      <value>,totalRoom,totalRoom,,System.Decimal,_x0030_,False,False,False,False</value>
      <value>,predictRevenue,predictRevenue,,System.Decimal,_x0030_,False,False,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <Key>ada4f4c89f6e4221b0a694dddad01515</Key>
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="8" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="5">
        <页眉1 Ref="9" type="PageHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,30,0.8</ClientRectangle>
          <Components isList="true" count="2">
            <Text2 Ref="10" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,5.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Name>Text2</Name>
              <Page isRef="8" />
              <Parent isRef="9" />
              <Text>{root_data.hname}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text2>
            <Text6 Ref="11" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>24.4,0,5.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Name>Text6</Name>
              <Page isRef="8" />
              <Parent isRef="9" />
              <Text>第{PageNumber}页,共{TotalPageCount}页</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text6>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>页眉1</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
        </页眉1>
        <报表标题区1 Ref="12" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,2,30,1.8</ClientRectangle>
          <Components isList="true" count="2">
            <Text1 Ref="13" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>13,0,5.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,12,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text1</Name>
              <Page isRef="8" />
              <Parent isRef="12" />
              <Text>房类预测报表</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text1>
            <Text5 Ref="14" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1,19.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Name>Text5</Name>
              <Page isRef="8" />
              <Parent isRef="12" />
              <Text>门店:{root_data.hname}   日期:{root_data.startDate} - {root_data.endDate}  最后查询时间：{root_data.lastSelectTime}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text5>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>报表标题区1</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
        </报表标题区1>
        <页眉2 Ref="15" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,4.6,30,0.8</ClientRectangle>
          <Components isList="true" count="13">
            <Text3 Ref="16" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,1.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text3</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>序号</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text3>
            <文本3 Ref="17" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.4,0,2.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本3</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>酒店/房型</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本3>
            <文本4 Ref="18" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.2,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本4</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>客房数</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本4>
            <文本5 Ref="19" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.6,0,2.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本5</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>预测日期</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本5>
            <文本7 Ref="20" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.8,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本7</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>已售房</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本7>
            <文本6 Ref="21" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9.4,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本6</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>维修房</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本6>
            <文本15 Ref="22" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>11.6,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本15</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>可售房</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本15>
            <文本21 Ref="23" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>16,0,2.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本21</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>预计营收</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本21>
            <文本2 Ref="24" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>18.6,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本2</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>预计出租率</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本2>
            <文本8 Ref="25" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>20.8,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本8</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>预计平均房价</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本8>
            <文本16 Ref="26" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>23,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本16</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>预计RevPar</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本16>
            <文本18 Ref="27" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>25.2,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本18</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>预订房</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本18>
            <文本23 Ref="28" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>27.4,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本23</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>在住房</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本23>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>页眉2</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
        </页眉2>
        <数据区1 Ref="29" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,6.2,30,0.8</ClientRectangle>
          <Components isList="true" count="13">
            <文本14 Ref="30" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,1.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,5,0,0</Margins>
              <Name>文本14</Name>
              <Page isRef="8" />
              <Parent isRef="29" />
              <Text>{Line}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>SystemVariables</Type>
              <VertAlignment>Center</VertAlignment>
            </文本14>
            <文本11 Ref="31" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>4.4,0,2.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,5,0,0</Margins>
              <Name>文本11</Name>
              <Page isRef="8" />
              <Parent isRef="29" />
              <Text>{root_data_list.hotelRoomTypeName}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本11>
            <文本12 Ref="32" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>7.2,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,5,0,0</Margins>
              <Name>文本12</Name>
              <Page isRef="8" />
              <Parent isRef="29" />
              <Text>{root_data_list.roomCount}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本12>
            <文本13 Ref="33" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>1.6,0,2.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Margins>5,5,0,0</Margins>
              <Name>文本13</Name>
              <Page isRef="8" />
              <Parent isRef="29" />
              <Text>{root_data_list.predictDate}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextFormat Ref="34" type="DateFormat" isKey="true" />
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本13>
            <文本1 Ref="35" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>9.4,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,5,0,0</Margins>
              <Name>文本1</Name>
              <Page isRef="8" />
              <Parent isRef="29" />
              <Text>{root_data_list.repairRoomCount}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本1>
            <文本19 Ref="36" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>11.6,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,5,0,0</Margins>
              <Name>文本19</Name>
              <Page isRef="8" />
              <Parent isRef="29" />
              <Text>{root_data_list.canSellCount}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本19>
            <文本20 Ref="37" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>13.8,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,5,0,0</Margins>
              <Name>文本20</Name>
              <Page isRef="8" />
              <Parent isRef="29" />
              <Text>{root_data_list.soldCount}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本20>
            <文本10 Ref="38" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>16,0,2.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>5,5,0,0</Margins>
              <Name>文本10</Name>
              <Page isRef="8" />
              <Parent isRef="29" />
              <Text>{root_data_list.predictRevenue}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本10>
            <文本22 Ref="39" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>18.6,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,5,0,0</Margins>
              <Name>文本22</Name>
              <Page isRef="8" />
              <Parent isRef="29" />
              <Text>{root_data_list.predictOccupancyRate}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本22>
            <文本9 Ref="40" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>20.8,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>5,5,0,0</Margins>
              <Name>文本9</Name>
              <Page isRef="8" />
              <Parent isRef="29" />
              <Text>{root_data_list.predictAvgRoomPrice}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本9>
            <文本17 Ref="41" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>23,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>5,5,0,0</Margins>
              <Name>文本17</Name>
              <Page isRef="8" />
              <Parent isRef="29" />
              <Text>{root_data_list.predictRevPar}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本17>
            <文本24 Ref="42" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>25.2,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,5,0,0</Margins>
              <Name>文本24</Name>
              <Page isRef="8" />
              <Parent isRef="29" />
              <Text>{root_data_list.bookingCount}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本24>
            <文本25 Ref="43" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>27.4,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,5,0,0</Margins>
              <Name>文本25</Name>
              <Page isRef="8" />
              <Parent isRef="29" />
              <Text>{root_data_list.inhouseCount}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本25>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>root_data_list</DataSourceName>
          <Expressions isList="true" count="0" />
          <Filters isList="true" count="0" />
          <Name>数据区1</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
          <Sort isList="true" count="0" />
        </数据区1>
        <页脚1 Ref="44" type="FooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,7.8,30,0.8</ClientRectangle>
          <Components isList="true" count="11">
            <文本26 Ref="45" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,7.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本26</Name>
              <Page isRef="8" />
              <Parent isRef="44" />
              <Text>总计</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本26>
            <文本27 Ref="46" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>7.2,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,5,0,0</Margins>
              <Name>文本27</Name>
              <Page isRef="8" />
              <Parent isRef="44" />
              <Text>{totalRoom=Sum(root_data_list.roomCount)}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本27>
            <文本28 Ref="47" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>9.4,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,5,0,0</Margins>
              <Name>文本28</Name>
              <Page isRef="8" />
              <Parent isRef="44" />
              <Text>{Sum(root_data_list.repairRoomCount)}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </文本28>
            <文本29 Ref="48" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>11.6,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,5,0,0</Margins>
              <Name>文本29</Name>
              <Page isRef="8" />
              <Parent isRef="44" />
              <Text>{Sum(root_data_list.canSellCount)}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </文本29>
            <文本30 Ref="49" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>13.8,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,5,0,0</Margins>
              <Name>文本30</Name>
              <Page isRef="8" />
              <Parent isRef="44" />
              <Text>{soldCount=Sum(root_data_list.soldCount)}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本30>
            <文本31 Ref="50" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>16,0,2.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>5,5,0,0</Margins>
              <Name>文本31</Name>
              <Page isRef="8" />
              <Parent isRef="44" />
              <Text>{predictRevenue=Sum(root_data_list.predictRevenue)}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本31>
            <文本32 Ref="51" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>25.2,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,5,0,0</Margins>
              <Name>文本32</Name>
              <Page isRef="8" />
              <Parent isRef="44" />
              <Text>{Sum(root_data_list.bookingCount)}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </文本32>
            <文本33 Ref="52" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>27.4,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,5,0,0</Margins>
              <Name>文本33</Name>
              <Page isRef="8" />
              <Parent isRef="44" />
              <Text>{Sum(root_data_list.inhouseCount)}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </文本33>
            <文本34 Ref="53" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>18.6,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,5,0,0</Margins>
              <Name>文本34</Name>
              <Page isRef="8" />
              <Parent isRef="44" />
              <Text>{soldCount/totalRoom}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextFormat Ref="54" type="PercentageFormat" isKey="true">
                <DecimalDigits>2</DecimalDigits>
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <PositivePattern>1</PositivePattern>
                <Symbol>%</Symbol>
              </TextFormat>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本34>
            <文本35 Ref="55" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>20.8,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>5,5,0,0</Margins>
              <Name>文本35</Name>
              <Page isRef="8" />
              <Parent isRef="44" />
              <Text>{predictRevenue/soldCount}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextFormat Ref="56" type="NumberFormat" isKey="true">
                <DecimalDigits>2</DecimalDigits>
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
              </TextFormat>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本35>
            <文本36 Ref="57" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>23,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>5,5,0,0</Margins>
              <Name>文本36</Name>
              <Page isRef="8" />
              <Parent isRef="44" />
              <Text>{predictRevenue/totalRoom}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextFormat Ref="58" type="NumberFormat" isKey="true">
                <DecimalDigits>2</DecimalDigits>
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
              </TextFormat>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本36>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>页脚1</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
        </页脚1>
      </Components>
      <Conditions isList="true" count="0" />
      <Expressions isList="true" count="0" />
      <Guid>269f983fc46a4961814cd6b1074cc681</Guid>
      <Margins>1,1,1,1</Margins>
      <Name>Page1</Name>
      <PageHeight>29.7</PageHeight>
      <PageWidth>32</PageWidth>
      <Report isRef="0" />
    </Page1>
  </Pages>
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>报表</ReportAlias>
  <ReportAuthor>xiao lin</ReportAuthor>
  <ReportChanged>8/5/2025 2:29:11 PM</ReportChanged>
  <ReportCreated>8/4/2025 4:48:50 PM</ReportCreated>
  <ReportFile>D:\11\2\pms-report\public\reports\roomClassPredictionReport.mrt</ReportFile>
  <ReportGuid>f0548078ed324a0f8b1ca6e24acd3254</ReportGuid>
  <ReportName>报表</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2024.3.5.0</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class 报表 : Stimulsoft.Report.StiReport
    {
        public 报表()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
		#endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>