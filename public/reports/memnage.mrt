<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <CalculationMode>Interpretation</CalculationMode>
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="1">
      <managerDaily Ref="2" type="Stimulsoft.Report.Dictionary.StiJsonDatabase" isKey="true">
        <Alias>managerDaily</Alias>
        <HeadersString />
        <Key />
        <Name>managerDaily</Name>
        <PathData>C:\Users\<USER>\Desktop\需求文档\2\经理报表\managerDaily.json</PathData>
      </managerDaily>
    </Databases>
    <DataSources isList="true" count="9">
      <root Ref="3" type="DataTableSource" isKey="true">
        <Alias>root</Alias>
        <Columns isList="true" count="4">
          <value>code,System.Decimal</value>
          <value>data,System.String</value>
          <value>msg,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>f6c6debd165c4b9a94f725ef7d1264ac</Key>
        <Name>root</Name>
        <NameInSource>managerDaily.root</NameInSource>
      </root>
      <root_data Ref="4" type="DataTableSource" isKey="true">
        <Alias>root_data</Alias>
        <Columns isList="true" count="12">
          <value>hname,System.String</value>
          <value>hcode,System.String</value>
          <value>bizDate,System.String</value>
          <value>strBizDate,System.String</value>
          <value>previousStartTime,System.String</value>
          <value>startTime,System.String</value>
          <value>lastSelectTime,System.String</value>
          <value>operator,System.String</value>
          <value>roomRevenueIndex,System.String</value>
          <value>revenueCount,System.String</value>
          <value>businessData,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>5b7a7949b57646b3ab6f57a80fa8b08d</Key>
        <Name>root_data</Name>
        <NameInSource>managerDaily.root_data</NameInSource>
      </root_data>
      <root_data_businessData Ref="5" type="DataTableSource" isKey="true">
        <Alias>root_data_businessData</Alias>
        <Columns isList="true" count="6">
          <value>bizDate,System.String</value>
          <value>cateGory,System.String</value>
          <value>classificationStatistics,System.String</value>
          <value>nightNum,System.Decimal</value>
          <value>totalFee,System.Decimal</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>c1638c6f3d104f3cbc3d44ed1f1d2e4d</Key>
        <Name>root_data_businessData</Name>
        <NameInSource>managerDaily.root_data_businessData</NameInSource>
      </root_data_businessData>
      <root_data_businessData_bizDate Ref="6" type="DataTableSource" isKey="true">
        <Alias>root_data_businessData_bizDate</Alias>
        <Columns isList="true" count="2">
          <value>value,System.Decimal</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>b967b3d02ba94bdcb637e1e48a4043eb</Key>
        <Name>root_data_businessData_bizDate</Name>
        <NameInSource>managerDaily.root_data_businessData_bizDate</NameInSource>
      </root_data_businessData_bizDate>
      <root_data_revenueCount Ref="7" type="DataTableSource" isKey="true">
        <Alias>root_data_revenueCount</Alias>
        <Columns isList="true" count="9">
          <value>paymentDetails,System.String</value>
          <value>consumptionDetails,System.String</value>
          <value>paymentTypeTotalFee,System.Decimal</value>
          <value>consumptionTypeTotalFee,System.Decimal</value>
          <value>memberRechargeTotalFee,System.Decimal</value>
          <value>mMemberRechargeTotalFee,System.Decimal</value>
          <value>customerBillTotalFee,System.Decimal</value>
          <value>gMemberRechargeTotalFee,System.Decimal</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>57dc5956e18b4ddea93e01727e00caff</Key>
        <Name>root_data_revenueCount</Name>
        <NameInSource>managerDaily.root_data_revenueCount</NameInSource>
      </root_data_revenueCount>
      <root_data_revenueCount_consumptionDetails Ref="8" type="DataTableSource" isKey="true">
        <Alias>root_data_revenueCount_consumptionDetails</Alias>
        <Columns isList="true" count="4">
          <value>subCode,System.String</value>
          <value>subName,System.String</value>
          <value>totalFee,System.Decimal</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>6c8bb5616c194d21ac70958222eeebbc</Key>
        <Name>root_data_revenueCount_consumptionDetails</Name>
        <NameInSource>managerDaily.root_data_revenueCount_consumptionDetails</NameInSource>
      </root_data_revenueCount_consumptionDetails>
      <root_data_revenueCount_paymentDetails Ref="9" type="DataTableSource" isKey="true">
        <Alias>root_data_revenueCount_paymentDetails</Alias>
        <Columns isList="true" count="4">
          <value>subCode,System.String</value>
          <value>subName,System.String</value>
          <value>totalFee,System.Decimal</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>bc0fdeddf7724c42bc7ded4434508c0e</Key>
        <Name>root_data_revenueCount_paymentDetails</Name>
        <NameInSource>managerDaily.root_data_revenueCount_paymentDetails</NameInSource>
      </root_data_revenueCount_paymentDetails>
      <root_data_roomRevenueIndex Ref="10" type="DataTableSource" isKey="true">
        <Alias>root_data_roomRevenueIndex</Alias>
        <Columns isList="true" count="26">
          <value>id,System.Decimal</value>
          <value>gcode,System.String</value>
          <value>hcode,System.String</value>
          <value>nightNum,System.Decimal</value>
          <value>roomFee,System.Decimal</value>
          <value>revPar,System.Decimal</value>
          <value>avgRoomFee,System.Decimal</value>
          <value>occ,System.Decimal</value>
          <value>totalRoomNum,System.Decimal</value>
          <value>emptyNum,System.Decimal</value>
          <value>repairNum,System.Decimal</value>
          <value>selfNum,System.Decimal</value>
          <value>overnightNum,System.Decimal</value>
          <value>overnightOcc,System.Decimal</value>
          <value>openRoomNum,System.Decimal</value>
          <value>workInNum,System.Decimal</value>
          <value>bookInNum,System.Decimal</value>
          <value>cancelBookNum,System.Decimal</value>
          <value>noShowNum,System.Decimal</value>
          <value>memberCardSellNum,System.Decimal</value>
          <value>creditNum,System.Decimal</value>
          <value>freeUpNum,System.Decimal</value>
          <value>memberCardFee,System.String</value>
          <value>bizDate,System.String</value>
          <value>createTime,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>9bc27191b3ab4e45bfc22b0dc05e22aa</Key>
        <Name>root_data_roomRevenueIndex</Name>
        <NameInSource>managerDaily.root_data_roomRevenueIndex</NameInSource>
      </root_data_roomRevenueIndex>
      <root_data_roomRevenueIndex_bizDate Ref="11" type="DataTableSource" isKey="true">
        <Alias>root_data_roomRevenueIndex_bizDate</Alias>
        <Columns isList="true" count="2">
          <value>value,System.Decimal</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>f1b352e8f33d4ef89f1a1166f5341c00</Key>
        <Name>root_data_roomRevenueIndex_bizDate</Name>
        <NameInSource>managerDaily.root_data_roomRevenueIndex_bizDate</NameInSource>
      </root_data_roomRevenueIndex_bizDate>
    </DataSources>
    <Relations isList="true" count="8">
      <root Ref="12" type="DataRelation" isKey="true">
        <Alias>root</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="4" />
        <Dictionary isRef="1" />
        <Name>root</Name>
        <NameInSource>root_data</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>data</value>
        </ParentColumns>
        <ParentSource isRef="3" />
      </root>
      <root_data Ref="13" type="DataRelation" isKey="true">
        <Alias>root_data</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="5" />
        <Dictionary isRef="1" />
        <Name>root_data</Name>
        <NameInSource>root_data_businessData</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>businessData</value>
        </ParentColumns>
        <ParentSource isRef="4" />
      </root_data>
      <root_data_businessData Ref="14" type="DataRelation" isKey="true">
        <Alias>root_data_businessData</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="6" />
        <Dictionary isRef="1" />
        <Name>root_data_businessData</Name>
        <NameInSource>root_data_businessData_bizDate</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>bizDate</value>
        </ParentColumns>
        <ParentSource isRef="5" />
      </root_data_businessData>
      <root_data Ref="15" type="DataRelation" isKey="true">
        <Alias>root_data</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="7" />
        <Dictionary isRef="1" />
        <Name>root_data</Name>
        <NameInSource>root_data_revenueCount</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>revenueCount</value>
        </ParentColumns>
        <ParentSource isRef="4" />
      </root_data>
      <root_data_revenueCount Ref="16" type="DataRelation" isKey="true">
        <Alias>root_data_revenueCount</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="8" />
        <Dictionary isRef="1" />
        <Name>root_data_revenueCount</Name>
        <NameInSource>root_data_revenueCount_consumptionDetails</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>consumptionDetails</value>
        </ParentColumns>
        <ParentSource isRef="7" />
      </root_data_revenueCount>
      <root_data_revenueCount Ref="17" type="DataRelation" isKey="true">
        <Alias>root_data_revenueCount</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="9" />
        <Dictionary isRef="1" />
        <Name>root_data_revenueCount</Name>
        <NameInSource>root_data_revenueCount_paymentDetails</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>paymentDetails</value>
        </ParentColumns>
        <ParentSource isRef="7" />
      </root_data_revenueCount>
      <root_data Ref="18" type="DataRelation" isKey="true">
        <Alias>root_data</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="10" />
        <Dictionary isRef="1" />
        <Name>root_data</Name>
        <NameInSource>root_data_roomRevenueIndex</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>roomRevenueIndex</value>
        </ParentColumns>
        <ParentSource isRef="4" />
      </root_data>
      <root_data_roomRevenueIndex Ref="19" type="DataRelation" isKey="true">
        <Alias>root_data_roomRevenueIndex</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="11" />
        <Dictionary isRef="1" />
        <Name>root_data_roomRevenueIndex</Name>
        <NameInSource>root_data_roomRevenueIndex_bizDate</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>bizDate</value>
        </ParentColumns>
        <ParentSource isRef="10" />
      </root_data_roomRevenueIndex>
    </Relations>
    <Report isRef="0" />
    <Resources isList="true" count="0" />
    <UserFunctions isList="true" count="0" />
    <Variables isList="true" count="0" />
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <Key>4fd67cec064c4d8aa51ffce8894f7d3a</Key>
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="20" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="12">
        <PageHeaderBand1 Ref="21" type="PageHeaderBand" isKey="true">
          <Border>Bottom;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,19,0.4</ClientRectangle>
          <Components isList="true" count="2">
            <Text2 Ref="22" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0.01,0,7,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Name>Text2</Name>
              <Page isRef="20" />
              <Parent isRef="21" />
              <Text>{root_data.hname}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text2>
            <Text8 Ref="23" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>13.4,0,5.6,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Name>Text8</Name>
              <Page isRef="20" />
              <Parent isRef="21" />
              <Text>第{PageNumber}页，共{TotalPageCount}页</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text8>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>PageHeaderBand1</Name>
          <Page isRef="20" />
          <Parent isRef="20" />
        </PageHeaderBand1>
        <PageFooterBand1 Ref="24" type="PageFooterBand" isKey="true">
          <Border>Bottom;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>0,40,19,0.8</ClientRectangle>
          <Components isList="true" count="2">
            <Text45 Ref="25" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.2,2.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Name>Text45</Name>
              <Page isRef="20" />
              <Parent isRef="24" />
              <Text>{root_data.operator_}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text45>
            <Text1 Ref="26" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>15.2,0.2,3.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Name>Text1</Name>
              <Page isRef="20" />
              <Parent isRef="24" />
              <Text>{root_data.lastSelectTime.Substring(0, 19).Replace("-", "/")}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text1>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>PageFooterBand1</Name>
          <Page isRef="20" />
          <Parent isRef="20" />
        </PageFooterBand1>
        <Text46 Ref="27" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>20.2,27.1,3.8,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Font>Arial,8,Bold</Font>
          <HorAlignment>Right</HorAlignment>
          <Name>Text46</Name>
          <Page isRef="20" />
          <Parent isRef="20" />
          <Text>{root_data.lastSelectTime}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Bottom</VertAlignment>
        </Text46>
        <ReportTitleBand1 Ref="28" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,1.6,19,2</ClientRectangle>
          <Components isList="true" count="2">
            <Text3 Ref="29" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.1,0.2,5.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,12</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text3</Name>
              <Page isRef="20" />
              <Parent isRef="28" />
              <Text>经理综合日报表 (固化)</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text3>
            <Text5 Ref="30" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1,19,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Name>Text5</Name>
              <Page isRef="20" />
              <Parent isRef="28" />
              <Text>门店:{root_data.hname}  营业日:{root_data.bizDate.Substring(0, 10)}({root_data.previousStartTime.Substring(0, 19).Replace("-", "/")} - {root_data.startTime.Substring(0, 19).Replace("-", "/")}) 
最后查询时间：{root_data.lastSelectTime.Substring(0, 19).Replace("-", "/")}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text5>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>ReportTitleBand1</Name>
          <Page isRef="20" />
          <Parent isRef="20" />
        </ReportTitleBand1>
        <HeaderBand1 Ref="31" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,4.4,19,1.6</ClientRectangle>
          <Components isList="true" count="4">
            <Text4 Ref="32" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,19,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text4</Name>
              <Page isRef="20" />
              <Parent isRef="31" />
              <Text>客房营业收入指标</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text4>
            <Text6 Ref="33" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.8,9,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text6</Name>
              <Page isRef="20" />
              <Parent isRef="31" />
              <Text>本日客房经营指标</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text6>
            <Text7 Ref="34" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9,0.8,5,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text7</Name>
              <Page isRef="20" />
              <Parent isRef="31" />
              <Text>本日</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text7>
            <Text9 Ref="35" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14,0.8,5,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text9</Name>
              <Page isRef="20" />
              <Parent isRef="31" />
              <Text>客房数</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text9>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>HeaderBand1</Name>
          <Page isRef="20" />
          <Parent isRef="20" />
        </HeaderBand1>
        <DataBand1 Ref="36" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,6.8,19,5.6</ClientRectangle>
          <Components isList="true" count="24">
            <Text11 Ref="37" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,4.5,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text11</Name>
              <Page isRef="20" />
              <Parent isRef="36" />
              <Text>间夜数</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text11>
            <Text10 Ref="38" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.5,0,4.5,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Hyperlink>#/?name=StoreDailyReviewRoomRate&amp;hcode={root_data.hcode}&amp;startDate={root_data.bizDate}&amp;endDate={root_data.bizDate}</Hyperlink>
              <Margins>5,0,0,0</Margins>
              <Name>Text10</Name>
              <Page isRef="20" />
              <Parent isRef="36" />
              <Text>{root_data_roomRevenueIndex.nightNum}</Text>
              <TextBrush>[0:176:240]</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text10>
            <Text12 Ref="39" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.8,4.5,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text12</Name>
              <Page isRef="20" />
              <Parent isRef="36" />
              <Text>总房费</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text12>
            <Text13 Ref="40" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.6,4.5,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text13</Name>
              <Page isRef="20" />
              <Parent isRef="36" />
              <Text>出租率</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text13>
            <Text14 Ref="41" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,2.4,4.5,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text14</Name>
              <Page isRef="20" />
              <Parent isRef="36" />
              <Text>平均房价</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text14>
            <Text15 Ref="42" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,3.2,4.5,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text15</Name>
              <Page isRef="20" />
              <Parent isRef="36" />
              <Text>RevPar</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text15>
            <Text17 Ref="43" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.5,0.8,4.5,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Hyperlink>#/?name=StoreDailyReviewRoomRate&amp;hcode={root_data.hcode}&amp;startDate={root_data.bizDate}&amp;endDate={root_data.bizDate}</Hyperlink>
              <Margins>0,5,0,0</Margins>
              <Name>Text17</Name>
              <Page isRef="20" />
              <Parent isRef="36" />
              <Text>{root_data_roomRevenueIndex.roomFee}</Text>
              <TextBrush>[0:176:240]</TextBrush>
              <TextFormat Ref="44" type="CurrencyFormat" isKey="true">
                <DecimalDigits>2</DecimalDigits>
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>2</NegativePattern>
                <PositivePattern>0</PositivePattern>
                <Symbol>¥</Symbol>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text17>
            <Text18 Ref="45" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.5,1.6,4.5,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text18</Name>
              <Page isRef="20" />
              <Parent isRef="36" />
              <Text>{Format("{0:P2}",root_data_roomRevenueIndex.occ/100)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="46" type="PercentageFormat" isKey="true">
                <DecimalDigits>2</DecimalDigits>
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <PositivePattern>1</PositivePattern>
                <Symbol>%</Symbol>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text18>
            <Text19 Ref="47" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.5,2.4,4.5,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>Text19</Name>
              <Page isRef="20" />
              <Parent isRef="36" />
              <Text>{root_data_roomRevenueIndex.avgRoomFee}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text19>
            <Text20 Ref="48" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.5,3.2,4.5,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>Text20</Name>
              <Page isRef="20" />
              <Parent isRef="36" />
              <Text> {root_data_roomRevenueIndex.revPar}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text20>
            <Text22 Ref="49" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9,0,5,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text22</Name>
              <Page isRef="20" />
              <Parent isRef="36" />
              <Text>总房数</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text22>
            <Text23 Ref="50" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9,0.8,5,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text23</Name>
              <Page isRef="20" />
              <Parent isRef="36" />
              <Text>空房</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text23>
            <Text24 Ref="51" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9,1.6,5,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text24</Name>
              <Page isRef="20" />
              <Parent isRef="36" />
              <Text>维修房</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text24>
            <Text25 Ref="52" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9,2.4,5,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text25</Name>
              <Page isRef="20" />
              <Parent isRef="36" />
              <Text>自用房</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text25>
            <Text26 Ref="53" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9,3.2,5,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text26</Name>
              <Page isRef="20" />
              <Parent isRef="36" />
              <Text>过夜房</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text26>
            <Text27 Ref="54" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9,4,5,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text27</Name>
              <Page isRef="20" />
              <Parent isRef="36" />
              <Text>过夜房出租率</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text27>
            <Text28 Ref="55" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14,0,5,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Hyperlink>#/?name=StoreGuestRoomNightReview&amp;hcode={root_data.hcode}&amp;bizDate={root_data.bizDate}</Hyperlink>
              <Margins>5,0,0,0</Margins>
              <Name>Text28</Name>
              <Page isRef="20" />
              <Parent isRef="36" />
              <Text>{root_data_roomRevenueIndex.totalRoomNum}</Text>
              <TextBrush>[0:176:240]</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text28>
            <Text29 Ref="56" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14,0.8,5,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Hyperlink>#/?name=StoreGuestRoomNightReview&amp;hcode={root_data.hcode}&amp;bizDate={root_data.bizDate}&amp;roomStatus=VC,VD</Hyperlink>
              <Margins>5,0,0,0</Margins>
              <Name>Text29</Name>
              <Page isRef="20" />
              <Parent isRef="36" />
              <Text>{root_data_roomRevenueIndex.emptyNum}</Text>
              <TextBrush>[0:176:240]</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text29>
            <Text30 Ref="57" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14,1.6,5,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Hyperlink>#/?name=StoreGuestRoomNightReview&amp;hcode={root_data.hcode}&amp;bizDate={root_data.bizDate}&amp;roomStatus=OO</Hyperlink>
              <Margins>5,0,0,0</Margins>
              <Name>Text30</Name>
              <Page isRef="20" />
              <Parent isRef="36" />
              <Text>{root_data_roomRevenueIndex.repairNum}</Text>
              <TextBrush>[0:176:240]</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text30>
            <Text31 Ref="58" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14,2.4,5,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text31</Name>
              <Page isRef="20" />
              <Parent isRef="36" />
              <Text>{root_data_roomRevenueIndex.selfNum}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text31>
            <Text32 Ref="59" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14,3.2,5,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Hyperlink>#/?name=StoreGuestRoomNightReview&amp;hcode={root_data.hcode}&amp;bizDate={root_data.bizDate}&amp;roomStatus=OD</Hyperlink>
              <Margins>5,0,0,0</Margins>
              <Name>Text32</Name>
              <Page isRef="20" />
              <Parent isRef="36" />
              <Text>{root_data_roomRevenueIndex.overnightNum}</Text>
              <TextBrush>[0:176:240]</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text32>
            <Text33 Ref="60" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14,4,5,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text33</Name>
              <Page isRef="20" />
              <Parent isRef="36" />
              <Text>{Format("{0:P2}", root_data_roomRevenueIndex.overnightOcc / 100)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="61" type="PercentageFormat" isKey="true">
                <DecimalDigits>2</DecimalDigits>
                <GroupSeparator>,</GroupSeparator>
                <GroupSize>2</GroupSize>
                <NegativePattern>1</NegativePattern>
                <PositivePattern>1</PositivePattern>
                <Symbol>%</Symbol>
                <UseGroupSeparator>False</UseGroupSeparator>
                <UseLocalSetting>False</UseLocalSetting>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text33>
            <Text35 Ref="62" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9,4.8,5,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text35</Name>
              <Page isRef="20" />
              <Parent isRef="36" />
              <Text>会员卡销售数</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text35>
            <Text36 Ref="63" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14,4.8,5,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Hyperlink>#/?name=memberStoreCardReport&amp;hcode={root_data.hcode}&amp;startDate={root_data.bizDate}&amp;endDate={root_data.bizDate}</Hyperlink>
              <Margins>5,0,0,0</Margins>
              <Name>Text36</Name>
              <Page isRef="20" />
              <Parent isRef="36" />
              <Text>{root_data_roomRevenueIndex.memberCardSellNum}</Text>
              <TextBrush>[0:176:240]</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text36>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>root_data_roomRevenueIndex</DataSourceName>
          <Expressions isList="true" count="0" />
          <Filters isList="true" count="0" />
          <Name>DataBand1</Name>
          <Page isRef="20" />
          <Parent isRef="20" />
          <Sort isList="true" count="0" />
        </DataBand1>
        <HeaderBand2 Ref="64" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,13.2,19,0.8</ClientRectangle>
          <Components isList="true" count="1">
            <Text34 Ref="65" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,19,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text34</Name>
              <Page isRef="20" />
              <Parent isRef="64" />
              <Text>收入统计</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text34>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>HeaderBand2</Name>
          <Page isRef="20" />
          <Parent isRef="20" />
        </HeaderBand2>
        <DataBand5 Ref="66" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,14.8,19,9.2</ClientRectangle>
          <Components isList="true" count="2">
            <Panel1 Ref="67" type="Panel" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,11.8,6</ClientRectangle>
              <Components isList="true" count="4">
                <DataBand4 Ref="68" type="DataBand" isKey="true">
                  <Brush>Transparent</Brush>
                  <BusinessObjectGuid isNull="true" />
                  <ClientRectangle>0,0.4,11.8,1</ClientRectangle>
                  <Components isList="true" count="5">
                    <Text127 Ref="69" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>7.4,0,2.2,1</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>Text127</Name>
                      <Page isRef="20" />
                      <Parent isRef="68" />
                      <Text>{root_data_revenueCount_consumptionDetails.subName}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </Text127>
                    <Text128 Ref="70" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>4.8,0,2.6,1</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>Text128</Name>
                      <Page isRef="20" />
                      <Parent isRef="68" />
                      <ProcessingDuplicates>GlobalMerge</ProcessingDuplicates>
                      <Text>今日发生消费</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </Text128>
                    <Text132 Ref="71" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>2.4,0,2.4,1</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>Text132</Name>
                      <Page isRef="20" />
                      <Parent isRef="68" />
                      <ProcessingDuplicates>GlobalMerge</ProcessingDuplicates>
                      <Text>门店收入</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </Text132>
                    <Text91 Ref="72" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>9.6,0,2.2,1</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Right</HorAlignment>
                      <Hyperlink>#/?name=StoreCashierConsumption&amp;hcode={root_data.hcode}&amp;startDate={root_data.bizDate}&amp;endDate={root_data.bizDate}&amp;subCode={root_data_revenueCount_consumptionDetails.subCode}</Hyperlink>
                      <Interaction Ref="73" type="Stimulsoft.Report.Components.StiInteraction" isKey="true">
                        <DrillDownReport>C:\Users\<USER>\OneDrive\桌面\报表\consumeReport.mrt</DrillDownReport>
                      </Interaction>
                      <Margins>0,5,0,0</Margins>
                      <Name>Text91</Name>
                      <Page isRef="20" />
                      <Parent isRef="68" />
                      <Text>{root_data_revenueCount_consumptionDetails.totalFee}</Text>
                      <TextBrush>[0:176:240]</TextBrush>
                      <TextFormat Ref="74" type="CurrencyFormat" isKey="true">
                        <DecimalDigits>2</DecimalDigits>
                        <GroupSeparator>,</GroupSeparator>
                        <NegativePattern>2</NegativePattern>
                        <PositivePattern>0</PositivePattern>
                        <Symbol>¥</Symbol>
                      </TextFormat>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </Text91>
                    <Text115 Ref="75" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0,2.4,1</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>Text115</Name>
                      <Page isRef="20" />
                      <Parent isRef="68" />
                      <ProcessingDuplicates>GlobalMerge</ProcessingDuplicates>
                      <Text>应收总计</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </Text115>
                  </Components>
                  <Conditions isList="true" count="0" />
                  <DataRelationName isNull="true" />
                  <DataSourceName>root_data_revenueCount_consumptionDetails</DataSourceName>
                  <Expressions isList="true" count="0" />
                  <Filters isList="true" count="0" />
                  <Name>DataBand4</Name>
                  <Page isRef="20" />
                  <Parent isRef="67" />
                  <Sort isList="true" count="0" />
                </DataBand4>
                <ColumnFooterBand1 Ref="76" type="Stimulsoft.Report.Components.StiColumnFooterBand" isKey="true">
                  <Brush>Transparent</Brush>
                  <ClientRectangle>0,2.2,11.8,2.4</ClientRectangle>
                  <Components isList="true" count="9">
                    <Text92 Ref="77" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>4.8,1.6,4.8,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>Text92</Name>
                      <Page isRef="20" />
                      <Parent isRef="76" />
                      <Text>合计</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </Text92>
                    <Text93 Ref="78" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>9.6,1.6,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Right</HorAlignment>
                      <Margins>0,5,0,0</Margins>
                      <Name>Text93</Name>
                      <Page isRef="20" />
                      <Parent isRef="76" />
                      <Text>{Sum(root_data_revenueCount_consumptionDetails.totalFee)+ root_data_revenueCount.mMemberRechargeTotalFee}</Text>
                      <TextBrush>Black</TextBrush>
                      <TextFormat Ref="79" type="CurrencyFormat" isKey="true">
                        <DecimalDigits>2</DecimalDigits>
                        <GroupSeparator>,</GroupSeparator>
                        <NegativePattern>2</NegativePattern>
                        <PositivePattern>0</PositivePattern>
                        <Symbol>¥</Symbol>
                      </TextFormat>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </Text93>
                    <Text119 Ref="80" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>7.4,0.8,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>Text119</Name>
                      <Page isRef="20" />
                      <Parent isRef="76" />
                      <Text>{root_data_revenueCount.mMemberRechargeTotalFee &gt; 0 ? "会员充值" : "None"}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </Text119>
                    <Text120 Ref="81" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>9.6,0.8,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Right</HorAlignment>
                      <Margins>0,5,0,0</Margins>
                      <Name>Text120</Name>
                      <Page isRef="20" />
                      <Parent isRef="76" />
                      <Text>{root_data_revenueCount.mMemberRechargeTotalFee}</Text>
                      <TextBrush>Black</TextBrush>
                      <TextFormat Ref="82" type="NumberFormat" isKey="true">
                        <DecimalDigits>2</DecimalDigits>
                        <GroupSeparator>,</GroupSeparator>
                        <NegativePattern>1</NegativePattern>
                      </TextFormat>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </Text120>
                    <Text107 Ref="83" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>4.8,0,2.6,1.6</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>Text107</Name>
                      <Page isRef="20" />
                      <Parent isRef="76" />
                      <ProcessingDuplicates>GlobalMerge</ProcessingDuplicates>
                      <Text>今日发生消费</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </Text107>
                    <Text137 Ref="84" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0,2.4,2.4</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>Text137</Name>
                      <Page isRef="20" />
                      <Parent isRef="76" />
                      <ProcessingDuplicates>GlobalMerge</ProcessingDuplicates>
                      <Text>应收总计</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </Text137>
                    <Text133 Ref="85" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>2.4,0,2.4,2.4</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>Text133</Name>
                      <Page isRef="20" />
                      <Parent isRef="76" />
                      <ProcessingDuplicates>GlobalMerge</ProcessingDuplicates>
                      <Text>门店收入</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </Text133>
                    <Text131 Ref="86" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>7.4,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>Text131</Name>
                      <Page isRef="20" />
                      <Parent isRef="76" />
                      <Text>小计</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </Text131>
                    <Text130 Ref="87" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>9.6,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Right</HorAlignment>
                      <Margins>0,5,0,0</Margins>
                      <Name>Text130</Name>
                      <Page isRef="20" />
                      <Parent isRef="76" />
                      <Text>{Sum(root_data_revenueCount_consumptionDetails.totalFee)}</Text>
                      <TextBrush>Black</TextBrush>
                      <TextFormat Ref="88" type="CurrencyFormat" isKey="true">
                        <DecimalDigits>2</DecimalDigits>
                        <GroupSeparator>,</GroupSeparator>
                        <NegativePattern>2</NegativePattern>
                        <PositivePattern>0</PositivePattern>
                        <Symbol>¥</Symbol>
                      </TextFormat>
                      <Type>Totals</Type>
                      <VertAlignment>Center</VertAlignment>
                    </Text130>
                  </Components>
                  <Conditions isList="true" count="0" />
                  <Expressions isList="true" count="0" />
                  <Name>ColumnFooterBand1</Name>
                  <Page isRef="20" />
                  <Parent isRef="67" />
                </ColumnFooterBand1>
                <DataBand8 Ref="89" type="DataBand" isKey="true">
                  <Brush>Transparent</Brush>
                  <BusinessObjectGuid isNull="true" />
                  <ClientRectangle>0,5.4,11.8,2.4</ClientRectangle>
                  <Components isList="true" count="9">
                    <Text108 Ref="90" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>2.4,0,2.4,2.4</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>Text108</Name>
                      <Page isRef="20" />
                      <Parent isRef="89" />
                      <Text>非门店收入</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </Text108>
                    <Text135 Ref="91" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0,2.4,2.4</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>Text135</Name>
                      <Page isRef="20" />
                      <Parent isRef="89" />
                      <ProcessingDuplicates>GlobalMerge</ProcessingDuplicates>
                      <Text>应收总计</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </Text135>
                    <Text114 Ref="92" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>4.8,0,2.6,1.6</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>Text114</Name>
                      <Page isRef="20" />
                      <Parent isRef="89" />
                      <Text>会员充值</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </Text114>
                    <Text122 Ref="93" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>7.4,0.8,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>Text122</Name>
                      <Page isRef="20" />
                      <Parent isRef="89" />
                      <Text>小计</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </Text122>
                    <Text112 Ref="94" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>4.8,1.6,4.8,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>Text112</Name>
                      <Page isRef="20" />
                      <Parent isRef="89" />
                      <Text>合计</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </Text112>
                    <Text125 Ref="95" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>9.6,1.6,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Right</HorAlignment>
                      <Margins>0,5,0,0</Margins>
                      <Name>Text125</Name>
                      <Page isRef="20" />
                      <Parent isRef="89" />
                      <Text>{root_data_revenueCount.gMemberRechargeTotalFee}</Text>
                      <TextBrush>Black</TextBrush>
                      <TextFormat Ref="96" type="CurrencyFormat" isKey="true">
                        <DecimalDigits>2</DecimalDigits>
                        <GroupSeparator>,</GroupSeparator>
                        <NegativePattern>2</NegativePattern>
                        <PositivePattern>0</PositivePattern>
                        <Symbol>¥</Symbol>
                      </TextFormat>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </Text125>
                    <Text123 Ref="97" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>9.6,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Right</HorAlignment>
                      <Hyperlink>#/?name=StoreCashierConsumption&amp;hcode={root_data.hcode}&amp;startDate={root_data.bizDate}&amp;endDate={root_data.bizDate}&amp;subCode=member_recharge</Hyperlink>
                      <Margins>0,5,0,0</Margins>
                      <Name>Text123</Name>
                      <Page isRef="20" />
                      <Parent isRef="89" />
                      <Text>{root_data_revenueCount.gMemberRechargeTotalFee}</Text>
                      <TextBrush>[0:176:240]</TextBrush>
                      <TextFormat Ref="98" type="CurrencyFormat" isKey="true">
                        <DecimalDigits>2</DecimalDigits>
                        <GroupSeparator>,</GroupSeparator>
                        <NegativePattern>2</NegativePattern>
                        <PositivePattern>0</PositivePattern>
                        <Symbol>¥</Symbol>
                      </TextFormat>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </Text123>
                    <Text124 Ref="99" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>9.6,0.8,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Right</HorAlignment>
                      <Margins>0,5,0,0</Margins>
                      <Name>Text124</Name>
                      <Page isRef="20" />
                      <Parent isRef="89" />
                      <Text>{root_data_revenueCount.gMemberRechargeTotalFee}</Text>
                      <TextBrush>Black</TextBrush>
                      <TextFormat Ref="100" type="CurrencyFormat" isKey="true">
                        <DecimalDigits>2</DecimalDigits>
                        <GroupSeparator>,</GroupSeparator>
                        <NegativePattern>2</NegativePattern>
                        <PositivePattern>0</PositivePattern>
                        <Symbol>¥</Symbol>
                      </TextFormat>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </Text124>
                    <Text116 Ref="101" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>7.4,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>Text116</Name>
                      <Page isRef="20" />
                      <Parent isRef="89" />
                      <Text>会员充值</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </Text116>
                  </Components>
                  <Conditions isList="true" count="0" />
                  <DataRelationName isNull="true" />
                  <DataSourceName>root_data_revenueCount</DataSourceName>
                  <Expressions isList="true" count="0" />
                  <Filters isList="true" count="0" />
                  <Name>DataBand8</Name>
                  <Page isRef="20" />
                  <Parent isRef="67" />
                  <Sort isList="true" count="0" />
                </DataBand8>
                <ColumnFooterBand3 Ref="102" type="Stimulsoft.Report.Components.StiColumnFooterBand" isKey="true">
                  <Brush>Transparent</Brush>
                  <ClientRectangle>0,8.6,11.8,0.8</ClientRectangle>
                  <Components isList="true" count="3">
                    <Text144 Ref="103" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0,2.4,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>Text144</Name>
                      <Page isRef="20" />
                      <Parent isRef="102" />
                      <ProcessingDuplicates>GlobalMerge</ProcessingDuplicates>
                      <Text>应收总计</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </Text144>
                    <Text109 Ref="104" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>2.4,0,7.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>Text109</Name>
                      <Page isRef="20" />
                      <Parent isRef="102" />
                      <Text>总计</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </Text109>
                    <Text95 Ref="105" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>9.6,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Right</HorAlignment>
                      <Margins>0,5,0,0</Margins>
                      <Name>Text95</Name>
                      <Page isRef="20" />
                      <Parent isRef="102" />
                      <Text>{root_data_revenueCount.consumptionTypeTotalFee}</Text>
                      <TextBrush>Black</TextBrush>
                      <TextFormat Ref="106" type="CurrencyFormat" isKey="true">
                        <DecimalDigits>2</DecimalDigits>
                        <GroupSeparator>,</GroupSeparator>
                        <NegativePattern>2</NegativePattern>
                        <PositivePattern>0</PositivePattern>
                        <Symbol>¥</Symbol>
                      </TextFormat>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </Text95>
                  </Components>
                  <Conditions isList="true" count="0" />
                  <Expressions isList="true" count="0" />
                  <Name>ColumnFooterBand3</Name>
                  <Page isRef="20" />
                  <Parent isRef="67" />
                </ColumnFooterBand3>
              </Components>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Name>Panel1</Name>
              <Page isRef="20" />
              <Parent isRef="66" />
            </Panel1>
            <Panel2 Ref="107" type="Panel" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>11.8,0,7.2,4.4</ClientRectangle>
              <Components isList="true" count="3">
                <DataBand6 Ref="108" type="DataBand" isKey="true">
                  <Brush>Transparent</Brush>
                  <BusinessObjectGuid isNull="true" />
                  <ClientRectangle>0,0.4,7.2,1</ClientRectangle>
                  <Components isList="true" count="3">
                    <Text90 Ref="109" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>2.8,0,2.2,1</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>Text90</Name>
                      <Page isRef="20" />
                      <Parent isRef="108" />
                      <Text>{root_data_revenueCount_paymentDetails.subName}</Text>
                      <TextBrush>Black</TextBrush>
                      <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </Text90>
                    <Text105 Ref="110" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0,2.8,1</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>Text105</Name>
                      <Page isRef="20" />
                      <Parent isRef="108" />
                      <ProcessingDuplicates>GlobalMerge</ProcessingDuplicates>
                      <Text>今日发生付款</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </Text105>
                    <Text94 Ref="111" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>5,0,2.2,1</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Right</HorAlignment>
                      <Hyperlink>#/?name=StoreCashierPayment&amp;hcode={root_data.hcode}&amp;startDate={root_data.bizDate}&amp;endDate={root_data.bizDate}&amp;subCode={root_data_revenueCount_paymentDetails.subCode}</Hyperlink>
                      <Margins>0,5,0,0</Margins>
                      <Name>Text94</Name>
                      <Page isRef="20" />
                      <Parent isRef="108" />
                      <Text>{root_data_revenueCount_paymentDetails.totalFee}</Text>
                      <TextBrush>[0:176:240]</TextBrush>
                      <TextFormat Ref="112" type="CurrencyFormat" isKey="true">
                        <DecimalDigits>2</DecimalDigits>
                        <GroupSeparator>,</GroupSeparator>
                        <NegativePattern>2</NegativePattern>
                        <PositivePattern>0</PositivePattern>
                        <Symbol>¥</Symbol>
                      </TextFormat>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </Text94>
                  </Components>
                  <Conditions isList="true" count="0" />
                  <DataRelationName isNull="true" />
                  <DataSourceName>root_data_revenueCount_paymentDetails</DataSourceName>
                  <Expressions isList="true" count="0" />
                  <Filters isList="true" count="0" />
                  <Name>DataBand6</Name>
                  <Page isRef="20" />
                  <Parent isRef="107" />
                  <Sort isList="true" count="0" />
                </DataBand6>
                <ColumnFooterBand2 Ref="113" type="Stimulsoft.Report.Components.StiColumnFooterBand" isKey="true">
                  <Brush>Transparent</Brush>
                  <ClientRectangle>0,2.2,7.2,0.8</ClientRectangle>
                  <Components isList="true" count="3">
                    <Text96 Ref="114" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>2.8,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>Text96</Name>
                      <Page isRef="20" />
                      <Parent isRef="113" />
                      <Text>总计</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </Text96>
                    <Text101 Ref="115" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>5,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Right</HorAlignment>
                      <Margins>0,5,0,0</Margins>
                      <Name>Text101</Name>
                      <Page isRef="20" />
                      <Parent isRef="113" />
                      <Text>{root_data_revenueCount.paymentTypeTotalFee}</Text>
                      <TextBrush>Black</TextBrush>
                      <TextFormat Ref="116" type="CurrencyFormat" isKey="true">
                        <DecimalDigits>2</DecimalDigits>
                        <GroupSeparator>,</GroupSeparator>
                        <NegativePattern>2</NegativePattern>
                        <PositivePattern>0</PositivePattern>
                        <Symbol>¥</Symbol>
                      </TextFormat>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </Text101>
                    <Text134 Ref="117" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0,2.8,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>Text134</Name>
                      <Page isRef="20" />
                      <Parent isRef="113" />
                      <ProcessingDuplicates>GlobalMerge</ProcessingDuplicates>
                      <Text>今日发生付款</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </Text134>
                  </Components>
                  <Conditions isList="true" count="0" />
                  <Expressions isList="true" count="0" />
                  <Name>ColumnFooterBand2</Name>
                  <Page isRef="20" />
                  <Parent isRef="107" />
                </ColumnFooterBand2>
                <DataBand7 Ref="118" type="DataBand" isKey="true">
                  <Brush>Transparent</Brush>
                  <BusinessObjectGuid isNull="true" />
                  <ClientRectangle>0,3.8,7.2,1.6</ClientRectangle>
                  <Components isList="true" count="5">
                    <Text106 Ref="119" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0,2.8,1.6</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>Text106</Name>
                      <Page isRef="20" />
                      <Parent isRef="118" />
                      <Text>宾客账</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </Text106>
                    <Text97 Ref="120" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>2.8,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>Text97</Name>
                      <Page isRef="20" />
                      <Parent isRef="118" />
                      <Text>宾客账</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </Text97>
                    <Text102 Ref="121" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>5,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Right</HorAlignment>
                      <Hyperlink>#/?name=StoreDailyReviewGuestAccount&amp;hcode={root_data.hcode}&amp;bizDate={root_data.bizDate}</Hyperlink>
                      <Margins>0,5,0,0</Margins>
                      <Name>Text102</Name>
                      <Page isRef="20" />
                      <Parent isRef="118" />
                      <Text>{root_data_revenueCount.customerBillTotalFee}</Text>
                      <TextBrush>[0:176:240]</TextBrush>
                      <TextFormat Ref="122" type="CurrencyFormat" isKey="true">
                        <DecimalDigits>2</DecimalDigits>
                        <GroupSeparator>,</GroupSeparator>
                        <NegativePattern>2</NegativePattern>
                        <PositivePattern>0</PositivePattern>
                        <Symbol>¥</Symbol>
                      </TextFormat>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </Text102>
                    <Text99 Ref="123" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>2.8,0.8,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>Text99</Name>
                      <Page isRef="20" />
                      <Parent isRef="118" />
                      <Text>总计</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </Text99>
                    <Text104 Ref="124" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>5,0.8,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Right</HorAlignment>
                      <Margins>0,5,0,0</Margins>
                      <Name>Text104</Name>
                      <Page isRef="20" />
                      <Parent isRef="118" />
                      <Text>{root_data_revenueCount.customerBillTotalFee}</Text>
                      <TextBrush>Black</TextBrush>
                      <TextFormat Ref="125" type="CurrencyFormat" isKey="true">
                        <DecimalDigits>2</DecimalDigits>
                        <GroupSeparator>,</GroupSeparator>
                        <NegativePattern>2</NegativePattern>
                        <PositivePattern>0</PositivePattern>
                        <Symbol>¥</Symbol>
                      </TextFormat>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </Text104>
                  </Components>
                  <Conditions isList="true" count="0" />
                  <DataRelationName isNull="true" />
                  <DataSourceName>root_data_revenueCount</DataSourceName>
                  <Expressions isList="true" count="0" />
                  <Filters isList="true" count="0" />
                  <Name>DataBand7</Name>
                  <Page isRef="20" />
                  <Parent isRef="107" />
                  <Sort isList="true" count="0" />
                </DataBand7>
              </Components>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Name>Panel2</Name>
              <Page isRef="20" />
              <Parent isRef="66" />
            </Panel2>
          </Components>
          <Conditions isList="true" count="1">
            <value>,EqualTo,,,String,Red,Transparent,Arial_x002C_8,True,False,,,None</value>
          </Conditions>
          <DataRelationName isNull="true" />
          <DataSourceName>root_data_revenueCount</DataSourceName>
          <Expressions isList="true" count="0" />
          <Filters isList="true" count="0" />
          <Name>DataBand5</Name>
          <Page isRef="20" />
          <Parent isRef="20" />
          <Sort isList="true" count="0" />
        </DataBand5>
        <HeaderBand4 Ref="126" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,24.8,19,1.6</ClientRectangle>
          <Components isList="true" count="6">
            <Text74 Ref="127" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,19,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text74</Name>
              <Page isRef="20" />
              <Parent isRef="126" />
              <Text>经营数据分类统计</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text74>
            <Text73 Ref="128" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.8,3.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text73</Name>
              <Page isRef="20" />
              <Parent isRef="126" />
              <Text>分类统计</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text73>
            <Text75 Ref="129" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.8,0.8,3.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text75</Name>
              <Page isRef="20" />
              <Parent isRef="126" />
              <Text>间夜数</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text75>
            <Text76 Ref="130" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.6,0.8,3.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text76</Name>
              <Page isRef="20" />
              <Parent isRef="126" />
              <Text>出租率</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text76>
            <Text77 Ref="131" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>11.4,0.8,3.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text77</Name>
              <Page isRef="20" />
              <Parent isRef="126" />
              <Text>房费</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text77>
            <Text78 Ref="132" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.2,0.8,3.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text78</Name>
              <Page isRef="20" />
              <Parent isRef="126" />
              <Text>平均房价</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text78>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>HeaderBand4</Name>
          <Page isRef="20" />
          <Parent isRef="20" />
        </HeaderBand4>
        <GroupHeaderBand1 Ref="133" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,27.2,19,0.8</ClientRectangle>
          <Components isList="true" count="1">
            <Text79 Ref="134" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,19,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text79</Name>
              <Page isRef="20" />
              <Parent isRef="133" />
              <Text>{root_data_businessData.cateGory}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text79>
          </Components>
          <Condition>{root_data_businessData.cateGory}</Condition>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>GroupHeaderBand1</Name>
          <Page isRef="20" />
          <Parent isRef="20" />
        </GroupHeaderBand1>
        <DataBand3 Ref="135" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,28.8,19,0.8</ClientRectangle>
          <Components isList="true" count="5">
            <Text80 Ref="136" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,3.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text80</Name>
              <Page isRef="20" />
              <Parent isRef="135" />
              <Text>{root_data_businessData.classificationStatistics}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text80>
            <Text81 Ref="137" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.8,0,3.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Hyperlink>#/?name=StoreDailyReviewRoomRate&amp;hcode={root_data.hcode}&amp;startDate={root_data.bizDate}&amp;endDate={root_data.bizDate}</Hyperlink>
              <Margins>5,0,0,0</Margins>
              <Name>Text81</Name>
              <Page isRef="20" />
              <Parent isRef="135" />
              <Text> {root_data_businessData.nightNum}</Text>
              <TextBrush>[0:176:240]</TextBrush>
              <TextFormat Ref="138" type="NumberFormat" isKey="true">
                <DecimalDigits>2</DecimalDigits>
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
              </TextFormat>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text81>
            <Text82 Ref="139" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.6,0,3.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text82</Name>
              <Page isRef="20" />
              <Parent isRef="135" />
              <Text>{Format("{0:P2}", root_data_businessData.nightNum / root_data_roomRevenueIndex.totalRoomNum)}
</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="140" type="PercentageFormat" isKey="true">
                <DecimalDigits>2</DecimalDigits>
                <GroupSeparator>,</GroupSeparator>
                <GroupSize>1</GroupSize>
                <NegativePattern>1</NegativePattern>
                <PositivePattern>1</PositivePattern>
                <State>DecimalDigits</State>
                <Symbol>%</Symbol>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text82>
            <Text83 Ref="141" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>11.4,0,3.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Hyperlink>#/?name=StoreDailyReviewRoomRate&amp;hcode={root_data.hcode}&amp;startDate={root_data.bizDate}&amp;endDate={root_data.bizDate}</Hyperlink>
              <Margins>0,5,0,0</Margins>
              <Name>Text83</Name>
              <Page isRef="20" />
              <Parent isRef="135" />
              <Text>{Format("{0:C2}", root_data_businessData.totalFee)} </Text>
              <TextBrush>[0:176:240]</TextBrush>
              <TextFormat Ref="142" type="CurrencyFormat" isKey="true">
                <DecimalDigits>2</DecimalDigits>
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>2</NegativePattern>
                <PositivePattern>0</PositivePattern>
                <State>DecimalDigits, DecimalSeparator, GroupSeparator, GroupSize</State>
                <Symbol>¥</Symbol>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text83>
            <Text84 Ref="143" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.2,0,3.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>Text84</Name>
              <Page isRef="20" />
              <Parent isRef="135" />
              <Text>{Format("{0:F2}", root_data_businessData.totalFee / root_data_businessData.nightNum)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="144" type="NumberFormat" isKey="true">
                <DecimalDigits>2</DecimalDigits>
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <State>DecimalDigits</State>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text84>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>root_data_businessData</DataSourceName>
          <Expressions isList="true" count="0" />
          <Filters isList="true" count="0" />
          <Name>DataBand3</Name>
          <Page isRef="20" />
          <Parent isRef="20" />
          <Sort isList="true" count="0" />
        </DataBand3>
        <GroupFooterBand1 Ref="145" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <CanBreak>True</CanBreak>
          <ClientRectangle>0,30.4,19,0.8</ClientRectangle>
          <Components isList="true" count="5">
            <Text85 Ref="146" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,3.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text85</Name>
              <Page isRef="20" />
              <Parent isRef="145" />
              <Text>小计</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text85>
            <Text86 Ref="147" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.8,0,3.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Hyperlink>#/?name=StoreDailyReviewRoomRate&amp;hcode={root_data.hcode}&amp;startDate={root_data.bizDate}&amp;endDate={root_data.bizDate}</Hyperlink>
              <Margins>5,0,0,0</Margins>
              <Name>Text86</Name>
              <Page isRef="20" />
              <Parent isRef="145" />
              <Text> {root_data_roomRevenueIndex.nightNum}</Text>
              <TextBrush>[0:176:240]</TextBrush>
              <TextFormat Ref="148" type="NumberFormat" isKey="true">
                <DecimalDigits>2</DecimalDigits>
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
              </TextFormat>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text86>
            <Text87 Ref="149" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.6,0,3.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text87</Name>
              <Page isRef="20" />
              <Parent isRef="145" />
              <Text>{Format("{0:P2}", root_data_roomRevenueIndex.nightNum / root_data_roomRevenueIndex.totalRoomNum)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="150" type="PercentageFormat" isKey="true">
                <DecimalDigits>2</DecimalDigits>
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <PositivePattern>1</PositivePattern>
                <Symbol>%</Symbol>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text87>
            <Text88 Ref="151" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>11.4,0,3.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Hyperlink>#/?name=StoreDailyReviewRoomRate&amp;hcode={root_data.hcode}&amp;startDate={root_data.bizDate}&amp;endDate={root_data.bizDate}</Hyperlink>
              <Margins>0,5,0,0</Margins>
              <Name>Text88</Name>
              <Page isRef="20" />
              <Parent isRef="145" />
              <Text>{Sum(root_data_businessData.totalFee)}</Text>
              <TextBrush>[0:176:240]</TextBrush>
              <TextFormat Ref="152" type="CurrencyFormat" isKey="true">
                <DecimalDigits>2</DecimalDigits>
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>2</NegativePattern>
                <PositivePattern>0</PositivePattern>
                <State>DecimalDigits, DecimalSeparator, GroupSeparator, GroupSize</State>
                <Symbol>¥</Symbol>
              </TextFormat>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text88>
            <Text89 Ref="153" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.2,0,3.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>Text89</Name>
              <Page isRef="20" />
              <Parent isRef="145" />
              <Text>{Format("{0:F2}", root_data_roomRevenueIndex.roomFee / root_data_roomRevenueIndex.nightNum)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text89>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>GroupFooterBand1</Name>
          <Page isRef="20" />
          <Parent isRef="20" />
        </GroupFooterBand1>
      </Components>
      <Conditions isList="true" count="0" />
      <Expressions isList="true" count="0" />
      <Guid>318e96f1b9254db59542944eb400d151</Guid>
      <Margins>1,1,1,1</Margins>
      <Name>Page1</Name>
      <PageHeight>36</PageHeight>
      <PageWidth>21</PageWidth>
      <Report isRef="0" />
    </Page1>
  </Pages>
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>Report</ReportAlias>
  <ReportAuthor>Z zx</ReportAuthor>
  <ReportChanged>7/1/2025 2:40:12 PM</ReportChanged>
  <ReportCreated>7/9/2024 2:29:50 PM</ReportCreated>
  <ReportFile>D:\11\2\pms-report\public\reports\memnage.mrt</ReportFile>
  <ReportGuid>d395241839114355a4c8d4d9797369ae</ReportGuid>
  <ReportName>Report</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2024.3.5.0</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
		#endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>