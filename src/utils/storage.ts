import pinia from '@/store'
import useSettingsStore from '@/store/modules/settings'

// const settingsStore = useSettingsStore(pinia)

const storage = {
  local: {
    has: (key: string) => {
      const settingsStore = useSettingsStore(pinia)
      return Object.prototype.hasOwnProperty.call(localStorage, `${settingsStore.settings.app?.storagePrefix}${key}`)
    },
    get: (key: string) => {
      const settingsStore = useSettingsStore(pinia)
      return localStorage.getItem(`${settingsStore.settings.app?.storagePrefix}${key}`)
    },
    set: (key: string, value: string) => {
      const settingsStore = useSettingsStore(pinia)
      localStorage.setItem(`${settingsStore.settings.app?.storagePrefix}${key}`, value)
    },
    remove: (key: string) => {
      const settingsStore = useSettingsStore(pinia)
      localStorage.removeItem(`${settingsStore.settings.app?.storagePrefix}${key}`)
    },
    clear: () => {
      localStorage.clear()
    },
  },
  session: {
    has: (key: string) => {
      const settingsStore = useSettingsStore(pinia)
      return Object.prototype.hasOwnProperty.call(sessionStorage, `${settingsStore.settings.app?.storagePrefix}${key}`)
    },
    get: (key: string) => {
      const settingsStore = useSettingsStore(pinia)
      return sessionStorage.getItem(`${settingsStore.settings.app?.storagePrefix}${key}`)
    },
    set: (key: string, value: string) => {
      const settingsStore = useSettingsStore(pinia)
      sessionStorage.setItem(`${settingsStore.settings.app?.storagePrefix}${key}`, value)
    },
    remove: (key: string) => {
      const settingsStore = useSettingsStore(pinia)
      sessionStorage.removeItem(`${settingsStore.settings.app?.storagePrefix}${key}`)
    },
    clear: () => {
      sessionStorage.clear()
    },
  },
}

export default storage
