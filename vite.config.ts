import fs from "node:fs";
import path from "node:path";
import process from "node:process";
import { defineConfig, loadEnv } from "vite";
import dayjs from "dayjs";
import pkg from "./package.json";
import createVitePlugins from "./vite/plugins";

// https://vitejs.dev/config/
export default ({ mode, command }) => {
  const env = loadEnv(mode, process.cwd());
  // 全局 scss 资源
  const scssResources = [];
  fs.readdirSync("src/assets/styles/resources").forEach((dirname) => {
    if (fs.statSync(`src/assets/styles/resources/${dirname}`).isFile()) {
      scssResources.push(`@use "src/assets/styles/resources/${dirname}" as *;`);
    }
  });
  return defineConfig({
    base: "./",
    // 开发服务器选项 https://cn.vitejs.dev/config/server-options
    server: {
      open: true,
      port: 9007,
      proxy: {
        "/proxy": {
          target: env.VITE_APP_API_BASEURL,
          changeOrigin: command === "serve" && env.VITE_OPEN_PROXY === "true",
          rewrite: (path) => path.replace(/\/proxy/, ""),
        },
      },
      host: "0.0.0.0",
    },
    // 构建选项 https://cn.vitejs.dev/config/build-options
    build: {
      outDir: mode === "production" ? "dist" : `dist-${mode}`,
      sourcemap: env.VITE_BUILD_SOURCEMAP === "true",
    },
    define: {
      __SYSTEM_INFO__: JSON.stringify({
        pkg: {
          dependencies: pkg.dependencies,
          devDependencies: pkg.devDependencies,
        },
        lastBuildTime: dayjs().format("YYYY-MM-DD HH:mm:ss"),
      }),
    },
    plugins: createVitePlugins(env, command),
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "src"),
      },
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: scssResources.join(""),
        },
      },
    },
  });
};
