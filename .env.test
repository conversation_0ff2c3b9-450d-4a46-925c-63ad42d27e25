# 应用配置面板
VITE_APP_SETTING = false
# 页面标题
VITE_APP_TITLE = 一朵花-AI酒店科技-报表系统
# 接口请求地址，会设置到 axios 的 baseURL 参数上
VITE_APP_API_BASEURL = /
# 前端链接前缀
VITE_BASE_PREFIX = /
# 体验账号和密码
VITE_APP_EXPERIENCE_ACCOUNT = ***********
VITE_APP_EXPERIENCE_PASSWORD = 123456
VITE_APP_LOGIN_URL = http://*************:4200/index.html#/login
# 返回酒店
VITE_APP_BACK_URL = http://*************:4200/#/front/room-state/index
# 调试工具，可设置 eruda 或 vconsole，如果不需要开启则留空
VITE_APP_DEBUG_TOOL =

# 是否在打包时启用 Mock
VITE_BUILD_MOCK = true
# 是否在打包时生成 sourcemap
VITE_BUILD_SOURCEMAP = true
# 是否在打包时开启压缩，支持 gzip 和 brotli
VITE_BUILD_COMPRESS =
# 是否在打包后生成存档，支持 zip 和 tar
VITE_BUILD_ARCHIVE =
