<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <CalculationMode>Interpretation</CalculationMode>
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="1">
      <guestDetail Ref="2" type="Stimulsoft.Report.Dictionary.StiJsonDatabase" isKey="true">
        <Alias>guestDetail</Alias>
        <HeadersString />
        <Key />
        <Name>guestDetail</Name>
        <PathData>C:\Users\<USER>\OneDrive\桌面\报表\guestDetail.json</PathData>
      </guestDetail>
    </Databases>
    <DataSources isList="true" count="3">
      <root Ref="3" type="DataTableSource" isKey="true">
        <Alias>root</Alias>
        <Columns isList="true" count="4">
          <value>code,System.Decimal</value>
          <value>data,System.String</value>
          <value>msg,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>9ee62550156540a0b0ea3b9f85050f78</Key>
        <Name>root</Name>
        <NameInSource>guestDetail.root</NameInSource>
      </root>
      <root_data Ref="4" type="DataTableSource" isKey="true">
        <Alias>root_data</Alias>
        <Columns isList="true" count="6">
          <value>list,System.String</value>
          <value>hname,System.String</value>
          <value>bizDate,System.String</value>
          <value>lastSelectTime,System.String</value>
          <value>operator,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>093e3ac766444e39aead84cb0abede8c</Key>
        <Name>root_data</Name>
        <NameInSource>guestDetail.root_data</NameInSource>
      </root_data>
      <root_data_list Ref="5" type="DataTableSource" isKey="true">
        <Alias>root_data_list</Alias>
        <Columns isList="true" count="20">
          <value>id,System.Decimal</value>
          <value>gcode,System.String</value>
          <value>hcode,System.String</value>
          <value>orderNo,System.String</value>
          <value>contact,System.String</value>
          <value>name,System.String</value>
          <value>rCode,System.String</value>
          <value>rNo,System.String</value>
          <value>teamCode,System.String</value>
          <value>teamName,System.String</value>
          <value>checkinTime,System.String</value>
          <value>checkoutTime,System.String</value>
          <value>todayConsume,System.Decimal</value>
          <value>todayPay,System.Decimal</value>
          <value>customerBill,System.Decimal</value>
          <value>state,System.String</value>
          <value>stateName,System.String</value>
          <value>bizDate,System.String</value>
          <value>isCash,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>218fa9257d9249d5a7d4dc73f4a3de5b</Key>
        <Name>root_data_list</Name>
        <NameInSource>guestDetail.root_data_list</NameInSource>
      </root_data_list>
    </DataSources>
    <Relations isList="true" count="2">
      <root Ref="6" type="DataRelation" isKey="true">
        <Alias>root</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="4" />
        <Dictionary isRef="1" />
        <Name>root</Name>
        <NameInSource>root_data</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>data</value>
        </ParentColumns>
        <ParentSource isRef="3" />
      </root>
      <root_data Ref="7" type="DataRelation" isKey="true">
        <Alias>root_data</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="5" />
        <Dictionary isRef="1" />
        <Name>root_data</Name>
        <NameInSource>root_data_list</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>list</value>
        </ParentColumns>
        <ParentSource isRef="4" />
      </root_data>
    </Relations>
    <Report isRef="0" />
    <Resources isList="true" count="0" />
    <UserFunctions isList="true" count="0" />
    <Variables isList="true" count="0" />
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <Key>88d41c5994674544aedae4f661ef15a1</Key>
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="8" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="8">
        <PageHeaderBand1 Ref="9" type="PageHeaderBand" isKey="true">
          <Border>Bottom;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,28,0.4</ClientRectangle>
          <Components isList="true" count="2">
            <Text2 Ref="10" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0.01,0,7,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Name>Text2</Name>
              <Page isRef="8" />
              <Parent isRef="9" />
              <Text>{root_data.hname}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text2>
            <Text8 Ref="11" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>22.4,0,5.6,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Name>Text8</Name>
              <Page isRef="8" />
              <Parent isRef="9" />
              <Text>第{PageNumber}页，共{TotalPageCount}页</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text8>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>PageHeaderBand1</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
        </PageHeaderBand1>
        <PageFooterBand1 Ref="12" type="PageFooterBand" isKey="true">
          <Border>Bottom;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>0,18.2,28,0.8</ClientRectangle>
          <Components isList="true" count="2">
            <Text45 Ref="13" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.2,2.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <Name>Text45</Name>
              <Page isRef="8" />
              <Parent isRef="12" />
              <Text>{root_data.operator_}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text45>
            <Text46 Ref="14" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>24.2,0.2,3.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Right</HorAlignment>
              <Name>Text46</Name>
              <Page isRef="8" />
              <Parent isRef="12" />
              <Text>{root_data.lastSelectTime.Substring(0, 19).Replace("-", "/")}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text46>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>PageFooterBand1</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
        </PageFooterBand1>
        <ReportTitleBand1 Ref="15" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,1.6,28,2</ClientRectangle>
          <Components isList="true" count="2">
            <Text1 Ref="16" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>11.9,0.4,4.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,12,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text1</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>宾客账明细报表（固化）</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text1>
            <Text5 Ref="17" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.2,24.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Name>Text5</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>门店:{root_data.hname}  营业日:{root_data.bizDate.Substring(0, 10)} 最后查询时间：{root_data.lastSelectTime.Substring(0, 19).Replace("-", "/")}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text5>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>ReportTitleBand1</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
        </ReportTitleBand1>
        <ColumnHeaderBand1 Ref="18" type="Stimulsoft.Report.Components.StiColumnHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,4.4,28,0.8</ClientRectangle>
          <Components isList="true" count="11">
            <Text3 Ref="19" type="Text" isKey="true">
              <Alias>（固化）</Alias>
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,1.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text3</Name>
              <Page isRef="8" />
              <Parent isRef="18" />
              <Text>序号
</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text3>
            <Text4 Ref="20" type="Text" isKey="true">
              <Alias>（固化）</Alias>
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.6,0,4.1,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text4</Name>
              <Page isRef="8" />
              <Parent isRef="18" />
              <Text>订单号</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text4>
            <Text7 Ref="21" type="Text" isKey="true">
              <Alias>（固化）</Alias>
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.8,0,2.1,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text7</Name>
              <Page isRef="8" />
              <Parent isRef="18" />
              <Text>客人姓名</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text7>
            <Text12 Ref="22" type="Text" isKey="true">
              <Alias>（固化）</Alias>
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5.7,0,2.1,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text12</Name>
              <Page isRef="8" />
              <Parent isRef="18" />
              <Text>联系人</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text12>
            <Text6 Ref="23" type="Text" isKey="true">
              <Alias>（固化）</Alias>
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9.9,0,1.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text6</Name>
              <Page isRef="8" />
              <Parent isRef="18" />
              <Text>房号</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text6>
            <Text9 Ref="24" type="Text" isKey="true">
              <Alias>（固化）</Alias>
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>11.7,0,2.5,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text9</Name>
              <Page isRef="8" />
              <Parent isRef="18" />
              <Text>团队名称
</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text9>
            <Text10 Ref="25" type="Text" isKey="true">
              <Alias>（固化）</Alias>
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>17.8,0,3.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Interaction Ref="26" type="Stimulsoft.Report.Components.StiInteraction" isKey="true">
                <SortingColumn>DataBand1.checkoutTime</SortingColumn>
              </Interaction>
              <Name>Text10</Name>
              <Page isRef="8" />
              <Parent isRef="18" />
              <Text>离店时间</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text10>
            <Text11 Ref="27" type="Text" isKey="true">
              <Alias>（固化）</Alias>
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14.2,0,3.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Interaction Ref="28" type="Stimulsoft.Report.Components.StiInteraction" isKey="true">
                <SortingColumn>DataBand1.checkinTime</SortingColumn>
              </Interaction>
              <Name>Text11</Name>
              <Page isRef="8" />
              <Parent isRef="18" />
              <Text>入住时间</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text11>
            <Text13 Ref="29" type="Text" isKey="true">
              <Alias>（固化）</Alias>
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>21.4,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Interaction Ref="30" type="Stimulsoft.Report.Components.StiInteraction" isKey="true">
                <SortingColumn>DataBand1.todayConsume</SortingColumn>
              </Interaction>
              <Name>Text13</Name>
              <Page isRef="8" />
              <Parent isRef="18" />
              <Text>今日发生消费</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text13>
            <Text19 Ref="31" type="Text" isKey="true">
              <Alias>（固化）</Alias>
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>26,0,2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Interaction Ref="32" type="Stimulsoft.Report.Components.StiInteraction" isKey="true">
                <SortingColumn>DataBand1.customerBill</SortingColumn>
              </Interaction>
              <Name>Text19</Name>
              <Page isRef="8" />
              <Parent isRef="18" />
              <Text>宾客账
</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text19>
            <Text21 Ref="33" type="Text" isKey="true">
              <Alias>（固化）</Alias>
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>23.8,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Interaction Ref="34" type="Stimulsoft.Report.Components.StiInteraction" isKey="true">
                <SortingColumn>DataBand1.todayPay</SortingColumn>
              </Interaction>
              <Name>Text21</Name>
              <Page isRef="8" />
              <Parent isRef="18" />
              <Text>今日发生付款</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text21>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>ColumnHeaderBand1</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
        </ColumnHeaderBand1>
        <GroupHeaderBand1 Ref="35" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,6,28,0.8</ClientRectangle>
          <Components isList="true" count="1">
            <Text30 Ref="36" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,28,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text30</Name>
              <Page isRef="8" />
              <Parent isRef="35" />
              <Text>{root_data_list.stateName}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text30>
          </Components>
          <Condition>{root_data_list.stateName}</Condition>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>GroupHeaderBand1</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
        </GroupHeaderBand1>
        <DataBand1 Ref="37" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,7.6,28,0.8</ClientRectangle>
          <Components isList="true" count="11">
            <Text14 Ref="38" type="Text" isKey="true">
              <Alias>（固化）</Alias>
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,1.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text14</Name>
              <Page isRef="8" />
              <Parent isRef="37" />
              <Text>{Line}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text14>
            <Text15 Ref="39" type="Text" isKey="true">
              <Alias>（固化）</Alias>
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.6,0,4.1,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text15</Name>
              <Page isRef="8" />
              <Parent isRef="37" />
              <Text>{root_data_list.orderNo}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text15>
            <Text16 Ref="40" type="Text" isKey="true">
              <Alias>（固化）</Alias>
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.8,0,2.1,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text16</Name>
              <Page isRef="8" />
              <Parent isRef="37" />
              <Text>{root_data_list.name}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text16>
            <Text17 Ref="41" type="Text" isKey="true">
              <Alias>（固化）</Alias>
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5.7,0,2.1,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text17</Name>
              <Page isRef="8" />
              <Parent isRef="37" />
              <Text>{root_data_list.contact}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text17>
            <Text18 Ref="42" type="Text" isKey="true">
              <Alias>（固化）</Alias>
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9.9,0,1.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text18</Name>
              <Page isRef="8" />
              <Parent isRef="37" />
              <Text>{root_data_list.rNo}
</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text18>
            <Text20 Ref="43" type="Text" isKey="true">
              <Alias>（固化）</Alias>
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>11.7,0,2.5,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text20</Name>
              <Page isRef="8" />
              <Parent isRef="37" />
              <Text>{root_data_list.teamName}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text20>
            <Text22 Ref="44" type="Text" isKey="true">
              <Alias>（固化）</Alias>
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>17.8,0,3.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text22</Name>
              <Page isRef="8" />
              <Parent isRef="37" />
              <Text>{root_data_list.checkoutTime}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="45" type="DateFormat" isKey="true">
                <StringFormat>G</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text22>
            <Text23 Ref="46" type="Text" isKey="true">
              <Alias>（固化）</Alias>
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14.2,0,3.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text23</Name>
              <Page isRef="8" />
              <Parent isRef="37" />
              <Text>{root_data_list.checkinTime}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="47" type="DateFormat" isKey="true">
                <StringFormat>G</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text23>
            <Text24 Ref="48" type="Text" isKey="true">
              <Alias>（固化）</Alias>
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>21.4,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>Text24</Name>
              <Page isRef="8" />
              <Parent isRef="37" />
              <Text>{root_data_list.todayConsume}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="49" type="CurrencyFormat" isKey="true">
                <DecimalDigits>2</DecimalDigits>
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>2</NegativePattern>
                <PositivePattern>0</PositivePattern>
                <Symbol>¥</Symbol>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text24>
            <Text25 Ref="50" type="Text" isKey="true">
              <Alias>（固化）</Alias>
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>26,0,2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>Text25</Name>
              <Page isRef="8" />
              <Parent isRef="37" />
              <Text>{root_data_list.customerBill}</Text>
              <TextBrush>[0:176:240]</TextBrush>
              <TextFormat Ref="51" type="CurrencyFormat" isKey="true">
                <DecimalDigits>2</DecimalDigits>
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>2</NegativePattern>
                <PositivePattern>0</PositivePattern>
                <Symbol>¥</Symbol>
              </TextFormat>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text25>
            <Text26 Ref="52" type="Text" isKey="true">
              <Alias>（固化）</Alias>
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>23.8,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>Text26</Name>
              <Page isRef="8" />
              <Parent isRef="37" />
              <Text>{root_data_list.todayPay}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="53" type="CurrencyFormat" isKey="true">
                <DecimalDigits>2</DecimalDigits>
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>2</NegativePattern>
                <PositivePattern>0</PositivePattern>
                <Symbol>¥</Symbol>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text26>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>root_data_list</DataSourceName>
          <Expressions isList="true" count="0" />
          <Filters isList="true" count="0" />
          <Name>DataBand1</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
          <Sort isList="true" count="0" />
        </DataBand1>
        <GroupFooterBand1 Ref="54" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,9.2,28,0.8</ClientRectangle>
          <Components isList="true" count="4">
            <Text31 Ref="55" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,21.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text31</Name>
              <Page isRef="8" />
              <Parent isRef="54" />
              <Text>小计</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text31>
            <Text32 Ref="56" type="Text" isKey="true">
              <Alias>（固化）</Alias>
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>21.4,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>Text32</Name>
              <Page isRef="8" />
              <Parent isRef="54" />
              <Text>{Sum(root_data_list.todayConsume)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="57" type="CurrencyFormat" isKey="true">
                <DecimalDigits>2</DecimalDigits>
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>2</NegativePattern>
                <PositivePattern>0</PositivePattern>
                <Symbol>¥</Symbol>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text32>
            <Text33 Ref="58" type="Text" isKey="true">
              <Alias>（固化）</Alias>
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>26,0,2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>Text33</Name>
              <Page isRef="8" />
              <Parent isRef="54" />
              <Text>{Sum(root_data_list.customerBill)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="59" type="CurrencyFormat" isKey="true">
                <DecimalDigits>2</DecimalDigits>
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>2</NegativePattern>
                <PositivePattern>0</PositivePattern>
                <Symbol>¥</Symbol>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text33>
            <Text34 Ref="60" type="Text" isKey="true">
              <Alias>（固化）</Alias>
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>23.8,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>Text34</Name>
              <Page isRef="8" />
              <Parent isRef="54" />
              <Text>{Sum(root_data_list.todayPay)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="61" type="CurrencyFormat" isKey="true">
                <DecimalDigits>2</DecimalDigits>
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>2</NegativePattern>
                <PositivePattern>0</PositivePattern>
                <Symbol>¥</Symbol>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text34>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>GroupFooterBand1</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
        </GroupFooterBand1>
        <FooterBand1 Ref="62" type="FooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,10.8,28,0.8</ClientRectangle>
          <Components isList="true" count="4">
            <Text44 Ref="63" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,21.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text44</Name>
              <Page isRef="8" />
              <Parent isRef="62" />
              <Text>总计</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text44>
            <Text27 Ref="64" type="Text" isKey="true">
              <Alias>（固化）</Alias>
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>21.4,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>Text27</Name>
              <Page isRef="8" />
              <Parent isRef="62" />
              <Text>{Sum(root_data_list.todayConsume)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="65" type="CurrencyFormat" isKey="true">
                <DecimalDigits>2</DecimalDigits>
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>2</NegativePattern>
                <PositivePattern>0</PositivePattern>
                <Symbol>¥</Symbol>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text27>
            <Text28 Ref="66" type="Text" isKey="true">
              <Alias>（固化）</Alias>
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>26,0,2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>Text28</Name>
              <Page isRef="8" />
              <Parent isRef="62" />
              <Text>{Sum(root_data_list.customerBill)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="67" type="CurrencyFormat" isKey="true">
                <DecimalDigits>2</DecimalDigits>
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>2</NegativePattern>
                <PositivePattern>0</PositivePattern>
                <Symbol>¥</Symbol>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text28>
            <Text29 Ref="68" type="Text" isKey="true">
              <Alias>（固化）</Alias>
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>23.8,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>Text29</Name>
              <Page isRef="8" />
              <Parent isRef="62" />
              <Text>{Sum(root_data_list.todayPay)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="69" type="CurrencyFormat" isKey="true">
                <DecimalDigits>2</DecimalDigits>
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>2</NegativePattern>
                <PositivePattern>0</PositivePattern>
                <Symbol>¥</Symbol>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text29>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>FooterBand1</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
        </FooterBand1>
      </Components>
      <Conditions isList="true" count="0" />
      <Expressions isList="true" count="0" />
      <Guid>04a5ebcbe766417d86ab74bfd95d2611</Guid>
      <Margins>1,1,1,1</Margins>
      <Name>Page1</Name>
      <Orientation>Landscape</Orientation>
      <PageHeight>21</PageHeight>
      <PageWidth>30</PageWidth>
      <Report isRef="0" />
    </Page1>
  </Pages>
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>Report</ReportAlias>
  <ReportAuthor>Z zx</ReportAuthor>
  <ReportChanged>7/1/2025 2:37:46 PM</ReportChanged>
  <ReportCreated>7/9/2024 10:31:57 AM</ReportCreated>
  <ReportFile>D:\11\2\pms-report\public\reports\guestDetailReport.mrt</ReportFile>
  <ReportGuid>9df8d87c20f745118ab04547735385b3</ReportGuid>
  <ReportName>Report</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2024.3.5.0</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
		#endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>