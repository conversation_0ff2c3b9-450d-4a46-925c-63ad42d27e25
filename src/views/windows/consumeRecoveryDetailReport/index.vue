<i18n>
{
  "zh": {
    "search": {
      "store": "门店",
      "bizDate": "营业日",
      "timeType": "时间类型",
      "statChannel": "统计渠道",
      "guestSource": "客源",
      "consumeSubject": "消费科目",
      "operator": "操作员",
      "shift": "班次",
      "query": "查询",
      "selectStore": "请选择门店",
      "selectChannel": "请选择统计渠道",
      "selectGuestSource": "请选择客源",
      "selectSubject": "请选择消费科目",
      "selectOperator": "请选择操作员",
      "selectShift": "请选择班次",
      "startDate": "开始日期",
      "endDate": "结束日期",
      "to": "至",
      "today": "今天",
      "thisWeek": "本周",
      "thisMonth": "本月",
      "operationTime": "操作时间"
    }
  },
  "en": {
    "search": {
      "store": "Store",
      "bizDate": "Biz Date",
      "timeType": "Time Type",
      "statChannel": "Channel",
      "guestSource": "Source",
      "consumeSubject": "Subject",
      "operator": "Operator",
      "shift": "Shift",
      "query": "Search",
      "selectStore": "Select Store",
      "selectChannel": "Select Stat Channel",
      "selectGuestSource": "Select Guest Source",
      "selectSubject": "Select Consume Subject",
      "selectOperator": "Select Operator",
      "selectShift": "Select Shift",
      "startDate": "Start Date",
      "endDate": "End Date",
      "to": "to",
      "today": "Today",
      "thisWeek": "This Week",
      "thisMonth": "This Month",
      "operationTime": "Operation Time"
    }
  },
  "km": {
    "search": {
      "store": "ហាង",
      "bizDate": "កាលបរិច្ឆេទអាជីវកម្ម",
      "timeType": "ប្រភេទពេលវេលា",
      "statChannel": "ឆានែលស្ថិតិ",
      "guestSource": "ប្រភពភ្ញៀវ",
      "consumeSubject": "ប្រធានបទចំណាយ",
      "operator": "ប្រតិបត្តិករ",
      "shift": "វេន",
      "query": "ស្វែងរក",
      "selectStore": "សូមជ្រើសរើសហាង",
      "selectChannel": "សូមជ្រើសរើសឆានែលស្ថិតិ",
      "selectGuestSource": "សូមជ្រើសរើសប្រភពភ្ញៀវ",
      "selectSubject": "សូមជ្រើសរើសប្រធានបទចំណាយ",
      "selectOperator": "សូមជ្រើសរើសប្រតិបត្តិករ",
      "selectShift": "សូមជ្រើសរើសវេន",
      "startDate": "កាលបរិច្ឆេទចាប់ផ្តើម",
      "endDate": "កាលបរិច្ឆេទបញ្ចប់",
      "to": "ទៅ",
      "today": "ថ្ងៃនេះ",
      "thisWeek": "សប្តាហ៍នេះ",
      "thisMonth": "ខែនេះ",
      "operationTime": "ពេលវេលាប្រតិបត្តិការ"
    }
  }
}
</i18n>

<script setup lang="ts">
import { dayjs } from 'element-plus'
import { useI18n } from 'vue-i18n'
import {
  getConsumeRecoveryDetailReport,
  getShiftTimeList,
  listConsumeAccountApi,
  simpleListApi,
  simpleUserListApi,
} from '@/api/modules/pms/shiftTimeApi.ts'
import useUserStore from '@/store/modules/user.ts'
import { getFirstDayOfMonth, getMonday } from '@/utils/tool'
import { ReportUtils } from '@/utils/report-utils.ts'

const userStore = useUserStore()
const { t } = useI18n()
const route = useRoute()

const data = ref({
  searchFold: false,
  // 表格是否自适应高度
  tableAutoHeight: true,
  labelState: '1',
  bizDate: [dayjs().subtract(0, 'day').toDate(), dayjs().toDate()] as any, // 起始时间为当月默认天数，结束时间为当天
  search: {
    gcode: userStore.gcode,
    hcode: userStore.hcode,
    timeType: '1',
    shiftNos: [] as string[],
    subCodes: [] as string[], // 消费科目
    operator: '', // 操作员
  },
})

const storeProps = { multiple: true }
const shortcuts = [
  {
    text: t('search.today'),
    value: () => {
      const end = new Date()
      const start = new Date()
      return [start, end]
    },
  },
  {
    text: t('search.thisWeek'),
    value: () => {
      const end = new Date()
      const start = getMonday()
      return [start, end]
    },
  },
  {
    text: t('search.thisMonth'),
    value: () => {
      const end = new Date()
      const start = getFirstDayOfMonth()
      return [start, end]
    },
  },
]


const simpleList = ref<{ hcode: string, hname: string }[]>([])
const subjectList = ref<{ code: string, name: string }[]>([])
const operators = ref<{ username: string, nickname: string }[]>([])
const shifts = ref<{ shiftCode: string, shiftName: string }[]>([])

// 监听门店变化，重新查询相关数据
watch(() => data.value.search.hcode, (newHcode) => {
  if (newHcode) {
    // 切换门店时清空已选择的数据
    data.value.search.operator = ''
    data.value.search.shiftNos = []

    // 重新加载数据
    getOperatorList()
    getShiftTimeData()
  }
})
const loading = ref(false)
function onSearch() {
  loading.value = true
  const timeData = {
    startDate: dayjs(data.value.bizDate[0]).format('YYYY-MM-DD'),
    endDate: dayjs(data.value.bizDate[1]).format('YYYY-MM-DD'),
  }

  // 处理数组参数，将数组转换为逗号分隔的字符串
  const searchParams = {
    ...data.value.search,
    ...timeData,
    shiftNos: data.value.search.shiftNos.length > 0 ? data.value.search.shiftNos.join(',') : undefined,
    subCodes: data.value.search.subCodes.length > 0 ? data.value.search.subCodes.join(',') : undefined,
  }

  getConsumeRecoveryDetailReport(searchParams).then((res) => {
    nextTick(() => {
      setJson(res)
      loading.value = false
    })
  }).catch(() => {
    loading.value = false
  })
}

async function setJson(json: any) {
  const viewer = ReportUtils.getViewer('ConsumeRecoveryDetailStiViewer')
  const report = ReportUtils.getReportObj('reports/consumeRecoveryDetailReport.mrt', json)

  ReportUtils.loadData(viewer, report, 'ConsumeRecoveryDetailReport')
}





async function getsimpleList() {
  const params = {
    gcode: userStore.gcode,
    hcode: data.value.search.hcode,
  }
  await simpleListApi(params).then((res) => {
    simpleList.value = res.data
  })
}

async function getConsumeAccountList() {
  const params = {
    gcode: userStore.gcode,
  }
  await listConsumeAccountApi(params).then((res) => {
    subjectList.value = res.data
  })
}

async function getOperatorList() {
  const params = {
    gcode: userStore.gcode,
    hcode: data.value.search.hcode,
  }
  await simpleUserListApi(params).then((res) => {
    operators.value = res.data
    // data.value.search.operator = res.data && res.data.length > 0 ? res.data[0].username : ''
  })
}
async function getShiftTimeData() {
  await getShiftTimeList({ gcode: userStore.gcode, hcode: data.value.search.hcode, state: '1' }).then((res) => {
    shifts.value = res.data
  })
}

async function getQueryParams() {
  const queryParams = route.query || {}
  if (!queryParams || JSON.stringify(queryParams) === '{}') {
    return
  }
  if (!queryParams.name || queryParams.name != 'ConsumeRecoveryDetail') {
    return
  }

  // 开始结束时间
  if (queryParams.startDate && queryParams.endDate) {
    data.value.bizDate = [queryParams.startDate, queryParams.endDate]
  }
  if (queryParams.hcode) {
    data.value.search.hcode = String(queryParams.hcode)
  }
  if (queryParams.subCode) {
    data.value.search.subCodes.push(String(queryParams.subCode))
  }
  if (queryParams.shiftNo && queryParams.shiftNo !== '') {
    data.value.search.shiftNos = String(queryParams.shiftNo).split(',')
  }
  if (queryParams.operator) {
    data.value.search.operator = String(queryParams.operator)
  }
}

onMounted(async () => {
  await getQueryParams()
  await getShiftTimeData()
  await getsimpleList()
  await getOperatorList()
  await getConsumeAccountList()
  onSearch()
})
// -----------最多只能选择一个月，且只能选昨天之前的-----------------
const startVal = ref(null)
/** 选择第一个时间的当月天数 */
const daysInMonth = ref(0)
function calendarChange(e) {
  if (e[0] && !e[1]) {
    startVal.value = e[0]
    daysInMonth.value = dayjs(e[0]).daysInMonth()
  }
}
/** 选择第一个时间 */
function visibleChange(e) {
  if (e) {
    startVal.value = null
  }
}
/** 禁止选择一个上下自然月之外时间 */
function disabledDate(time: Date) {
  const flag_yestodayAfter = dayjs(time).isAfter(dayjs().subtract(0, 'day'), 'day')
  if (!startVal.value) {
    return flag_yestodayAfter
  }
  const range = [dayjs(startVal.value).subtract(daysInMonth.value, 'day'), dayjs(startVal.value).add(daysInMonth.value, 'day')]
  const cur = dayjs(time)
  return cur < range[0] || cur > range[1] || flag_yestodayAfter
}
// -----------最多只能选择一个月，且只能选今天(包含)之前的-----------------
</script>

<template>
  <div :class="{ 'absolute-container': data.tableAutoHeight }">
    <page-main>
      <search-bar :fold="data.searchFold" :show-toggle="false">
        <el-form :model="data.search" size="default" label-width="80px" inline-message inline class="search-form">
          <el-form-item :label="t('search.store')" style="width: 260px;">
            <el-select v-model="data.search.hcode" :placeholder="t('search.selectStore')" filterable>
              <el-option v-for="item in simpleList" :key="item.hcode" :label="item.hname" :value="item.hcode" />
            </el-select>
          </el-form-item>
          <el-form-item :label="t('search.bizDate')">
            <el-date-picker
              v-model="data.bizDate" type="daterange"
              :range-separator="t('search.to')" :start-placeholder="t('search.startDate')"
              :end-placeholder="t('search.endDate')" :shortcuts="shortcuts"
              :clearable="false"
              :disabled-date="disabledDate"
              @calendar-change="calendarChange"
              @visible-change="visibleChange"
            />
          </el-form-item>

          <el-form-item :label="t('search.consumeSubject')" style="width: 200px;">
            <el-select v-model="data.search.subCodes" multiple style="width: 200px;" :placeholder="t('search.selectSubject')" clearable filterable>
              <el-option v-for="item in subjectList" :key="item.code" :label="item.name" :value="item.code" />
            </el-select>
          </el-form-item>
          <el-form-item :label="t('search.operator')" style="width: 200px;">
            <el-select v-model="data.search.operator" :placeholder="t('search.selectOperator')" style="width: 150px;" clearable filterable>
              <el-option v-for="item in operators" :key="item.username" :label="item.nickname" :value="item.username" />
            </el-select>
          </el-form-item>
          <el-form-item :label="t('search.shift')" style="width: 200px;">
            <el-select v-model="data.search.shiftNos" multiple :placeholder="t('search.selectShift')" clearable filterable>
              <el-option v-for="item in shifts" :key="item.shiftCode" :label="item.shiftName" :value="item.shiftCode" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSearch">
              {{ t('search.query') }}
            </el-button>
          </el-form-item>
        </el-form>
      </search-bar>
      <div id="ConsumeRecoveryDetailReport">
        <el-skeleton :loading="loading" animated :rows="30" />
      </div>
    </page-main>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-radio) {
  margin-right: 14px;
}
</style>
