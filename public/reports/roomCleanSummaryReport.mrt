<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <CalculationMode>Interpretation</CalculationMode>
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="1">
      <roomCleanSummary Ref="2" type="Stimulsoft.Report.Dictionary.StiJsonDatabase" isKey="true">
        <Alias>roomCleanSummary</Alias>
        <HeadersString />
        <Key />
        <Name>roomCleanSummary</Name>
        <PathData>D:\11\2\pms-report\public\reports\json\roomCleanSummary.json</PathData>
      </roomCleanSummary>
    </Databases>
    <DataSources isList="true" count="4">
      <root Ref="3" type="DataTableSource" isKey="true">
        <Alias>root</Alias>
        <Columns isList="true" count="4">
          <value>code,System.Decimal</value>
          <value>data,System.String</value>
          <value>msg,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>9280ad1e2825407a874ac351102366e9</Key>
        <Name>root</Name>
        <NameInSource>roomCleanSummary.root</NameInSource>
      </root>
      <root_data Ref="4" type="DataTableSource" isKey="true">
        <Alias>root_data</Alias>
        <Columns isList="true" count="9">
          <value>hname,System.String</value>
          <value>startDate,System.String</value>
          <value>endDate,System.String</value>
          <value>lastSelectTime,System.String</value>
          <value>operator,System.String</value>
          <value>list,System.String</value>
          <value>VDTotalSum,System.Decimal</value>
          <value>ODTotalSum,System.Decimal</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>d4972af65b0c48fcbca4fd68edd850da</Key>
        <Name>root_data</Name>
        <NameInSource>roomCleanSummary.root_data</NameInSource>
      </root_data>
      <root_data_list Ref="5" type="DataTableSource" isKey="true">
        <Alias>root_data_list</Alias>
        <Columns isList="true" count="4">
          <value>cleaner,System.String</value>
          <value>cleanerName,System.String</value>
          <value>dataList,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>db7e3d591ea949d8b9ec10f87d0fac74</Key>
        <Name>root_data_list</Name>
        <NameInSource>roomCleanSummary.root_data_list</NameInSource>
      </root_data_list>
      <root_data_list_dataList Ref="6" type="DataTableSource" isKey="true">
        <Alias>root_data_list_dataList</Alias>
        <Columns isList="true" count="7">
          <value>cleanState,System.String</value>
          <value>sum,System.Decimal</value>
          <value>rtCode,System.String</value>
          <value>rtName,System.String</value>
          <value>VDSum,System.Decimal</value>
          <value>ODSum,System.Decimal</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>adbdc15e75034aa2ae9a5fc749ed53be</Key>
        <Name>root_data_list_dataList</Name>
        <NameInSource>roomCleanSummary.root_data_list_dataList</NameInSource>
      </root_data_list_dataList>
    </DataSources>
    <Relations isList="true" count="3">
      <root Ref="7" type="DataRelation" isKey="true">
        <Alias>root</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="4" />
        <Dictionary isRef="1" />
        <Name>root</Name>
        <NameInSource>root_data</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>data</value>
        </ParentColumns>
        <ParentSource isRef="3" />
      </root>
      <root_data Ref="8" type="DataRelation" isKey="true">
        <Alias>root_data</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="5" />
        <Dictionary isRef="1" />
        <Name>root_data</Name>
        <NameInSource>root_data_list</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>list</value>
        </ParentColumns>
        <ParentSource isRef="4" />
      </root_data>
      <root_data_list Ref="9" type="DataRelation" isKey="true">
        <Alias>root_data_list</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="6" />
        <Dictionary isRef="1" />
        <Name>root_data_list</Name>
        <NameInSource>root_data_list_dataList</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>dataList</value>
        </ParentColumns>
        <ParentSource isRef="5" />
      </root_data_list>
    </Relations>
    <Report isRef="0" />
    <Resources isList="true" count="0" />
    <UserFunctions isList="true" count="0" />
    <Variables isList="true" count="0" />
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <Key>07ef4833e866497cb7dbfed0137568fa</Key>
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="10" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="6">
        <页眉1 Ref="11" type="PageHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,19,0.8</ClientRectangle>
          <Components isList="true" count="2">
            <Text2 Ref="12" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,7,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Name>Text2</Name>
              <Page isRef="10" />
              <Parent isRef="11" />
              <Text>{root_data.hname}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text2>
            <Text6 Ref="13" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>13.4,0,5.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Name>Text6</Name>
              <Page isRef="10" />
              <Parent isRef="11" />
              <Text>第{PageNumber}页,共{TotalPageCount}页</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text6>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>页眉1</Name>
          <Page isRef="10" />
          <Parent isRef="10" />
        </页眉1>
        <页脚1 Ref="14" type="PageFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,26.9,19,0.8</ClientRectangle>
          <Components isList="true" count="2">
            <Text45 Ref="15" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.1,2.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Name>Text45</Name>
              <Page isRef="10" />
              <Parent isRef="14" />
              <Text>{root_data.operator_}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text45>
            <Text46 Ref="16" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>15.2,0.1,3.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Name>Text46</Name>
              <Page isRef="10" />
              <Parent isRef="14" />
              <Text>{root_data.lastSelectTime.Substring(0, 19).Replace("-", "/")}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text46>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>页脚1</Name>
          <Page isRef="10" />
          <Parent isRef="10" />
        </页脚1>
        <报表标题区1 Ref="17" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,2,19,2</ClientRectangle>
          <Components isList="true" count="2">
            <Text1 Ref="18" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>8.2,0,3.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,12,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text1</Name>
              <Page isRef="10" />
              <Parent isRef="17" />
              <Text>房扫汇总报表</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text1>
            <Text5 Ref="19" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1,17.4,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Name>Text5</Name>
              <Page isRef="10" />
              <Parent isRef="17" />
              <Text>门店:{root_data.hname}  营业日:{root_data.startDate.Substring(0, 10)} - {root_data.endDate.Substring(0, 10)}  最后查询时间：{root_data.lastSelectTime.Substring(0, 19).Replace("-", "/")}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text5>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>报表标题区1</Name>
          <Page isRef="10" />
          <Parent isRef="10" />
        </报表标题区1>
        <页眉2 Ref="20" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,4.8,19,0.8</ClientRectangle>
          <Components isList="true" count="6">
            <Text3 Ref="21" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text3</Name>
              <Page isRef="10" />
              <Parent isRef="20" />
              <Text>序号</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text3>
            <文本1 Ref="22" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.2,0,3.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本1</Name>
              <Page isRef="10" />
              <Parent isRef="20" />
              <Text>服务员</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本1>
            <文本2 Ref="23" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5.6,0,3.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本2</Name>
              <Page isRef="10" />
              <Parent isRef="20" />
              <Text>房型</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本2>
            <文本3 Ref="24" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9,0,3.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本3</Name>
              <Page isRef="10" />
              <Parent isRef="20" />
              <Text>空脏</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本3>
            <文本4 Ref="25" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>12.4,0,3.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本4</Name>
              <Page isRef="10" />
              <Parent isRef="20" />
              <Text>住脏</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本4>
            <文本5 Ref="26" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.8,0,3.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本5</Name>
              <Page isRef="10" />
              <Parent isRef="20" />
              <Text>小计</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本5>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>页眉2</Name>
          <Page isRef="10" />
          <Parent isRef="10" />
        </页眉2>
        <数据区1 Ref="27" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <CanShrink>True</CanShrink>
          <ClientRectangle>0,6.4,19,2.8</ClientRectangle>
          <Components isList="true" count="3">
            <面板1 Ref="28" type="Panel" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>5.6,0,13.4,1.6</ClientRectangle>
              <Components isList="true" count="2">
                <数据区2 Ref="29" type="DataBand" isKey="true">
                  <Brush>Transparent</Brush>
                  <BusinessObjectGuid isNull="true" />
                  <ClientRectangle>0,0.4,13.4,0.8</ClientRectangle>
                  <Components isList="true" count="4">
                    <文本7 Ref="30" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>3.4,0,3.4,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <Margins>5,0,0,0</Margins>
                      <Name>文本7</Name>
                      <Page isRef="10" />
                      <Parent isRef="29" />
                      <Text>{root_data_list_dataList.VDSum}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本7>
                    <文本8 Ref="31" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>6.8,0,3.4,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <Margins>5,0,0,0</Margins>
                      <Name>文本8</Name>
                      <Page isRef="10" />
                      <Parent isRef="29" />
                      <Text>{root_data_list_dataList.ODSum}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本8>
                    <文本9 Ref="32" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>10.2,0,3.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <Margins>5,0,0,0</Margins>
                      <Name>文本9</Name>
                      <Page isRef="10" />
                      <Parent isRef="29" />
                      <Text>{root_data_list_dataList.sum}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本9>
                    <文本6 Ref="33" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0,3.4,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <Margins>5,0,0,0</Margins>
                      <Name>文本6</Name>
                      <Page isRef="10" />
                      <Parent isRef="29" />
                      <Text>{root_data_list_dataList.rtName}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本6>
                  </Components>
                  <Conditions isList="true" count="0" />
                  <DataRelationName>root_data_list_dataList</DataRelationName>
                  <DataSourceName>root_data_list_dataList</DataSourceName>
                  <Expressions isList="true" count="0" />
                  <Filters isList="true" count="0" />
                  <MasterComponent isRef="27" />
                  <Name>数据区2</Name>
                  <Page isRef="10" />
                  <Parent isRef="28" />
                  <Sort isList="true" count="0" />
                </数据区2>
                <页脚3 Ref="34" type="FooterBand" isKey="true">
                  <Brush>Transparent</Brush>
                  <ClientRectangle>0,2,13.4,0.8</ClientRectangle>
                  <Components isList="true" count="4">
                    <文本11 Ref="35" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>3.4,0,3.4,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <Margins>5,0,0,0</Margins>
                      <Name>文本11</Name>
                      <Page isRef="10" />
                      <Parent isRef="34" />
                      <Text>{Sum(root_data_list_dataList.VDSum)}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Totals</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本11>
                    <文本12 Ref="36" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0,3.4,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本12</Name>
                      <Page isRef="10" />
                      <Parent isRef="34" />
                      <Text>小计</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本12>
                    <文本13 Ref="37" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>6.8,0,3.4,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <Margins>5,0,0,0</Margins>
                      <Name>文本13</Name>
                      <Page isRef="10" />
                      <Parent isRef="34" />
                      <Text>{Sum(root_data_list_dataList.ODSum)}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Totals</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本13>
                    <文本14 Ref="38" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>10.2,0,3.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <Margins>5,0,0,0</Margins>
                      <Name>文本14</Name>
                      <Page isRef="10" />
                      <Parent isRef="34" />
                      <Text>{Sum(root_data_list_dataList.sum)}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Totals</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本14>
                  </Components>
                  <Conditions isList="true" count="0" />
                  <Expressions isList="true" count="0" />
                  <Name>页脚3</Name>
                  <Page isRef="10" />
                  <Parent isRef="28" />
                </页脚3>
              </Components>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Name>面板1</Name>
              <Page isRef="10" />
              <Parent isRef="27" />
            </面板1>
            <文本10 Ref="39" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.2,0,3.4,1.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,0,0,0</Margins>
              <Name>文本10</Name>
              <Page isRef="10" />
              <Parent isRef="27" />
              <Text>{root_data_list.cleanerName}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本10>
            <Text47 Ref="40" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2.2,1.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>Text47</Name>
              <Page isRef="10" />
              <Parent isRef="27" />
              <Text>{Line}</Text>
              <TextBrush>Black</TextBrush>
              <Type>SystemVariables</Type>
              <VertAlignment>Center</VertAlignment>
            </Text47>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>root_data_list</DataSourceName>
          <Expressions isList="true" count="0" />
          <Filters isList="true" count="0" />
          <Name>数据区1</Name>
          <Page isRef="10" />
          <Parent isRef="10" />
          <Sort isList="true" count="0" />
        </数据区1>
        <页脚2 Ref="41" type="FooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,10,19,0.8</ClientRectangle>
          <Components isList="true" count="4">
            <文本15 Ref="42" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,9,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Margins>5,0,0,0</Margins>
              <Name>文本15</Name>
              <Page isRef="10" />
              <Parent isRef="41" />
              <Text>总计</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本15>
            <文本16 Ref="43" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9,0,3.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>文本16</Name>
              <Page isRef="10" />
              <Parent isRef="41" />
              <Text>{root_data.VDTotalSum}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本16>
            <文本17 Ref="44" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>12.4,0,3.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>文本17</Name>
              <Page isRef="10" />
              <Parent isRef="41" />
              <Text>{root_data.ODTotalSum}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本17>
            <文本18 Ref="45" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.8,0,3.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>文本18</Name>
              <Page isRef="10" />
              <Parent isRef="41" />
              <Text>{Convert.ToInt32(root_data.VDTotalSum) + Convert.ToInt32(root_data.ODTotalSum)}
</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本18>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>页脚2</Name>
          <Page isRef="10" />
          <Parent isRef="10" />
        </页脚2>
      </Components>
      <Conditions isList="true" count="0" />
      <Expressions isList="true" count="0" />
      <Guid>b05e360ccba84dc885ee9655c7053be8</Guid>
      <Margins>1,1,1,1</Margins>
      <Name>Page1</Name>
      <PageHeight>29.7</PageHeight>
      <PageWidth>21</PageWidth>
      <Report isRef="0" />
    </Page1>
  </Pages>
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>报表</ReportAlias>
  <ReportAuthor>xiao lin</ReportAuthor>
  <ReportChanged>7/1/2025 2:44:30 PM</ReportChanged>
  <ReportCreated>11/28/2024 3:55:37 PM</ReportCreated>
  <ReportFile>D:\11\2\pms-report\public\reports\roomCleanSummaryReport.mrt</ReportFile>
  <ReportGuid>919eb0c9baa149c392870ae0b50c524a</ReportGuid>
  <ReportName>报表</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2024.3.5.0</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class 报表 : Stimulsoft.Report.StiReport
    {
        public 报表()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
		#endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>