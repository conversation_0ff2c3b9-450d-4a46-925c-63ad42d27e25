{"type": "module", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "scripts": {"dev": "vite", "build": "vite build", "build:test": "node --max_old_space_size=1024000 ./node_modules/vite/bin/vite.js build --mode test", "serve:test": "http-server ./dist-test -o", "svgo": "svgo -f src/assets/icons", "new": "plop", "generate:icons": "esno ./scripts/generate.icons.ts", "lint": "npm-run-all -s lint:tsc lint:eslint lint:stylelint", "lint:tsc": "vue-tsc", "lint:eslint": "eslint . --cache --fix", "lint:stylelint": "stylelint \"src/**/*.{css,scss,vue}\" --cache --fix", "postinstall": "simple-git-hooks", "preinstall": "npx only-allow pnpm"}, "dependencies": {"@antv/g2plot": "^2.4.32", "@bytemd/plugin-gfm": "^1.21.0", "@bytemd/vue-next": "^1.21.0", "@headlessui/vue": "^1.7.22", "@imengyu/vue3-context-menu": "^1.4.2", "@tinymce/tinymce-vue": "^6.0.1", "@vueuse/components": "^11.0.3", "@vueuse/core": "^11.0.3", "@vueuse/integrations": "^11.0.3", "animate.css": "^4.1.1", "axios": "^1.7.7", "bytemd": "^1.21.0", "dayjs": "^1.11.13", "defu": "^6.1.4", "disable-devtool": "^0.3.7", "echarts": "^5.5.1", "element-plus": "^2.8.1", "eruda": "^3.2.3", "floating-vue": "5.2.2", "hotkeys-js": "^3.13.7", "lodash-es": "^4.17.21", "medium-zoom": "^1.1.0", "mitt": "^3.0.1", "mockjs": "^1.1.0", "overlayscrollbars": "^2.10.0", "overlayscrollbars-vue": "^0.5.9", "pinia": "^2.2.2", "pinyin-pro": "^3.24.2", "print-js": "^1.6.0", "qrcode": "^1.5.4", "qs": "^6.13.0", "sortablejs": "^1.15.2", "swiper": "^11.1.11", "tinymce": "^7.3.0", "v-wave": "^2.0.0", "vconsole": "^3.15.1", "vue": "^3.4.38", "vue-esign": "^1.1.4", "vue-i18n": "^9.14.0", "vue-m-message": "^4.0.2", "vue-router": "^4.4.3", "vue3-count-to": "^1.1.2"}, "devDependencies": {"@antfu/eslint-config": "2.24.1", "@iconify/json": "^2.2.243", "@iconify/vue": "^4.1.2", "@intlify/unplugin-vue-i18n": "^4.0.0", "@stylistic/stylelint-config": "^2.0.0", "@types/lodash-es": "^4.17.12", "@types/mockjs": "^1.0.10", "@types/qrcode": "^1.5.5", "@types/qs": "^6.9.15", "@types/sortablejs": "^1.15.8", "@unocss/eslint-plugin": "^0.62.3", "@vitejs/plugin-vue": "^5.1.3", "@vitejs/plugin-vue-jsx": "^4.0.1", "archiver": "^7.0.1", "autoprefixer": "^10.4.20", "boxen": "^8.0.1", "eslint": "^9.9.1", "esno": "^4.7.0", "fs-extra": "^11.2.0", "http-server": "^14.1.1", "inquirer": "^10.1.8", "lint-staged": "^15.2.9", "npm-run-all": "^4.1.5", "picocolors": "^1.0.1", "plop": "^4.0.1", "postcss": "^8.4.42", "postcss-html": "^1.7.0", "postcss-nested": "^6.2.0", "sass": "^1.77.8", "stylelint": "^16.9.0", "stylelint-config-recess-order": "^5.1.0", "stylelint-config-standard-scss": "^13.1.0", "stylelint-config-standard-vue": "^1.0.0", "stylelint-scss": "^6.5.1", "svgo": "^3.3.2", "typescript": "^5.5.4", "unocss": "^0.62.3", "unocss-preset-scrollbar": "^0.3.1", "unplugin-auto-import": "^0.18.2", "unplugin-turbo-console": "^1.10.1", "unplugin-vue-components": "^0.27.4", "vite": "^5.4.2", "vite-plugin-banner": "^0.7.1", "vite-plugin-compression2": "^1.2.0", "vite-plugin-fake-server": "^2.1.1", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-devtools": "^7.5.2", "vue-tsc": "^2.1.4"}}