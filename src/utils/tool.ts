// 获取本周一的日期
export function getMonday() {
  const today = new Date();
  const day = today.getDay(); // 获取今天是星期几（0-6，0表示周日）
  const diff = today.getDate() - day + (day === 0 ? -6 : 1); // 计算本周一的日期
  const monday = new Date(today.setDate(diff));
  return monday.toLocaleDateString(); // 返回本周一的日期（字符串格式）
}
// 获取本月第一天的日期
export function getFirstDayOfMonth() {
  const date = new Date()
  return new Date(date.getFullYear(), date.getMonth(), 1);
}
// 获取本季第一天的日期
export function getFirstDayOfQuarter(date = new Date()) {
  let month = date.getMonth();
  let quarterStartMonth = Math.floor(month / 3) * 3;
  return new Date(date.getFullYear(), quarterStartMonth, 1);
}
// 获取本年第一天的日期
export function getFirstDayOfYear(date = new Date()) {
  return new Date(date.getFullYear(), 0, 1);
}
