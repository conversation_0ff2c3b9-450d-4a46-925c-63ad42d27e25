<i18n>
{
  "en": {
    "search": {
      "today": "Today",
      "thisWeek": "This Week",
      "thisMonth": "This Month",
      "thisQuarter": "This Quarter",
      "thisYear": "This Year",
      "store": "Store",
      "operator": "Operator",
      "search": "Search",
      "selectDate": "Select Date",
      "selectStore": "Please select a store",
      "selectAccount": "Please select AR Account",
      "selectOperator": "Please select",
      "inType": "Stay Type",
      "selectInType": "Please select Stay Type",
      "startDate": "Start Date",
      "endDate": "End Date",
      "to": "to",
      "payTime": "Checkout Time",
      "bizDate": "bizDate"
    }
  },
  "zh": {
    "search": {
      "today": "今天",
      "thisWeek": "本周",
      "thisMonth": "本月",
      "thisQuarter": "本季",
      "thisYear": "本年",
      "store": "门店",
      "operator": "操作员",
      "search": "查询",
      "selectDate": "选择日期",
      "selectStore": "请选择门店",
      "selectAccount": "请选择AR账户",
      "selectOperator": "请选择",
      "inType": "入住类型",
      "selectInType": "请选择入住类型",
      "startDate": "开始日期",
      "endDate": "结束日期",
      "to": "至",
      "operateTime": "操作时间",
      "bizDate": "营业日"
    }
  },
  "km": {
    "search": {
      "today": "ថ្ងៃនេះ",
      "thisWeek": "សប្តាហ៍នេះ",
      "thisMonth": "ខែនេះ",
      "thisQuarter": "ត្រីមាសនេះ",
      "thisYear": "ឆ្នាំនេះ",
      "store": "ហាង",
      "operator": "ប្រតិបត្តិករ",
      "search": "ស្វែងរក",
      "selectDate": "ជ្រើសរើសកាលបរិច្ឆេទ",
      "selectStore": "សូមជ្រើសរើសហាង",
      "selectAccount": "សូមជ្រើសរើសគណនី AR",
      "selectOperator": "សូមជ្រើសរើស",
      "inType": "ប្រភេទស្នាក់នៅ",
      "selectInType": "សូមជ្រើសរើសប្រភេទស្នាក់នៅ",
      "startDate": "កាលបរិច្ឆេទចាប់ផ្តើម",
      "endDate": "កាលបរិច្ឆេទបញ្ចប់",
      "to": "ដល់",
      "operateTime": "ពេលវេលាប្រតិបត្តិការ",
      "bizDate": "ថ្ងៃធ្វើការ"
    }
  }
}
</i18n>

<script setup lang="ts">
import { dayjs } from 'element-plus'
import { useI18n } from 'vue-i18n'
import {
  SettleDetailReportApi,
  getShiftTimeList,
  hotelCheckInDetailReportApi,
  listSellerApi,
  simpleListApi,
  simpleUserListApi,
} from '@/api/modules/pms/shiftTimeApi.ts'
import useUserStore from '@/store/modules/user.ts'
import { getFirstDayOfMonth, getFirstDayOfQuarter, getFirstDayOfYear, getMonday } from '@/utils/tool'
import { ReportUtils } from '@/utils/report-utils.ts'

const userStore = useUserStore()
const { t } = useI18n()

const data = ref({
  searchFold: false,
  // 表格是否自适应高度
  tableAutoHeight: true,
  bizDate: [dayjs().subtract(30, 'day').toDate(), dayjs().subtract(0, 'day').toDate()] as any, // 起始时间为30天前，结束时间为前一天
  search: {
    gcode: userStore.gcode,
    hcode: userStore.hcode,
    arSetCode: '',
    timeType: '1', // 默认选择结账时间
    operator: '',
    inType: '',
    operateType: '',
    shiftNo: '',
    seller: '',
    keyWords: '',
  },
})
const simpleList = ref<{ hcode: string, hname: string }[]>([])
const operators = ref<{ username: string, nickname: string }[]>([])
const shifts = ref<{ shiftCode: string, shiftName: string }[]>([])
const sellers = ref<{ username: string, nickname: string }[]>([])

const inTypeList = ref([
  { inType: 'hour_room', inTypeName: '钟点房' },
  { inType: 'all_day', inTypeName: '全天房' },
  { inType: 'long_stay', inTypeName: '长包' },
  { inType: 'self_use', inTypeName: '自用' },
  { inType: 'free', inTypeName: '免费' },
  { inType: 'travel_group', inTypeName: '旅行团' },
  { inType: 'meeting_group', inTypeName: '会议团' },
])

const operateTypeList = ref([
  { operateType: 'first_stay', operateTypeName: '首住' },
  { operateType: 'extend_stay', operateTypeName: '续住' },
  { operateType: 'auto_extend_by_night_audit', operateTypeName: '夜审自动续住' },
])

const timeList = [
  { code: '1', label: t('search.bizDate') },
  { code: '2', label: t('search.operateTime') },
]

const shortcuts = [
  {
    text: t('search.today'),
    value: () => {
      const end = new Date()
      const start = new Date()
      return [start, end]
    },
  },
  {
    text: t('search.thisWeek'),
    value: () => {
      const end = new Date()
      const start = getMonday()
      return [start, end]
    },
  },
  {
    text: t('search.thisMonth'),
    value: () => {
      const end = new Date()
      const start = getFirstDayOfMonth()
      return [start, end]
    },
  },
  {
    text: t('search.thisQuarter'),
    value: () => {
      const end = new Date()
      const start = getFirstDayOfQuarter()
      return [start, end]
    },
  },
  {
    text: t('search.thisYear'),
    value: () => {
      const end = new Date()
      const start = getFirstDayOfYear()
      return [start, end]
    },
  },
]
const loading = ref(false)
function onSearch() {
  loading.value = true
  const timeData = {
    startDate: dayjs(data.value.bizDate[0]).format(data.value.search.timeType === '1' ? 'YYYY-MM-DD' : 'YYYY-MM-DD HH:mm:ss'),
    endDate: dayjs(data.value.bizDate[1]).format(data.value.search.timeType === '1' ? 'YYYY-MM-DD' : 'YYYY-MM-DD HH:mm:ss'),
  }
  hotelCheckInDetailReportApi({
    gcode: userStore.gcode,
    hcode: data.value.search.hcode,
    creator: data.value.search.operator,
    checkinType: data.value.search.inType,
    operateType: data.value.search.operateType,
    timeType: data.value.search.timeType,
    shiftNo: data.value.search.shiftNo,
    seller: data.value.search.seller,
    keyWords: data.value.search.keyWords,
    ...timeData,
  }).then((res) => {
    nextTick(() => {
      setJson(res)
      loading.value = false
    })
  }).catch(() => {
    loading.value = false
  })
}

async function setJson(json: any) {
  const viewer = ReportUtils.getViewer('hotelCheckInStiViewer')
  const report = ReportUtils.getReportObj('reports/HotelCheckInReport.mrt', json)
  ReportUtils.loadData(viewer, report, 'hotelCheckInReport')
}
async function getShiftTimeData() {
  await getShiftTimeList({ gcode: userStore.gcode, hcode: userStore.hcode, state: '1' }).then((res) => {
    shifts.value = res.data
  })
}

async function getSellerData() {
  await listSellerApi({ gcode: userStore.gcode, hcode: userStore.hcode }).then((res) => {
    sellers.value = res.data
  })
}

function onChangeTime() { }

async function getsimpleList() {
  const params = {
    gcode: userStore.gcode,
    hcode: userStore.hcode,
  }
  await simpleListApi(params).then((res) => {
    simpleList.value = res.data
  })
}
async function getOperatorList() {
  const params = {
    gcode: userStore.gcode,
    hcode: userStore.hcode,
  }
  await simpleUserListApi(params).then((res) => {
    operators.value = res.data
    // data.value.search.operator = res.data && res.data.length > 0 ? res.data[0].username : ''
  })
}

onMounted(async () => {
  await getsimpleList()
  await getOperatorList()
  await getShiftTimeData()
  await getSellerData()
  onSearch()
})

// -----------最多只能选择一个月，且只能选昨天之前的-----------------
const startVal = ref(null)
/** 选择第一个时间的当月天数 */
const daysInMonth = ref(0)
function calendarChange(e) {
  if (e[0] && !e[1]) {
    startVal.value = e[0]
    daysInMonth.value = dayjs(e[0]).daysInMonth()
  }
}
/** 选择第一个时间 */
function visibleChange(e) {
  if (e) {
    startVal.value = null
  }
}
/** 禁止选择一个上下自然月之外时间 */
function disabledDate(time) {
  const flag_yestodayAfter = dayjs(time).isAfter(dayjs().subtract(0, 'day'), 'day')
  if (!startVal.value) { return flag_yestodayAfter }
  const range = [dayjs(startVal.value).subtract(daysInMonth.value, 'day'), dayjs(startVal.value).add(daysInMonth.value, 'day')]
  const cur = dayjs(time)
  return cur < range[0] || cur > range[1] || flag_yestodayAfter
}
// -----------最多只能选择一个月，且只能选昨天之前的-----------------
</script>

<template>
  <div :class="{ 'absolute-container': data.tableAutoHeight }">
    <page-main>
      <search-bar :fold="data.searchFold" :show-toggle="false">
        <el-form :model="data.search" size="default" label-width="80px" inline-message inline class="search-form">
          <el-form-item :label="t('search.store')" style="width: 300px;">
            <el-select v-model="data.search.hcode" :placeholder="t('search.selectStore')">
              <el-option v-for="item in simpleList" :key="item.hcode" :label="item.hname" :value="item.hcode" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select
              v-model="data.search.timeType" style="display: inline-block;width: 100px;"
              @change="onChangeTime"
            >
              <el-option v-for="item in timeList" :key="item.code" :label="item.label" :value="item.code" />
            </el-select>
            <el-date-picker
              v-if="data.search.timeType === '1'" v-model="data.bizDate" type="daterange"
              :range-separator="t('search.to')" :start-placeholder="t('search.startDate')" :end-placeholder="t('search.endDate')" :shortcuts="shortcuts"
              :clearable="false"
              :disabled-date="disabledDate"
              @calendar-change="calendarChange"
              @visible-change="visibleChange"
            />
            <el-date-picker
              v-else v-model="data.bizDate" type="datetimerange" :range-separator="t('search.to')"
              :start-placeholder="t('search.startDate')" :end-placeholder="t('search.endDate')" :shortcuts="shortcuts" :clearable="false"
            />
          </el-form-item>
          <el-form-item :label="t('search.operator')" style="width: 260px;">
            <el-select v-model="data.search.operator" :placeholder="t('search.selectOperator')" style="width: 150px;" clearable>
              <el-option v-for="item in operators" :key="item.username" :label="item.nickname" :value="item.username" />
            </el-select>
          </el-form-item>
          <el-form-item :label="t('search.inType')" label-width="140px" style="width: 310px;">
            <el-select v-model="data.search.inType" :placeholder="t('search.selectInType')" style="width: 150px;" clearable>
              <el-option v-for="item in inTypeList" :key="item.inType" :label="item.inTypeName" :value="item.inType" />
            </el-select>
          </el-form-item>
          <el-form-item label="操作类型" label-width="140px" style="width: 310px;">
            <el-select v-model="data.search.operateType" placeholder="请选择操作类型" style="width: 150px;" clearable>
              <el-option v-for="item in operateTypeList" :key="item.operateType" :label="item.operateTypeName" :value="item.operateType" />
            </el-select>
          </el-form-item>
          <el-form-item label="查询" style="width: 300px;">
            <el-input v-model="data.search.keyWords" placeholder="请输入姓名/房号查询" clearable />
          </el-form-item>
          <el-form-item label="销售员" style="width: 260px;">
            <el-select v-model="data.search.seller" placeholder="请选择销售员" style="width: 320px;" clearable>
              <el-option v-for="item in sellers" :key="item.username" :label="item.nickname" :value="item.username" />
            </el-select>
          </el-form-item>
          <el-form-item label="班次" style="width: 260px;">
            <el-select v-model="data.search.shiftNo" placeholder="请选择班次" style="width: 320px;" clearable>
              <el-option v-for="item in shifts" :key="item.shiftCode" :label="item.shiftName" :value="item.shiftCode" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSearch">
              {{ t('search.search') }}
            </el-button>
          </el-form-item>
        </el-form>
      </search-bar>
      <div id="hotelCheckInReport" style="width: 1900px; height: 100%; overflow: auto; white-space: nowrap;">
        <el-skeleton :loading="loading" animated :rows="30" />
      </div>
    </page-main>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-radio) {
  margin-right: 14px;
}
</style>
