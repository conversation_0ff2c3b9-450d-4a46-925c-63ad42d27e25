import path from 'node:path'
import fs from 'node:fs'

function getFolder(path) {
  const components = []
  const files = fs.readdirSync(path)
  files.forEach((item) => {
    const stat = fs.lstatSync(`${path}/${item}`)
    if (stat.isDirectory() === true) {
      components.push(`${path}/${item}`)
    }
  })
  return components
}

export default {
  description: '创建标准模块 Mock',
  prompts: [
    {
      type: 'list',
      name: 'path',
      message: '请选择模块目录',
      choices: getFolder('src/views/windows'),
    },
  ],
  actions: (data) => {
    const moduleName = path.relative('src/views/windows', data.path)
    const actions = []
    actions.push({
      type: 'add',
      path: 'src/mock/{{kebabCase moduleName}}.ts',
      templateFile: 'plop-templates/mock/mock.hbs',
      data: {
        moduleName,
      },
    })
    return actions
  },
}
