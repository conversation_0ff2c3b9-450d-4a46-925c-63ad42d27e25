<i18n>
{
  "zh": {
    "search": {
      "store": "门店",
      "selectStore": "请选择门店",
      "bizDate": "营业日",
      "selectBizDate": "选择日期",
      "guestSource": "客源",
      "selectGuestSource": "请选择客源",
      "memberLevel": "会员等级",
      "selectLevel": "请选择级别",
      "agentCompany": "中介公司",
      "selectAgentCompany": "请选择中介公司",
      "protocolCompany": "协议公司",
      "selectProtocolCompany": "请选择协议公司",
      "roomType": "房型",
      "rNo": "房号",
      "roomNo":"请输入房号",
      "selectRoomType": "请选择房型",
      "orderSource": "订单来源",
      "selectOrderSource": "请选择订单来源",
      "checkinType": "入住类型",
      "selectCheckinType": "请选择入住类型",
      "statChannel": "统计渠道",
      "selectStatChannel": "请选择统计渠道",
      "selectOperator": "请选择",
      "query": "查询",
      "today": "今天",
      "thisWeek": "本周",
      "thisMonth": "本月",
      "operationTime": "操作时间",
      "startDate": "开始日期",
      "endDate": "结束日期",
      "to": "至"
    }
  },
  "en": {
    "search": {
      "store": "Store",
      "selectStore": "Select Store",
      "bizDate": "Biz Date",
      "selectBizDate": "Select Date",
      "guestSource": "Source",
      "selectGuestSource": "Select Guest Source",
      "memberLevel": "Level",
      "selectLevel": "Select Member Level",
      "agentCompany": "Agent Company",
      "selectAgentCompany": "Select Agent Company",
      "protocolCompany": "Protocol Company",
      "selectProtocolCompany": "Select Protocol Company",
      "roomType": "Type",
      "rNo": "Room No",
      "roomNo": "Input Room No",
      "selectRoomType": "Select Room Type",
      "orderSource": "Order",
      "selectOrderSource": "Select Order Source",
      "checkinType": "Check-in",
      "selectCheckinType": "Select Check-in Type",
      "statChannel": "Channel",
      "selectStatChannel": "Select Stat Channel",
      "selectOperator": "Select",
      "query": "Search",
      "today": "Today",
      "thisWeek": "This Week",
      "thisMonth": "This Month",
      "operationTime": "Operation Time",
      "startDate": "Start Date",
      "endDate": "End Date",
      "to": "to"
    }
  },
  "km": {
    "search": {
      "store": "ហាង",
      "selectStore": "សូមជ្រើសរើសហាង",
      "bizDate": "កាលបរិច្ឆេទអាជីវកម្ម",
      "selectBizDate": "ជ្រើសរើសកាលបរិច្ឆេទ",
      "guestSource": "ប្រភពភ្ញៀវ",
      "selectGuestSource": "សូមជ្រើសរើសប្រភពភ្ញៀវ",
      "memberLevel": "កម្រិតសមាជិក",
      "selectLevel": "សូមជ្រើសរើសកម្រិត",
      "agentCompany": "ក្រុមហ៊ុនអ្នកចាត់ការ",
      "selectAgentCompany": "សូមជ្រើសរើសក្រុមហ៊ុនអ្នកចាត់ការ",
      "protocolCompany": "ក្រុមហ៊ុនព្រមាន",
      "selectProtocolCompany": "សូមជ្រើសរើសក្រុមហ៊ុនព្រមាន",
      "roomType": "ប្រភេទបន្ទប់",
      "rNo": "លេខបន្ទប់",
      "roomNo": "បញ្ចូលលេខបន្ទប់",
      "selectRoomType": "សូមជ្រើសរើសប្រភេទបន្ទប់",
      "orderSource": "ប្រភពការបញ្ជាទិញ",
      "selectOrderSource": "សូមជ្រើសរើសប្រភពការបញ្ជាទិញ",
      "checkinType": "ប្រភេទចូលស្នាក់នៅ",
      "selectCheckinType": "សូមជ្រើសរើសប្រភេទចូលស្នាក់នៅ",
      "statChannel": "ឆានែលស្ថិតិ",
      "selectStatChannel": "សូមជ្រើសរើសឆានែលស្ថិតិ",
      "selectOperator": "សូមជ្រើសរើស",
      "query": "ស្វែងរក",
      "today": "ថ្ងៃនេះ",
      "thisWeek": "សប្តាហ៍នេះ",
      "thisMonth": "ខែនេះ",
      "operationTime": "ពេលវេលាប្រតិបត្តិការ",
      "startDate": "កាលបរិច្ឆេទចាប់ផ្តើម",
      "endDate": "កាលបរិច្ឆេទបញ្ចប់",
      "to": "ទៅ"
    }
  }
}
</i18n>

<script setup lang="ts">
import { dayjs } from 'element-plus'
import { useI18n } from 'vue-i18n'
import {
  getDictDataBatch,
  listChannelApi,
  listSimpleApi,
  rateDailyReportApi,
  roomTypeSimpleListApi,
  simpleListApi,
} from '@/api/modules/pms/shiftTimeApi.ts'
import useUserStore from '@/store/modules/user.ts'
import { getFirstDayOfMonth, getMonday } from '@/utils/tool'
import { ReportUtils } from '@/utils/report-utils.ts'

const userStore = useUserStore()
const { t } = useI18n()
const route = useRoute()

const data = ref({
  searchFold: false,
  // 表格是否自适应高度
  tableAutoHeight: true,
  labelState: '1',
  bizDate: [dayjs().subtract(1, 'day').toDate(), dayjs().subtract(1, 'day').toDate()] as any, // 起始时间和结束时间都为前一天
  search: {
    gcode: userStore.gcode,
    hcode: userStore.hcode,
    rNo: '',
    guestSrcType: '',
    mtCode: '',
    guestCode: '',
    rtCode: '',
    checkinType: '',
    orderSrc: undefined,
    statChannel: '',
  },
})

const shortcuts = [
  {
    text: t('search.today'),
    value: () => {
      const end = new Date()
      const start = new Date()
      return [start, end]
    },
  },
  {
    text: t('search.thisWeek'),
    value: () => {
      const end = new Date()
      const start = getMonday()
      return [start, end]
    },
  },
  {
    text: t('search.thisMonth'),
    value: () => {
      const end = new Date()
      const start = getFirstDayOfMonth()
      return [start, end]
    },
  },
]

const timeList = [
  { code: '1', label: t('search.bizDate') },
  { code: '2', label: t('search.operationTime') },
]
/** 订单来源列表 */
const orderSources = ref<{ code: string, label: string }[]>([])
/** 房型列表 */
const rts = ref<{ rtCode: string, rtName: string }[]>([])
const simpleList = ref<{ hcode: string, hname: string }[]>([])
const srcTypeList = ref<{ code: string, label: string }[]>([])
/** 中介列表 */
const agents = ref<{ paCode: string, paName: string, channel: string }[]>([])
/** 协议单位列表 */
const protocols = ref<{ paCode: string, paName: string }[]>([])
/** 入住类型列表 */
const checkinTypeList = ref<{ code: string, label: string }[]>([])
/** 统计渠道 */
const statChannelList = ref<{ channelCode: string, channelName: string }[]>([])

// 监听门店变化，重新查询相关数据
watch(() => data.value.search.hcode, (newHcode) => {
  if (newHcode) {
    // 清空已选数据
    data.value.search.rtCode = ''
    data.value.search.guestSrcType = ''
    data.value.search.guestCode = ''
    data.value.search.orderSrc = undefined
    data.value.search.checkinType = ''
    data.value.search.statChannel = ''

    // 重新加载数据
    getRts()
    getAgents()
    getProtocols()
    getStatChannelList()
  }
})
const loading = ref(false)
function onSearch() {
  loading.value = true
  rateDailyReportApi({
    ...data.value.search,
    startDate: dayjs(data.value.bizDate[0]).format('YYYY-MM-DD'),
    endDate: dayjs(data.value.bizDate[1]).format('YYYY-MM-DD'),
  }).then((res) => {
    nextTick(() => {
      setJson(res)
      loading.value = false
    })
  }).catch(() => {
    loading.value = false
  })
}
function onChange() {
  data.value.search.guestCode = ''
}

async function setJson(json: any) {
  const viewer = ReportUtils.getViewer('RoomRateStiViewer')
  const report = ReportUtils.getReportObj('reports/rateDailyReport.mrt', json)

  ReportUtils.loadData(viewer, report, 'StoreDailyReviewRoomRateReport')
}

const dictTypes = ['order_source', 'guest_src_type', 'checkin_type']
async function getConstants() {
  await getDictDataBatch(dictTypes).then((res: any) => {
    orderSources.value = res.data.filter((item: any) => item.dictType === 'order_source')
    srcTypeList.value = res.data.filter((item: any) => item.dictType === 'guest_src_type')
    checkinTypeList.value = res.data.filter((item: any) => item.dictType === 'checkin_type')
  })
}

function getRts() {
  const params = {
    gcode: userStore.gcode,
    hcode: data.value.search.hcode,
    isVirtual: '0',
    isEnable: '1',
  }
  roomTypeSimpleListApi(params).then((res: any) => {
    if (res.code === 0) {
      rts.value = res.data
    }
  })
}
async function getsimpleList() {
  const params = {
    gcode: userStore.gcode,
    hcode: data.value.search.hcode,
  }
  await simpleListApi(params).then((res) => {
    simpleList.value = res.data
  })
}
async function getAgents() {
  await listSimpleApi({
    gcode: userStore.gcode,
    hcode: data.value.search.hcode,
    paType: '1',
    isEnable: '1',
  }).then((res: any) => {
    agents.value = res.data
  })
}
async function getProtocols() {
  await listSimpleApi({
    gcode: userStore.gcode,
    hcode: data.value.search.hcode,
    paType: '0',
    isEnable: '1',
  }).then((res: any) => {
    protocols.value = res.data
  })
}
async function getStatChannelList() {
  await listChannelApi({
    gcode: userStore.gcode,
    hcode: data.value.search.hcode,
    isEnable: '1',
  }).then((res: any) => {
    statChannelList.value = res.data
  })
}

async function getQuery() {
  const query = route.query
  if (!query || JSON.stringify(query) === '{}') {
    return
  }
  if (!query.name || query.name !== 'StoreDailyReviewRoomRate') {
    return
  }
  if (query && query.startDate && query.endDate) {
    data.value.bizDate = [query.startDate, query.endDate]
  }
}

onMounted(async () => {
  await getQuery()
  await getConstants()
  await getRts()
  await getsimpleList()
  await getAgents()
  await getProtocols()
  await getStatChannelList()
  onSearch()
})
// -----------最多只能选择一个月，且只能选昨天之前的-----------------
const startVal = ref(null)
/** 选择第一个时间的当月天数 */
const daysInMonth = ref(0)
function calendarChange(e) {
  if (e[0] && !e[1]) {
    startVal.value = e[0]
    daysInMonth.value = dayjs(e[0]).daysInMonth()
  }
}
/** 选择第一个时间 */
function visibleChange(e) {
  if (e) {
    startVal.value = null
  }
}
/** 禁止选择一个上下自然月之外时间 */
function disabledDate(time) {
  const flag_yestodayAfter = dayjs(time).isAfter(dayjs().subtract(1, 'day'), 'day')
  if (!startVal.value) {
    return flag_yestodayAfter
  }
  const range = [dayjs(startVal.value).subtract(daysInMonth.value, 'day'), dayjs(startVal.value).add(daysInMonth.value, 'day')]
  const cur = dayjs(time)
  return cur < range[0] || cur > range[1] || flag_yestodayAfter
}
// -----------最多只能选择一个月，且只能选昨天之前的-----------------
</script>

<template>
  <div :class="{ 'absolute-container': data.tableAutoHeight }">
    <page-main>
      <search-bar :fold="data.searchFold" :show-toggle="false">
        <el-form :model="data.search" size="default" label-width="80px" inline-message inline class="search-form">
          <el-form-item :label="t('search.store')" style="width: 260px;">
            <el-select v-model="data.search.hcode" :placeholder="t('search.selectStore')">
              <el-option v-for="item in simpleList" :key="item.hcode" :label="item.hname" :value="item.hcode" />
            </el-select>
          </el-form-item>
          <el-form-item :label="t('search.bizDate')" style="width: 360px;">
            <el-date-picker
              v-model="data.bizDate" type="daterange" :range-separator="t('search.to')"
              :start-placeholder="t('search.startDate')"
              :end-placeholder="t('search.endDate')" :shortcuts="shortcuts" :clearable="false"
              :disabled-date="disabledDate"
              @calendar-change="calendarChange"
              @visible-change="visibleChange"
            />
          </el-form-item>
          <el-form-item :label="t('search.guestSource')" style="width: 260px;">
            <el-select v-model="data.search.guestSrcType" :placeholder="t('search.selectGuestSource')" clearable @change="onChange">
              <el-option v-for="item in srcTypeList" :key="item.code" :label="item.label" :value="item.code" />
            </el-select>
          </el-form-item>
          <el-form-item v-if="data.search.guestSrcType === 'agent'" :label="t('search.agentCompany')" style="width: 260px;">
            <el-select v-model="data.search.guestCode" :placeholder="t('search.selectAgentCompany')" clearable>
              <el-option v-for="item in agents" :key="item.paCode" :label="item.paName" :value="item.paCode" />
            </el-select>
          </el-form-item>
          <el-form-item v-if="data.search.guestSrcType === 'protocol'" :label="t('search.protocolCompany')" style="width: 260px;">
            <el-select v-model="data.search.guestCode" :placeholder="t('search.selectProtocolCompany')" clearable>
              <el-option v-for="item in protocols" :key="item.paCode" :label="item.paName" :value="item.paCode" />
            </el-select>
          </el-form-item>
          <el-form-item :label="t('search.roomType')" style="width: 260px;">
            <el-select v-model="data.search.rtCode" :placeholder="t('search.selectRoomType')" style="width: 180px;" clearable>
              <el-option v-for="item in rts" :key="item.rtCode" :label="item.rtName" :value="item.rtCode" />
            </el-select>
          </el-form-item>
          <el-form-item :label="t('search.rNo')" style="width: 260px;">
            <el-input v-model="data.search.rNo" :placeholder="t('search.roomNo')" style="width: 180px;" clearable />
          </el-form-item>
          <el-form-item :label="t('search.orderSource')" style="width: 260px;">
            <el-select v-model="data.search.orderSrc" :placeholder="t('search.selectOrderSource')" clearable>
              <el-option v-for="item in orderSources" :key="item.code" :label="item.label" :value="item.code" />
            </el-select>
          </el-form-item>
          <el-form-item :label="t('search.checkinType')" style="width: 260px;">
            <el-select v-model="data.search.checkinType" :placeholder="t('search.selectCheckinType')" clearable>
              <el-option v-for="item in checkinTypeList" :key="item.code" :label="item.label" :value="item.code" />
            </el-select>
          </el-form-item>
          <el-form-item :label="t('search.statChannel')" style="width: 260px;">
            <el-select v-model="data.search.statChannel" :placeholder="t('search.selectStatChannel')" clearable>
              <el-option v-for="item in statChannelList" :key="item.channelCode" :label="item.channelName" :value="item.channelCode" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSearch">
              {{ t('search.query') }}
            </el-button>
          </el-form-item>
        </el-form>
      </search-bar>
      <div
        id="StoreDailyReviewRoomRateReport"
      >
        <el-skeleton :loading="loading" animated :rows="30" />
      </div>
    </page-main>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-radio) {
  margin-right: 14px;
  overflow: scroll auto;
  white-space: nowrap;
}
</style>
