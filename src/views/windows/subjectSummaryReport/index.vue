<i18n>
{
  "zh": {
    "search": {
      "today": "今天",
      "thisWeek": "本周",
      "thisMonth": "本月",
      "bizDate": "营业日",
      "selectBizDate": "选择日期",
      "store": "门店",
      "selectStore": "请选择门店",
      "query": "查询"
    }
  },
  "en": {
    "search": {
      "today": "Today",
      "thisWeek": "This Week",
      "thisMonth": "This Month",
      "bizDate": "Biz Date",
      "selectBizDate": "Select Date",
      "store": "Store",
      "selectStore": "Select Store",
      "query": "Search"
    }
  },
  "km": {
    "search": {
      "today": "ថ្ងៃនេះ",
      "thisWeek": "សប្តាហ៍នេះ",
      "thisMonth": "ខែនេះ",
      "bizDate": "កាលបរិច្ឆេទអាជីវកម្ម",
      "selectBizDate": "ជ្រើសរើសកាលបរិច្ឆេទ",
      "store": "ហាង",
      "selectStore": "សូមជ្រើសរើសហាង",
      "query": "ស្វែងរក"
    }
  }
}
</i18n>

<script setup lang="ts">
import { dayjs } from 'element-plus'
import { useI18n } from 'vue-i18n'
import {
  simpleListApi,
  subjectSummaryReportApi,
} from '@/api/modules/pms/shiftTimeApi.ts'
import useUserStore from '@/store/modules/user.ts'
import { getFirstDayOfMonth, getMonday } from '@/utils/tool'
import { ReportUtils } from '@/utils/report-utils.ts'

const userStore = useUserStore()
const { t } = useI18n()
const route = useRoute()

const data = ref({
  searchFold: false,
  // 表格是否自适应高度
  tableAutoHeight: true,
  labelState: '1',
  buildingFloorCodes: [] as string[],
  search: {
    gcode: userStore.gcode,
    hcode: userStore.hcode,
    bizDate: dayjs().add(1, 'day').toDate(), // 设置为明天的日期
  },
})

const shortcuts = [
  {
    text: t('search.today'),
    value: () => {
      const start = new Date()
      return start
    },
  },
  {
    text: t('search.thisWeek'),
    value: () => {
      const start = getMonday()
      return start
    },
  },
  {
    text: t('search.thisMonth'),
    value: () => {
      const start = getFirstDayOfMonth()
      return start
    },
  },
]
/** 门店列表 */
const simpleList = ref<{ hcode: string, hname: string }[]>([])
const loading = ref(false)
function onSearch() {
  loading.value = true
  subjectSummaryReportApi({
    ...data.value.search,
    bizDate: dayjs(data.value.search.bizDate).format('YYYY-MM-DD'),
  }).then((res) => {
    nextTick(() => {
      setJson(res)
      loading.value = false
    })
  }).catch(() => {
    loading.value = false
  })
}

async function setJson(json: any) {
  const viewer = ReportUtils.getViewer('SubjectSummaryStiViewer')
  const report = ReportUtils.getReportObj('reports/subjectSummaryReport.mrt', json)

  ReportUtils.loadData(viewer, report, 'SubjectSummaryReport')
}

async function getsimpleList() {
  const params = {
    gcode: userStore.gcode,
    hcode: userStore.hcode,
  }
  await simpleListApi(params).then((res) => {
    simpleList.value = res.data
  })
}

async function getQueryParams() {
  const queryParams = route.query || {}

  if (!queryParams || JSON.stringify(queryParams) === '{}') {
    return
  }
  if (!queryParams.name || queryParams.name !== 'SubjectSummaryStiViewer') {
    return
  }

  data.value.search.hcode = String(queryParams.hcode) || userStore.hcode
  data.value.search.bizDate = queryParams.bizDate || Date.now()
}

onMounted(async () => {
  await getQueryParams()
  await getsimpleList()
  onSearch()
})
</script>

<template>
  <div :class="{ 'absolute-container': data.tableAutoHeight }">
    <page-main>
      <search-bar :fold="data.searchFold" :show-toggle="false">
        <el-form :model="data.search" size="default" label-width="80px" inline-message inline class="search-form">
          <el-form-item :label="t('search.store')" style="width: 300px;">
            <el-select v-model="data.search.hcode" :placeholder="t('search.selectStore')">
              <el-option v-for="item in simpleList" :key="item.hcode" :label="item.hname" :value="item.hcode" />
            </el-select>
          </el-form-item>
          <el-form-item :label="t('search.bizDate')" style="width: 360px;">
            <el-date-picker
              v-model="data.search.bizDate" type="date" :placeholder="t('search.selectBizDate')" style="margin-left: 10px;"
              :shortcuts="shortcuts" :clearable="false"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSearch">
              {{ t('search.query') }}
            </el-button>
          </el-form-item>
        </el-form>
      </search-bar>
      <div id="SubjectSummaryReport">
        <el-skeleton :loading="loading" animated :rows="30" />
      </div>
    </page-main>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-radio) {
  margin-right: 14px;
}
</style>
