<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <CalculationMode>Interpretation</CalculationMode>
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="1">
      <RoomChangeUpgradeReport Ref="2" type="Stimulsoft.Report.Dictionary.StiJsonDatabase" isKey="true">
        <Alias>RoomChangeUpgradeReport</Alias>
        <HeadersString />
        <Key />
        <Name>RoomChangeUpgradeReport</Name>
        <PathData>C:\Users\<USER>\Desktop\需求文档\2\换房及升级\RoomChangeUpgradeReport.json</PathData>
      </RoomChangeUpgradeReport>
    </Databases>
    <DataSources isList="true" count="3">
      <root Ref="3" type="DataTableSource" isKey="true">
        <Alias>root</Alias>
        <Columns isList="true" count="4">
          <value>code,System.Decimal</value>
          <value>data,System.String</value>
          <value>msg,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>5e070e049e62471ebc6b070723765617</Key>
        <Name>root</Name>
        <NameInSource>RoomChangeUpgradeReport.root</NameInSource>
      </root>
      <root_data Ref="4" type="DataTableSource" isKey="true">
        <Alias>root_data</Alias>
        <Columns isList="true" count="7">
          <value>hname,System.String</value>
          <value>startTime,System.String</value>
          <value>endTime,System.String</value>
          <value>lastSelectTime,System.String</value>
          <value>operator,System.String</value>
          <value>list,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>0139e322d5f2433f9fc985e067209856</Key>
        <Name>root_data</Name>
        <NameInSource>RoomChangeUpgradeReport.root_data</NameInSource>
      </root_data>
      <root_data_list Ref="5" type="DataTableSource" isKey="true">
        <Alias>root_data_list</Alias>
        <Columns isList="true" count="22">
          <value>id,System.Decimal</value>
          <value>gcode,System.String</value>
          <value>hcode,System.String</value>
          <value>name,System.String</value>
          <value>checkinTime,System.String</value>
          <value>checkoutTime,System.String</value>
          <value>oldRNo,System.String</value>
          <value>newRNo,System.String</value>
          <value>oldRoomType,System.String</value>
          <value>oldRoomTypeName,System.String</value>
          <value>newRoomType,System.String</value>
          <value>newRoomTypeName,System.String</value>
          <value>oldRoomPrice,System.Decimal</value>
          <value>newRoomPrice,System.Decimal</value>
          <value>changeRoomTime,System.String</value>
          <value>changeRoomType,System.String</value>
          <value>changeRoomTypeName,System.String</value>
          <value>changeRoomReason,System.String</value>
          <value>creator,System.String</value>
          <value>createTime,System.String</value>
          <value>updateTime,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>acabbeae54a94800bf9010bbda5bf8ec</Key>
        <Name>root_data_list</Name>
        <NameInSource>RoomChangeUpgradeReport.root_data_list</NameInSource>
      </root_data_list>
    </DataSources>
    <Relations isList="true" count="2">
      <root Ref="6" type="DataRelation" isKey="true">
        <Alias>root</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="4" />
        <Dictionary isRef="1" />
        <Name>root</Name>
        <NameInSource>root_data</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>data</value>
        </ParentColumns>
        <ParentSource isRef="3" />
      </root>
      <root_data Ref="7" type="DataRelation" isKey="true">
        <Alias>root_data</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="5" />
        <Dictionary isRef="1" />
        <Name>root_data</Name>
        <NameInSource>root_data_list</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>list</value>
        </ParentColumns>
        <ParentSource isRef="4" />
      </root_data>
    </Relations>
    <Report isRef="0" />
    <Resources isList="true" count="0" />
    <UserFunctions isList="true" count="0" />
    <Variables isList="true" count="0" />
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <Key>20ad669092e945d6b0e8bf018197e522</Key>
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="8" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="4">
        <页眉1 Ref="9" type="PageHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,43,0.8</ClientRectangle>
          <Components isList="true" count="2">
            <Text2 Ref="10" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,5.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Name>Text2</Name>
              <Page isRef="8" />
              <Parent isRef="9" />
              <Text>{root_data.hname}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text2>
            <Text6 Ref="11" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>37.4,0,5.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Name>Text6</Name>
              <Page isRef="8" />
              <Parent isRef="9" />
              <Text>第{PageNumber}页,共{TotalPageCount}页</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text6>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>页眉1</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
        </页眉1>
        <ReportTitleBand1 Ref="12" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,2,43,2</ClientRectangle>
          <Components isList="true" count="2">
            <Text1 Ref="13" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>18.9,0,5.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,12,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text1</Name>
              <Page isRef="8" />
              <Parent isRef="12" />
              <Text>换房及升级报表</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text1>
            <Text5 Ref="14" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.2,21.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Name>Text5</Name>
              <Page isRef="8" />
              <Parent isRef="12" />
              <Text>门店:{root_data.hname}   日期:{root_data.startTime} - {root_data.endTime}  最后查询时间：{root_data.lastSelectTime}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text5>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>ReportTitleBand1</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
        </ReportTitleBand1>
        <页眉2 Ref="15" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,4.8,43,0.8</ClientRectangle>
          <Components isList="true" count="14">
            <Text3 Ref="16" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text3</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>序号</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text3>
            <文本1 Ref="17" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2,0,2.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本1</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>客人姓名</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本1>
            <文本2 Ref="18" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.8,0,4.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本2</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>入住时间</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本2>
            <文本3 Ref="19" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9,0,4.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本3</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>离店时间</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本3>
            <文本4 Ref="20" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.2,0,2.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本4</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>原房号</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本4>
            <文本5 Ref="21" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.8,0,2.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本5</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>现房号</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本5>
            <文本6 Ref="22" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>18.4,0,2.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本6</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>原房型</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本6>
            <文本7 Ref="23" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>21,0,2.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本7</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>现房型</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本7>
            <文本8 Ref="24" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>23.6,0,2.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本8</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>原房价</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本8>
            <文本9 Ref="25" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>26.2,0,2.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本9</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>现房价</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本9>
            <文本10 Ref="26" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>28.8,0,4.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本10</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>换房时间</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本10>
            <文本11 Ref="27" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>33,0,2.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本11</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>换房类型</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本11>
            <文本12 Ref="28" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>35.6,0,4.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本12</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>换房原因</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本12>
            <文本13 Ref="29" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>39.8,0,3.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本13</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>操作员</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本13>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>页眉2</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
        </页眉2>
        <数据区1 Ref="30" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,6.4,43,0.8</ClientRectangle>
          <Components isList="true" count="14">
            <文本669 Ref="31" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>2,0,2.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,5,0,0</Margins>
              <Name>文本669</Name>
              <Page isRef="8" />
              <Parent isRef="30" />
              <Text>{root_data_list.name}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本669>
            <文本14 Ref="32" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,5,0,0</Margins>
              <Name>文本14</Name>
              <Page isRef="8" />
              <Parent isRef="30" />
              <Text>{Line}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>SystemVariables</Type>
              <VertAlignment>Center</VertAlignment>
            </文本14>
            <文本15 Ref="33" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>4.8,0,4.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>文本15</Name>
              <Page isRef="8" />
              <Parent isRef="30" />
              <Text>{root_data_list.checkinTime}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本15>
            <文本16 Ref="34" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>9,0,4.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>文本16</Name>
              <Page isRef="8" />
              <Parent isRef="30" />
              <Text>{root_data_list.checkoutTime}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本16>
            <文本17 Ref="35" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>13.2,0,2.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,5,0,0</Margins>
              <Name>文本17</Name>
              <Page isRef="8" />
              <Parent isRef="30" />
              <Text>{root_data_list.oldRNo}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本17>
            <文本18 Ref="36" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>15.8,0,2.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,5,0,0</Margins>
              <Name>文本18</Name>
              <Page isRef="8" />
              <Parent isRef="30" />
              <Text>{root_data_list.newRNo}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本18>
            <文本19 Ref="37" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>18.4,0,2.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,5,0,0</Margins>
              <Name>文本19</Name>
              <Page isRef="8" />
              <Parent isRef="30" />
              <Text>{root_data_list.oldRoomTypeName}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本19>
            <文本20 Ref="38" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>21,0,2.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,5,0,0</Margins>
              <Name>文本20</Name>
              <Page isRef="8" />
              <Parent isRef="30" />
              <Text>{root_data_list.newRoomTypeName}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本20>
            <文本21 Ref="39" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>23.6,0,2.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>5,5,0,0</Margins>
              <Name>文本21</Name>
              <Page isRef="8" />
              <Parent isRef="30" />
              <Text>{root_data_list.oldRoomPrice}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本21>
            <文本22 Ref="40" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>26.2,0,2.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>5,5,0,0</Margins>
              <Name>文本22</Name>
              <Page isRef="8" />
              <Parent isRef="30" />
              <Text>{root_data_list.newRoomPrice}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本22>
            <文本23 Ref="41" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>28.8,0,4.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>文本23</Name>
              <Page isRef="8" />
              <Parent isRef="30" />
              <Text>{root_data_list.changeRoomTime}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本23>
            <文本24 Ref="42" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>33,0,2.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,5,0,0</Margins>
              <Name>文本24</Name>
              <Page isRef="8" />
              <Parent isRef="30" />
              <Text>{root_data_list.changeRoomTypeName}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本24>
            <文本25 Ref="43" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>35.6,0,4.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,5,0,0</Margins>
              <Name>文本25</Name>
              <Page isRef="8" />
              <Parent isRef="30" />
              <Text>{root_data_list.changeRoomReason}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本25>
            <文本26 Ref="44" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>39.8,0,3.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,5,0,0</Margins>
              <Name>文本26</Name>
              <Page isRef="8" />
              <Parent isRef="30" />
              <Text>{root_data_list.creator}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本26>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>root_data_list</DataSourceName>
          <Expressions isList="true" count="0" />
          <Filters isList="true" count="0" />
          <Name>数据区1</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
          <Sort isList="true" count="0" />
        </数据区1>
      </Components>
      <Conditions isList="true" count="0" />
      <Expressions isList="true" count="0" />
      <Guid>4b18da2580f94f2287533271c57fdf49</Guid>
      <Margins>1,1,1,1</Margins>
      <Name>Page1</Name>
      <PageHeight>29.7</PageHeight>
      <PageWidth>45</PageWidth>
      <Report isRef="0" />
    </Page1>
  </Pages>
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>报表</ReportAlias>
  <ReportAuthor>xiao lin</ReportAuthor>
  <ReportChanged>7/30/2025 5:17:37 PM</ReportChanged>
  <ReportCreated>7/30/2025 4:43:35 PM</ReportCreated>
  <ReportFile>C:\Users\<USER>\Desktop\需求文档\2\换房及升级\roomChangeUpgradeReport.mrt</ReportFile>
  <ReportGuid>94d1bb1046fc41229bb6f71281489dde</ReportGuid>
  <ReportName>报表</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2024.3.5.0</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class 报表 : Stimulsoft.Report.StiReport
    {
        public 报表()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
		#endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>