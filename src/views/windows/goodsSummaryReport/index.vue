<i18n>
{
  "zh": {
    "search": {
      "store": "门店",
      "bizDate": "营业日",
      "query": "查询",
      "selectStore": "请选择门店",
      "startDate": "开始日期",
      "endDate": "结束日期",
      "to": "至",
      "today": "今天",
      "thisWeek": "本周",
      "thisMonth": "本月",
      "storePlaceholder": "请选择门店",
      "thingClass": "商品分类",
      "selectThingClass": "请选择商品分类",
      "goodsName": "商品名称",
      "goodsNamePlaceholder": "请输入商品名称"
    }
  },
  "en": {
    "search": {
      "store": "Store",
      "bizDate": "Biz Date",
      "query": "Search",
      "selectStore": "Select Store",
      "startDate": "Start Date",
      "endDate": "End Date",
      "to": "to",
      "today": "Today",
      "thisWeek": "This Week",
      "thisMonth": "This Month",
      "storePlaceholder": "Select Store",
      "thingClass": "Product Category",
      "selectThingClass": "Select Product Category",
      "goodsName": "Product Name",
      "goodsNamePlaceholder": "Enter Product Name"
    }
  },
  "km": {
    "search": {
      "store": "ហាង",
      "bizDate": "ថ្ងៃធ្វើការ",
      "query": "ស្វែងរក",
      "selectStore": "សូមជ្រើសរើសហាង",
      "startDate": "កាលបរិច្ឆេទចាប់ផ្តើម",
      "endDate": "កាលបរិច្ឆេទបញ្ចប់",
      "to": "ដល់",
      "today": "ថ្ងៃនេះ",
      "thisWeek": "សប្តាហ៍នេះ",
      "thisMonth": "ខែនេះ",
      "storePlaceholder": "សូមជ្រើសរើសហាង",
      "thingClass": "ប្រភេទផលិតផល",
      "selectThingClass": "សូមជ្រើសរើសប្រភេទផលិតផល",
      "goodsName": "ឈ្មោះផលិតផល",
      "goodsNamePlaceholder": "បញ្ចូលឈ្មោះផលិតផល"
    }
  }
}
</i18n>

<script setup lang="ts">
import { dayjs } from 'element-plus'
import { useI18n } from 'vue-i18n'
import {
  goodsSellSummaryReportApi,
  listThingClassApi,
  simpleListApi,
} from '@/api/modules/pms/shiftTimeApi.ts'
import useUserStore from '@/store/modules/user.ts'
import { getFirstDayOfMonth, getMonday } from '@/utils/tool'
import { ReportUtils } from '@/utils/report-utils.ts'

const userStore = useUserStore()
const { t } = useI18n()

const data = ref({
  searchFold: false,
  // 表格是否自适应高度
  tableAutoHeight: true,
  labelState: '1',
  bizDate: [dayjs().subtract(dayjs().daysInMonth(), 'day').toDate(), dayjs().toDate()] as any, // 起始时间为当月默认天数，结束时间为当天
  search: {
    gcode: userStore.gcode,
    hcode: userStore.hcode,
    goodsName: '', // 商品名称
    thingCodes: [], // 商品分类代码
  },
})

const shortcuts = [
  {
    text: t('search.today'),
    value: () => {
      const end = new Date()
      const start = new Date()
      return [start, end]
    },
  },
  {
    text: t('search.thisWeek'),
    value: () => {
      const end = new Date()
      const start = getMonday()
      return [start, end]
    },
  },
  {
    text: t('search.thisMonth'),
    value: () => {
      const end = new Date()
      const start = getFirstDayOfMonth()
      return [start, end]
    },
  },
]

const simpleList = ref<{ hcode: string, hname: string }[]>([])
const thingClassList = ref<any[]>([])

// 监听门店变化，重新查询相关数据
watch(() => data.value.search.hcode, (newHcode) => {
  if (newHcode) {
    // 切换门店时清空已选择的数据
    data.value.search.thingCodes = []
    data.value.search.goodsName = ''

    // 重新加载数据
    getThingClassList()
    // 不自动查询报表，等用户点击查询按钮时再查询
  }
})
const loading = ref(false)
function onSearch() {
  loading.value = true
  const timeData = {
    startDate: dayjs(data.value.bizDate[0]).format('YYYY-MM-DD'),
    endDate: dayjs(data.value.bizDate[1]).format('YYYY-MM-DD'),
  }
  // 处理多选的商品分类
  const searchParams = { ...data.value.search }
  if (Array.isArray(searchParams.thingCodes) && searchParams.thingCodes.length > 0) {
    searchParams.thingCodes = searchParams.thingCodes.join(',')
  }
  goodsSellSummaryReportApi({
    ...searchParams,
    ...timeData,
  }).then((res) => {
    nextTick(() => {
      setJson(res)
    })
  }).catch(() => {
    loading.value = false
  })
}

async function setJson(json: any) {
  const viewer = ReportUtils.getViewer('goodsSummaryStiViewer')
  const report = ReportUtils.getReportObj('reports/goodsSellSummaryReport.mrt', json)
  ReportUtils.loadData(viewer, report, 'goodsSummaryReport')
}

async function getsimpleList() {
  const params = {
    gcode: userStore.gcode,
    hcode: data.value.search.hcode,
  }
  await simpleListApi(params).then((res) => {
    simpleList.value = res.data
  })
}

async function getThingClassList() {
  const params = {
    gcode: userStore.gcode,
    hcode: data.value.search.hcode,
  }
  await listThingClassApi(params).then((res) => {
    thingClassList.value = convertToTree(res.data)
  })
}

function convertToTree(list: any[]) {
  const map = {}
  const roots = [] as any[]

  // 首先创建一个以id为键的映射
  list.forEach((item) => {
    map[item.id] = {
      ...item,
      label: item.name,
      value: item.id, // 修改这里，使用id作为value值而不是code
      children: [],
    }
  })

  // 然后构建树结构
  list.forEach((item) => {
    const node = map[item.id]
    if (item.parentId && map[item.parentId]) {
      // 如果有父节点，将当前节点添加到父节点的children中
      map[item.parentId].children.push(node)
    }
    else {
      // 否则作为根节点
      roots.push(node)
    }
  })

  return roots
}

onMounted(async () => {
  await getsimpleList()
  await getThingClassList()
  onSearch()
})
// -----------最多只能选择一个月，且只能选昨天之前的-----------------
const startVal = ref(null)
/** 选择第一个时间的当月天数 */
const daysInMonth = ref(0)
function calendarChange(e) {
  if (e[0] && !e[1]) {
    startVal.value = e[0]
    daysInMonth.value = dayjs(e[0]).daysInMonth()
  }
}
/** 选择第一个时间 */
function visibleChange(e) {
  if (e) {
    startVal.value = null
  }
}
/** 禁止选择一个上下自然月之外时间 */
function disabledDate(time: Date) {
  const flag_yestodayAfter = dayjs(time).isAfter(dayjs().subtract(0, 'day'), 'day')
  if (!startVal.value) {
    return flag_yestodayAfter
  }
  const range = [dayjs(startVal.value).subtract(daysInMonth.value, 'day'), dayjs(startVal.value).add(daysInMonth.value, 'day')]
  const cur = dayjs(time)
  return cur < range[0] || cur > range[1] || flag_yestodayAfter
}
// -----------最多只能选择一个月，且只能选今天(包含)之前的-----------------
</script>

<template>
  <div :class="{ 'absolute-container': data.tableAutoHeight }">
    <page-main>
      <search-bar :fold="data.searchFold" :show-toggle="false">
        <el-form :model="data.search" size="default" label-width="80px" inline-message inline class="search-form">
          <el-form-item :label="t('search.store')" style="width: 260px;">
            <el-select v-model="data.search.hcode" :placeholder="t('search.selectStore')">
              <el-option v-for="item in simpleList" :key="item.hcode" :label="item.hname" :value="item.hcode" />
            </el-select>
          </el-form-item>
          <el-form-item :label="t('search.bizDate')">
            <el-date-picker
              v-model="data.bizDate" type="daterange" style="width: 230px;"
              :range-separator="t('search.to')" :start-placeholder="t('search.startDate')"
              :end-placeholder="t('search.endDate')" :shortcuts="shortcuts"
              :clearable="false"
              :disabled-date="disabledDate"
              @calendar-change="calendarChange"
              @visible-change="visibleChange"
            />
          </el-form-item>
          <el-form-item :label="t('search.thingClass')" style="width: 260px;">
            <el-tree-select
              v-model="data.search.thingCodes"
              :data="thingClassList"
              :placeholder="t('search.selectThingClass')"
              multiple
              check-strictly
              show-checkbox
              default-expand-all
              clearable
              style="width: 100%;"
            />
          </el-form-item>
          <el-form-item :label="t('search.goodsName')" style="width: 300px;">
            <el-input v-model="data.search.goodsName" :placeholder="t('search.goodsNamePlaceholder')" clearable />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSearch">
              {{ t('search.query') }}
            </el-button>
          </el-form-item>
        </el-form>
      </search-bar>
      <div id="goodsSummaryReport">
        <el-skeleton :loading="loading" animated :rows="30" />
      </div>
    </page-main>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-radio) {
  margin-right: 14px;
}
</style>
