import { $t } from '@/locales'

const menus: Menu.recordRaw[] = [
  // {
  //   title: '会员',
  //   children: [
  //     {
  //       title: '会员本店&跨店入住明细表',
  //       windowName: 'StoreMemberCheckin',
  //     },
  //   ],
  // },
  {
    title: $t('menu.title.front_desk'),
    children: [
      {
        title: $t('menu.title.settle_detail_report'),
        windowName: 'settleDetailReport',
        windowWidth: 2800,
      },
      {
        title: $t('menu.title.hotel_checkin_detail_report'),
        windowName: 'hotelCheckInReport',
        windowWidth: 2000,
      },
      {
        title: $t('menu.title.guest_continue_stay_detail_report'),
        windowName: 'hotelCheckInContinueReport',
        windowWidth: 1800,
      },
      {
        title: $t('menu.title.today_arrival_guest_report'),
        windowName: 'bookGuestReport',
        windowWidth: 1800,
      },
      {
        title: $t('menu.title.lock_log_report'),
        windowName: 'lockLogReport',
        windowWidth: 1200,
      },
      // {
      //   title: 'AR账核销明细报表',
      //   windowName: 'StoreARaccountWriteoff',
      // },
      // {
      //   title: 'AR账发生明细报表',
      //   windowName: 'StoreARaccountHappen',
      // },
    ],
  },
  {
    title: $t('menu.title.finance'),
    children: [
      {
        title: $t('menu.title.ar_account_payment_report'),
        windowName: 'StoreARaccountPayment',
      },
      {
        title: $t('menu.title.ar_account_writeoff_report'),
        windowName: 'StoreARaccountWriteoff',
        windowWidth: 1500,
      },
      {
        title: $t('menu.title.ar_account_happen_report'),
        windowName: 'StoreARaccountHappen',
        windowWidth: 1800,
      },
      {
        title: $t('menu.title.transaction_detail_report'),
        windowName: 'TransactionReport',
      },
      {
        title: $t('menu.title.hotel_business_subject_summary_monthly_report'),
        windowName: 'subjectSummaryReport',
      },
    ],
  },
  {
    title: $t('menu.title.manager'),
    children: [
      {
        title: $t('menu.title.manager_daily_report_fixed'),
        windowName: 'StoreManagerReport',
      },
      {
        title: $t('menu.title.manager_comprehensive_monthly_report'),
        windowName: 'StoreManagerMonthReport',
        windowWidth: 2100,
      },
      {
        title: $t('menu.title.hotel_comprehensive_statistics_monthly_report_fixed'),
        windowName: 'StoreManagerMonthStatisticsReport',
        windowWidth: 3200,
      },
      {
        title: $t('menu.title.manager_daily_report'),
        windowName: 'StoreManagerDailyReport',
        windowWidth: 1600,
      },
      {
        title: '房类预测报表',
        windowName: 'roomClassPredictionReport',
      },
      /* {
        title: '经营日报表',
        windowName: 'businessDailyReport',
        windowWidth: 1600,
      }, */
      /* {
        title: "客房营收统计报表(固化)",
        windowName: 'roomStat',
      }, */
    ],
  },
  {
    title: $t('menu.title.cashier'),
    children: [
      {
        title: $t('menu.title.consumption_detail_report'),
        windowName: 'StoreCashierConsumption',
      },
      {
        title: $t('menu.title.payment_detail_report'),
        windowName: 'StoreCashierPayment',
      },
       {
        title: '消费(结账)收回明细报表',
        windowName: 'consumeRecoveryDetailReport',
      },
      {
        title: '结账(结账)收回明细报表',
        windowName: 'paymentRecoveryDetailReport',
      },
    ],
  },
  {
    title: $t('menu.title.guest_room'),
    children: [
      {
        title: $t('menu.title.night_audit_room_status_report_fixed'),
        windowName: 'StoreGuestRoomNightReview',
        windowWidth: 1200,
      },
    ],
  },
  {
    title: $t('menu.title.daily_audit'),
    children: [
      {
        title: $t('menu.title.room_rate_daily_report_fixed'),
        windowName: 'StoreDailyReviewRoomRate',
        windowWidth: 2000,
      },
      {
        title: $t('menu.title.charge_back_detail_report'),
        windowName: 'RedAndAdjustReport',

      },
      {
        title: $t('menu.title.zero_negative_room_rate_report_fixed'),
        windowName: 'zeroRoomRateReport',
        windowWidth: 1500,

      },
      {
        title: $t('menu.title.transfer_account_report'),
        windowName: 'transferAccount',
        windowWidth: 1400,
      },
      {
        title: $t('menu.title.split_account_report'),
        windowName: 'splitAccount',
        windowWidth: 1400,
      },
      {
        title: $t('menu.title.manual_room_price_change_detail_report'),
        windowName: 'changePrice',
        windowWidth: 1900,
      },
      {
        title: '换房及升级报表',
        windowName: 'roomChangeUpgradeReport',
        windowWidth: 1900,
      },
    ],
  },
  {
    title: $t('menu.title.member'),
    children: [
      {
        title: $t('menu.title.member_card_sales_report'),
        windowName: 'memberStoreCardReport',
        windowWidth: 1500,
      },
      {
        title: $t('menu.title.member_recharge_detail_report'),
        windowName: 'memberRechargeDetail',
        windowWidth: 1400,
      },
      {
        title: $t('menu.title.member_payment_detail_report'),
        windowName: 'memberPayDetailReport',
        windowWidth: 1400,
      },
    ],
  },
  {
    title: $t('menu.title.room_clean'),
    children: [
      {
        title: $t('menu.title.room_clean_detail_report'),
        windowName: 'roomCleanDetailReport',
      },
      {
        title: $t('menu.title.room_clean_summary_report'),
        windowName: 'RoomCleanSummaryReport',
        windowWidth: 1000,
      },
    ],
  },
  {
    title: $t('menu.title.goods'),
    children: [
      {
        title: $t('menu.title.goods_sales_detail_report'),
        windowName: 'goodsReport',
        windowWidth: 1400,
      },
      {
        title: $t('menu.title.goods_sales_summary_report'),
        windowName: 'goodsSummaryReport',
        windowWidth: 1000,
      },
    ],
  },
  {
    title: $t('menu.title.guest'),
    children: [
      {
        title: $t('menu.title.guest_account_detail_report_fixed'),
        windowName: 'StoreDailyReviewGuestAccount',
      },
      {
        title: $t('menu.title.inhouse_guest_balance_report'),
        windowName: 'inhouseGuestBalanceReport',
        windowWidth: 1500,
      },
      {
        title: $t('menu.title.hotel_guest_detail_report'),
        windowName: 'hotelGuestDetailReport',
        windowWidth: 1700,
      },
    ],
  },
  {
    title: $t('menu.title.breakfast'),
    children: [
      {
        title: $t('menu.title.breakfast_preparation_report'),
        windowName: 'breakfastReport',
        windowWidth: 1000,
      },
    ],
  },
  {
    title: $t('menu.title.invoice'),
    children: [
      {
        title: $t('menu.title.invoice_detail_report'),
        windowName: 'invoiceDetailReport',
        windowWidth: 2600,
      },
    ],
  },
  {
    title: $t('menu.title.night_audit'),
    children: [
      {
        title: $t('menu.title.night_price_prequalify_report'),
        windowName: 'nightPricePrequalifyReport',
        windowWidth: 1400,
      },
    ],
  },
]

export default menus
