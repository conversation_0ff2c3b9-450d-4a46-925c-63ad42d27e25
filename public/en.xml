<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<Localization language="English" description="English" cultureName="en">
  <A_WebViewer>
    <AbbreviatedDayFriday>Fr</AbbreviatedDayFriday>
    <AbbreviatedDayMonday>Mo</AbbreviatedDayMonday>
    <AbbreviatedDaySaturday>Sa</AbbreviatedDaySaturday>
    <AbbreviatedDaySunday>Su</AbbreviatedDaySunday>
    <AbbreviatedDayThursday>Th</AbbreviatedDayThursday>
    <AbbreviatedDayTuesday>Tu</AbbreviatedDayTuesday>
    <AbbreviatedDayWednesday>We</AbbreviatedDayWednesday>
    <Attachment>Attachment:</Attachment>
    <ButtonNext>Next</ButtonNext>
    <ButtonPrev>Previous</ButtonPrev>
    <ButtonSend>Send</ButtonSend>
    <CategoryAlreadyExists>The category with that name already exists!</CategoryAlreadyExists>
    <DayFriday>Friday</DayFriday>
    <DayMonday>Monday</DayMonday>
    <Days>Days</Days>
    <DaySaturday>Saturday</DaySaturday>
    <DaySunday>Sunday</DaySunday>
    <DayThursday>Thursday</DayThursday>
    <DayTuesday>Tuesday</DayTuesday>
    <DayWednesday>Wednesday</DayWednesday>
    <Email>Email:</Email>
    <EmailOptions>Email Options</EmailOptions>
    <FirstPage>First Page</FirstPage>
    <Hours>Hours</Hours>
    <LabelFrom>From:</LabelFrom>
    <LabelSelectExportFormat>Select the format to which you want to save the report:</LabelSelectExportFormat>
    <LabelTo>To:</LabelTo>
    <LastPage>Last Page</LastPage>
    <Loading>Loading...</Loading>
    <Message>Message:</Message>
    <Minutes>Minutes</Minutes>
    <MonthApril>April</MonthApril>
    <MonthAugust>August</MonthAugust>
    <MonthDecember>December</MonthDecember>
    <MonthFebruary>February</MonthFebruary>
    <MonthJanuary>January</MonthJanuary>
    <MonthJuly>July</MonthJuly>
    <MonthJune>June</MonthJune>
    <MonthMarch>March</MonthMarch>
    <MonthMay>May</MonthMay>
    <MonthNovember>November</MonthNovember>
    <MonthOctober>October</MonthOctober>
    <MonthSeptember>September</MonthSeptember>
    <NextPage>Next Page</NextPage>
    <OnePage>One Page</OnePage>
    <Page>Page</Page>
    <PageOf>of</PageOf>
    <PreviousPage>Previous Page</PreviousPage>
    <PrintContinue>Click Print to continue</PrintContinue>
    <PrintReport>Print</PrintReport>
    <PrintToPdf>PDF</PrintToPdf>
    <PrintToXps>Microsoft XPS</PrintToXps>
    <PrintWithoutPreview>Print</PrintWithoutPreview>
    <PrintWithPreview>Preview</PrintWithPreview>
    <SaveReport>Save</SaveReport>
    <Subject>Subject:</Subject>
    <TabItemContacts>Contacts</TabItemContacts>
    <TextComputer>Computer</TextComputer>
    <TextItemsRoot>Root</TextItemsRoot>
    <TodayDate>Today</TodayDate>
    <WholeReport>Whole Report</WholeReport>
  </A_WebViewer>
  <Adapters>
    <AdapterBusinessObjects>Data from Business Objects</AdapterBusinessObjects>
    <AdapterConnection>Data from {0}</AdapterConnection>
    <AdapterCrossTabDataSource>Data from Cross-Tab</AdapterCrossTabDataSource>
    <AdapterCsvFiles>Data from CSV Files</AdapterCsvFiles>
    <AdapterDataTables>Data from DataSet, DataTables</AdapterDataTables>
    <AdapterDataViews>Data from DataViews</AdapterDataViews>
    <AdapterDB2Connection>Data from IBM DB2 Connection</AdapterDB2Connection>
    <AdapterDBaseFiles>Data from dBase Files</AdapterDBaseFiles>
    <AdapterFirebirdConnection>Data from Firebird Connection</AdapterFirebirdConnection>
    <AdapterInformixConnection>Data from Informix Connection</AdapterInformixConnection>
    <AdapterMySQLConnection>Data from MySQL Connection</AdapterMySQLConnection>
    <AdapterOdbcConnection>Data from ODBC Connection</AdapterOdbcConnection>
    <AdapterOleDbConnection>Data from OLE DB Connection</AdapterOleDbConnection>
    <AdapterOracleConnection>Data from Oracle Connection</AdapterOracleConnection>
    <AdapterOracleODPConnection>Data from Oracle ODP.NET Connection</AdapterOracleODPConnection>
    <AdapterPostgreSQLConnection>Data from PostgreSQL Connection</AdapterPostgreSQLConnection>
    <AdapterSqlCeConnection>Data from SQL CE Connection</AdapterSqlCeConnection>
    <AdapterSqlConnection>Data from SQL Connection</AdapterSqlConnection>
    <AdapterSQLiteConnection>Data from SQLite Connection</AdapterSQLiteConnection>
    <AdapterTeradataConnection>Data from Teradata Connection</AdapterTeradataConnection>
    <AdapterUniDirectConnection>Data from Uni Direct Connection</AdapterUniDirectConnection>
    <AdapterUserSources>Data from User Sources</AdapterUserSources>
    <AdapterVirtualSource>Data from other Data Source</AdapterVirtualSource>
    <AdapterVistaDBConnection>Data from VistaDB Connection</AdapterVistaDBConnection>
  </Adapters>
  <BarCode>
    <Post>Post</Post>
    <TwoDimensional>Two-Dimensional</TwoDimensional>
  </BarCode>
  <Buttons>
    <Add>Add</Add>
    <AddAllColumns>Add All Columns</AddAllColumns>
    <Attach>Attach</Attach>
    <Build>Build...</Build>
    <Buttons>Buttons</Buttons>
    <Cancel>&amp;Cancel</Cancel>
    <Check>Check</Check>
    <Close>Close</Close>
    <Delete>Delete</Delete>
    <Design>Design</Design>
    <Down>Down</Down>
    <Duplicate>Duplicate</Duplicate>
    <Export>Export</Export>
    <ForceDelete>Force Delete</ForceDelete>
    <Help>Help</Help>
    <Install>Install</Install>
    <InvertAll>Invert All</InvertAll>
    <LessOptions>Less Options</LessOptions>
    <LoadDataSet>Load DataSet (*.data)</LoadDataSet>
    <More>More</More>
    <MoreApps>More Apps</MoreApps>
    <MoreOptions>More Options</MoreOptions>
    <MoveLeft>Move Left</MoveLeft>
    <MoveRight>Move Right</MoveRight>
    <MoveToResource>Move to Resource</MoveToResource>
    <No>&amp;No</No>
    <Ok>&amp;OK</Ok>
    <Open>Open</Open>
    <OpenContainingFolder>Open Containing Folder</OpenContainingFolder>
    <OpenInSafeMode>Open in Safe Mode</OpenInSafeMode>
    <Print>Print</Print>
    <Publish>Publish</Publish>
    <QuickPrint>Quick Print</QuickPrint>
    <Remove>Remove</Remove>
    <RemoveAll>Remove All</RemoveAll>
    <Rename>Rename</Rename>
    <RestoreDefaults>Restore Defaults</RestoreDefaults>
    <Reverse>Reverse</Reverse>
    <Save>Save</Save>
    <SaveCopy>Save a Copy</SaveCopy>
    <SetAll>Set All</SetAll>
    <ShowLess>Show Less</ShowLess>
    <ShowMore>Show More</ShowMore>
    <ShowSpecific>Show '{0}'</ShowSpecific>
    <Sign>Sign</Sign>
    <Submit>Submit</Submit>
    <SwitchTo>Switch to {0}</SwitchTo>
    <Test>Test</Test>
    <TryToReconnect>Try to Reconnect</TryToReconnect>
    <Up>Up</Up>
    <Upgrade>Upgrade</Upgrade>
    <UpgradeNow>Upgrade Now</UpgradeNow>
    <Upload>Upload</Upload>
    <Waiting>Waiting</Waiting>
    <Yes>&amp;Yes</Yes>
  </Buttons>
  <Chart>
    <AddCondition>&amp;Add Condition</AddCondition>
    <AddConstantLine>Add Constant Line</AddConstantLine>
    <AddFilter>&amp;Add Filter</AddFilter>
    <AddSeries>&amp;Add Series</AddSeries>
    <AddStrip>Add Strip</AddStrip>
    <AddTrendLine>Add Trend Line</AddTrendLine>
    <Area>Area</Area>
    <Axes>Axes</Axes>
    <AxisReverse>Reverse</AxisReverse>
    <AxisX>Axis X</AxisX>
    <AxisY>Axis Y</AxisY>
    <BoxAndWhisker>Box and Whisker</BoxAndWhisker>
    <Bubble>Bubble</Bubble>
    <Candlestick>Candlestick</Candlestick>
    <ChartConditionsCollectionForm>Conditions</ChartConditionsCollectionForm>
    <ChartEditorForm>Chart Editor</ChartEditorForm>
    <ChartFiltersCollectionForm>Filters</ChartFiltersCollectionForm>
    <ChartType>Chart Type</ChartType>
    <CheckBoxAutoRotation>Auto Rotation</CheckBoxAutoRotation>
    <ClusteredBar>Clustered Bar</ClusteredBar>
    <ClusteredColumn>Clustered Column</ClusteredColumn>
    <Common>Common</Common>
    <ConstantLine>Constant Line</ConstantLine>
    <ConstantLinesEditorForm>Constant Lines Editor</ConstantLinesEditorForm>
    <DataColumns>Data Columns</DataColumns>
    <Doughnut>Doughnut</Doughnut>
    <Financial>Financial</Financial>
    <FullStackedArea>Full-Stacked Area</FullStackedArea>
    <FullStackedBar>Full-Stacked Bar</FullStackedBar>
    <FullStackedColumn>Full-Stacked Column</FullStackedColumn>
    <FullStackedLine>Full-Stacked Line</FullStackedLine>
    <FullStackedSpline>Full-Stacked Spline</FullStackedSpline>
    <FullStackedSplineArea>Full-Stacked Spline Area</FullStackedSplineArea>
    <Funnel>Funnel</Funnel>
    <FunnelWeightedSlices>Funnel Weighted Slices</FunnelWeightedSlices>
    <Gantt>Gantt</Gantt>
    <GridInterlaced>Interlaced</GridInterlaced>
    <GridLines>Grid Lines</GridLines>
    <Heatmap>Heatmap</Heatmap>
    <Histogram>Histogram</Histogram>
    <LabelAlignment>Alignment:</LabelAlignment>
    <LabelAlignmentHorizontal>Horizontal:</LabelAlignmentHorizontal>
    <LabelAlignmentVertical>Vertical:</LabelAlignmentVertical>
    <LabelAngle>Angle:</LabelAngle>
    <LabelArgumentDataColumn>Argument Data Column:</LabelArgumentDataColumn>
    <LabelAutoRotation>Auto Rotation:</LabelAutoRotation>
    <LabelCloseValueDataColumn>Close Value Data Column:</LabelCloseValueDataColumn>
    <LabelEndValueDataColumn>End Value Data Column:</LabelEndValueDataColumn>
    <LabelHighValueDataColumn>High Value Data Column:</LabelHighValueDataColumn>
    <LabelHorizontal>Horizontal:</LabelHorizontal>
    <LabelLowValueDataColumn>Low Value Data Column:</LabelLowValueDataColumn>
    <LabelMinorCount>Minor Count:</LabelMinorCount>
    <LabelOpenValueDataColumn>Open Value Data Column:</LabelOpenValueDataColumn>
    <Labels>Labels</Labels>
    <LabelsCenter>Center</LabelsCenter>
    <LabelSeriesName>Series Name:</LabelSeriesName>
    <LabelsInside>Inside</LabelsInside>
    <LabelsInsideBase>Inside Base</LabelsInsideBase>
    <LabelsInsideEnd>Inside End</LabelsInsideEnd>
    <LabelsNone>None</LabelsNone>
    <LabelsOutside>Outside</LabelsOutside>
    <LabelsOutsideBase>Outside Base</LabelsOutsideBase>
    <LabelsOutsideEnd>Outside End</LabelsOutsideEnd>
    <LabelsOverlay>Overlay</LabelsOverlay>
    <LabelsStyleCategory>Category</LabelsStyleCategory>
    <LabelsStyleCategoryPercentOfTotal>Category - Percent Of Total</LabelsStyleCategoryPercentOfTotal>
    <LabelsStyleCategoryValue>Category - Value</LabelsStyleCategoryValue>
    <LabelsStylePercentOfTotal>Percent Of Total</LabelsStylePercentOfTotal>
    <LabelsStyleValue>Value</LabelsStyleValue>
    <LabelsTwoColumns>Two Columns</LabelsTwoColumns>
    <LabelTextAfter>Text After:</LabelTextAfter>
    <LabelTextBefore>Text Before:</LabelTextBefore>
    <LabelTitleAlignment>Alignment:</LabelTitleAlignment>
    <LabelValueDataColumn>Value Data Column:</LabelValueDataColumn>
    <LabelValueType>Value Type:</LabelValueType>
    <LabelVertical>Vertical:</LabelVertical>
    <LabelVisible>Visible:</LabelVisible>
    <Legend>Legend</Legend>
    <LegendSpacing>Spacing</LegendSpacing>
    <Line>Line</Line>
    <ListOfValues>List of Values</ListOfValues>
    <ListOfXValues>List of X Values</ListOfXValues>
    <ListOfYValues>List of Y Values</ListOfYValues>
    <Marker>Marker</Marker>
    <MoveConstantLineDown>Move Constant Line Down</MoveConstantLineDown>
    <MoveConstantLineUp>Move Constant Line Up</MoveConstantLineUp>
    <MoveSeriesDown>Move Series Down</MoveSeriesDown>
    <MoveSeriesUp>Move Series Up</MoveSeriesUp>
    <MoveStripDown>Move Strip Down</MoveStripDown>
    <MoveStripUp>Move Strip Up</MoveStripUp>
    <NoConditions>No Conditions</NoConditions>
    <NoFilters>No Filters</NoFilters>
    <Pareto>Pareto</Pareto>
    <Pictorial>Pictorial</Pictorial>
    <PictorialStacked>Pictorial Stacked</PictorialStacked>
    <Pie>Pie</Pie>
    <Radar>Radar</Radar>
    <RadarArea>Radar Area</RadarArea>
    <RadarColumn>Radar Column</RadarColumn>
    <RadarLine>Radar Line</RadarLine>
    <RadarPoint>Radar Point</RadarPoint>
    <Range>Range</Range>
    <RangeBar>Range Bar</RangeBar>
    <RemoveCondition>&amp;Remove Condition</RemoveCondition>
    <RemoveConstantLine>Remove Constant Line</RemoveConstantLine>
    <RemoveFilter>&amp;Remove Filter</RemoveFilter>
    <RemoveSeries>&amp;Remove Series</RemoveSeries>
    <RemoveStrip>Remove Strip</RemoveStrip>
    <Ribbon>Ribbon</Ribbon>
    <RunChartWizard>Run Chart &amp;Wizard</RunChartWizard>
    <Scatter>Scatter</Scatter>
    <ScatterLine>Scatter Line</ScatterLine>
    <ScatterSpline>Scatter Spline</ScatterSpline>
    <Series>Series</Series>
    <SeriesColorsCollectionForm>Series Colors</SeriesColorsCollectionForm>
    <SeriesEditorForm>Series Editor</SeriesEditorForm>
    <Serieses>Series</Serieses>
    <SparklinesArea>Area</SparklinesArea>
    <SparklinesColumn>Column</SparklinesColumn>
    <SparklinesLine>Line</SparklinesLine>
    <SparklinesWinLoss>Win / Loss</SparklinesWinLoss>
    <Spline>Spline</Spline>
    <SplineArea>Spline Area</SplineArea>
    <SplineRange>Spline Range</SplineRange>
    <StackedArea>Stacked Area</StackedArea>
    <StackedBar>Stacked Bar</StackedBar>
    <StackedColumn>Stacked Column</StackedColumn>
    <StackedLine>Stacked Line</StackedLine>
    <StackedSpline>Stacked Spline</StackedSpline>
    <StackedSplineArea>Stacked Spline Area</StackedSplineArea>
    <SteppedArea>Stepped Area</SteppedArea>
    <SteppedLine>Stepped Line</SteppedLine>
    <SteppedRange>Stepped Range</SteppedRange>
    <Stock>Stock</Stock>
    <Strip>Strip</Strip>
    <StripsEditorForm>Strips Editor Form</StripsEditorForm>
    <Style>Style</Style>
    <Sunburst>Sunburst</Sunburst>
    <Treemap>Treemap</Treemap>
    <TrendLinesEditorForm>Trend Lines Editor</TrendLinesEditorForm>
    <Waterfall>Waterfall</Waterfall>
  </Chart>
  <CharterMapEditor>
    <Characters>Characters</Characters>
  </CharterMapEditor>
  <ChartRibbon>
    <Axes>Axes</Axes>
    <AxesArrowStyle>Arrow Style</AxesArrowStyle>
    <AxesArrowStyleLines>Lines</AxesArrowStyleLines>
    <AxesArrowStyleNone>None</AxesArrowStyleNone>
    <AxesArrowStyleTriangle>Triangle</AxesArrowStyleTriangle>
    <AxesLabel>Label Placement</AxesLabel>
    <AxesLabelsNone>None</AxesLabelsNone>
    <AxesLabelsOneLine>One Line</AxesLabelsOneLine>
    <AxesLabelsTwoLines>Two Lines</AxesLabelsTwoLines>
    <AxesReverseHorizontal>Reverse Horizontal</AxesReverseHorizontal>
    <AxesReverseVertical>Reverse Vertical</AxesReverseVertical>
    <AxesTicks>Ticks</AxesTicks>
    <AxesTicksMajor>Major</AxesTicksMajor>
    <AxesTicksMinor>Minor</AxesTicksMinor>
    <AxesTicksNone>None</AxesTicksNone>
    <AxesVisible>Visible</AxesVisible>
    <AxesXAxis>X Axis</AxesXAxis>
    <AxesXTopAxis>X Top Axis</AxesXTopAxis>
    <AxesYAxis>Y Axis</AxesYAxis>
    <AxesYRightAxis>Y Right Axis</AxesYRightAxis>
    <CenterLabels>Center</CenterLabels>
    <ChangeType>Change Type</ChangeType>
    <GridLines>Grid Lines</GridLines>
    <GridLinesHorizontal>Grid Lines Horizontal</GridLinesHorizontal>
    <GridLinesVertical>Grid Lines Vertical</GridLinesVertical>
    <HorAlCenter>&lt;b&gt;Overlay Legend at Center&lt;/b&gt;&lt;br&gt;Show Legend at center of the chart&lt;/br&gt;&lt;br&gt;without resizing&lt;/br&gt;</HorAlCenter>
    <HorAlLeft>&lt;b&gt;Overlay Legend at Left&lt;/b&gt;&lt;br&gt;Show Legend at left of the chart&lt;/br&gt;&lt;br&gt;without resizing&lt;/br&gt;</HorAlLeft>
    <HorAlLeftOutside>&lt;b&gt;Show Legend at Left&lt;/b&gt;&lt;br&gt;Show Legend and align left&lt;/br&gt;</HorAlLeftOutside>
    <HorAlRight>&lt;b&gt;Overlay Legend at Right&lt;/b&gt;&lt;br&gt;Show Legend at right of the chart&lt;/br&gt;&lt;br&gt;without resizing&lt;/br&gt;</HorAlRight>
    <HorAlRightOutside>&lt;b&gt;Show Legend at Right&lt;/b&gt;&lt;br&gt;Show Legend and align right&lt;/br&gt;</HorAlRightOutside>
    <HorizontalMajor>&lt;b&gt;Major&lt;/b&gt;&lt;br&gt;Display Horizontal Gridlines for Major units&lt;/br&gt;</HorizontalMajor>
    <HorizontalMajorMinor>&lt;b&gt;Major &amp;&amp; Minor Gridlines&lt;/b&gt;&lt;br&gt;Display Horizontal Gridlines for Major and Minor units&lt;/br&gt;</HorizontalMajorMinor>
    <HorizontalMinor>&lt;b&gt;Minor&lt;/b&gt;&lt;br&gt;Display Horizontal Gridlines for Minor units&lt;/br&gt;</HorizontalMinor>
    <HorizontalNone>&lt;b&gt;None&lt;/b&gt;&lt;br&gt;Do not display Horizontal Grirdlines&lt;/br&gt;</HorizontalNone>
    <InsideBaseLabels>Inside Base</InsideBaseLabels>
    <InsideEndLabels>Inside End</InsideEndLabels>
    <Labels>Series Labels</Labels>
    <Legend>Legend</Legend>
    <LegendHorizontalAlignment>Horizontal Alignment</LegendHorizontalAlignment>
    <LegendMarker>Marker</LegendMarker>
    <LegendMarkerAlignmentLeft>Left</LegendMarkerAlignmentLeft>
    <LegendMarkerAlignmentRight>Right</LegendMarkerAlignmentRight>
    <LegendMarkerVisible>Visible</LegendMarkerVisible>
    <LegendVerticalAlignment>Vertical Alignment</LegendVerticalAlignment>
    <LegendVisible>Visible</LegendVisible>
    <NoneLabels>None</NoneLabels>
    <OutsideBaseLabels>Outside Base</OutsideBaseLabels>
    <OutsideEndLabels>Outside End</OutsideEndLabels>
    <OutsideLabels>Outside</OutsideLabels>
    <ribbonBarAxis>Axes</ribbonBarAxis>
    <ribbonBarChartStyles>Chart Styles</ribbonBarChartStyles>
    <ribbonBarChartType>Chart Type</ribbonBarChartType>
    <ribbonBarLabels>Labels</ribbonBarLabels>
    <ribbonBarLegend>Legend</ribbonBarLegend>
    <Style>Change Style</Style>
    <TwoColumnsPieLabels>Two Columns</TwoColumnsPieLabels>
    <VertAlBottom>&lt;b&gt;Overlay Legend at Bottom&lt;/b&gt;&lt;br&gt;Show Legend at bottom of the chart&lt;/br&gt;&lt;br&gt;without resizing&lt;/br&gt;</VertAlBottom>
    <VertAlBottomOutside>&lt;b&gt;Show Legend at Bottom&lt;/b&gt;&lt;br&gt;Show Legend and align bottom&lt;/br&gt;</VertAlBottomOutside>
    <VertAlCenter>&lt;b&gt;Overlay Legend at Center&lt;/b&gt;&lt;br&gt;Show Legend at center of the chart&lt;/br&gt;&lt;br&gt;without resizing&lt;/br&gt;</VertAlCenter>
    <VertAlTop>&lt;b&gt;Overlay Legend at Top&lt;/b&gt;&lt;br&gt;Show Legend at top of the chart&lt;/br&gt;&lt;br&gt;without resizing&lt;/br&gt;</VertAlTop>
    <VertAlTopOutside>&lt;b&gt;Show Legend at Top&lt;/b&gt;&lt;br&gt;Show Legend and align top&lt;/br&gt;</VertAlTopOutside>
    <VerticalMajor>&lt;b&gt;Major&lt;/b&gt;&lt;br&gt;Display Vertical Gridlines for Major units&lt;/br&gt;</VerticalMajor>
    <VerticalMajorMinor>&lt;b&gt;Major &amp;&amp; Minor Gridlines&lt;/b&gt;&lt;br&gt;Display Vertical Gridlines for Major and Minor units&lt;/br&gt;</VerticalMajorMinor>
    <VerticalMinor>&lt;b&gt;Minor&lt;/b&gt;&lt;br&gt;Display Vertical Gridlines for Minor units&lt;/br&gt;</VerticalMinor>
    <VerticalNone>&lt;b&gt;None&lt;/b&gt;&lt;br&gt;Do not display Vertical Gridlines&lt;/br&gt;</VerticalNone>
  </ChartRibbon>
  <Cloud>
    <AcceptTermsAndPrivacyPolicy>I read and accept the {0} and {1}</AcceptTermsAndPrivacyPolicy>
    <Account>Account</Account>
    <AccountSettings>Account Settings</AccountSettings>
    <AddAPlace>Add a Place</AddAPlace>
    <AreYouSureYouWantDeleteReport>Are you sure you want to delete "{0}" report from disk?</AreYouSureYouWantDeleteReport>
    <Authorize>Authorize</Authorize>
    <AuthorizeWithLicenseKey>Authorize with License Key</AuthorizeWithLicenseKey>
    <ButtonChangePassword>Change Password</ButtonChangePassword>
    <ButtonDeleteAll>Delete All</ButtonDeleteAll>
    <ButtonDesign>Design Report</ButtonDesign>
    <ButtonLater>Later</ButtonLater>
    <ButtonLogInWith>Log in with {0}</ButtonLogInWith>
    <ButtonLogout>Logout</ButtonLogout>
    <ButtonPublish>Publish</ButtonPublish>
    <ButtonPurchase>Purchase</ButtonPurchase>
    <ButtonRecover>Recover</ButtonRecover>
    <ButtonRenew>Renew</ButtonRenew>
    <ButtonResendEmail>Resend Email</ButtonResendEmail>
    <ButtonResendInvitation>Resend Invitation</ButtonResendInvitation>
    <ButtonResetPassword>Reset Password</ButtonResetPassword>
    <ButtonRun>Run</ButtonRun>
    <ButtonScheduler>Scheduler</ButtonScheduler>
    <ButtonShare>Share</ButtonShare>
    <ButtonSignUp>Sign up</ButtonSignUp>
    <ButtonSignUpWith>Sign up with {0}</ButtonSignUpWith>
    <ButtonSkip>Skip</ButtonSkip>
    <ButtonView>View</ButtonView>
    <ButtonWhereUsed>Where is it used?</ButtonWhereUsed>
    <Cancel>Cancel</Cancel>
    <CheckBoxMoveToRecycleBin>Move to Recycle Bin</CheckBoxMoveToRecycleBin>
    <CheckBoxRememberMe>Remember Me</CheckBoxRememberMe>
    <CheckForUpdate>Check for Update</CheckForUpdate>
    <Cloud>Cloud</Cloud>
    <Collection>Collection</Collection>
    <Create>Create</Create>
    <CreateError>Error while creating report!</CreateError>
    <CreateNewCollection>Create New Collection</CreateNewCollection>
    <CreateYourTeam>Create your team</CreateYourTeam>
    <CreatingReport>Creating report...</CreatingReport>
    <DashboardWindowTitleNew>New Dashboard</DashboardWindowTitleNew>
    <DeleteFile>Delete File</DeleteFile>
    <DoNotAskMe>Do not ask me again</DoNotAskMe>
    <EnterUserNameToLogin>Enter User Name (email) to log in.</EnterUserNameToLogin>
    <ExecutionError>Execution error</ExecutionError>
    <ExpiredDate>Expired Date</ExpiredDate>
    <FileStorageWindowTitleEdit>Edit File Storage</FileStorageWindowTitleEdit>
    <FileStorageWindowTitleNew>New File Storage</FileStorageWindowTitleNew>
    <FolderWindowTitleEdit>Edit Folder</FolderWindowTitleEdit>
    <FolderWindowTitleNew>New Folder</FolderWindowTitleNew>
    <ForExample>For example</ForExample>
    <GroupBoxAttachedItems>Attached Items</GroupBoxAttachedItems>
    <HyperlinkAgreeToTerms>By registering you agree to Terms.</HyperlinkAgreeToTerms>
    <HyperlinkAlreadyHaveAccount>Already have account</HyperlinkAlreadyHaveAccount>
    <HyperlinkForgotPassword>Forgot password</HyperlinkForgotPassword>
    <HyperlinkHavePassword>Have password</HyperlinkHavePassword>
    <HyperlinkRegisterAccount>Register FREE account</HyperlinkRegisterAccount>
    <InstallSamples>Install Samples</InstallSamples>
    <Invitations>Invitations</Invitations>
    <Invite>Invite</Invite>
    <LabelAddCloudFolder>Connect your Stimulsoft Cloud folder with reports and dashboards.</LabelAddCloudFolder>
    <LabelAddFolder>Add your local folder with reports and dashboards.</LabelAddFolder>
    <labelCollectionName>Collection Name:</labelCollectionName>
    <LabelCreated>Created:</LabelCreated>
    <LabelCreateFolder>Create a new category in which you can place other items.</LabelCreateFolder>
    <LabelCreateNewDashboard>Create new dashboard.</LabelCreateNewDashboard>
    <LabelCreateReportTemplate>Create report template.</LabelCreateReportTemplate>
    <LabelCurrentPassword>Current Password:</LabelCurrentPassword>
    <LabelDataFile>Data File:</LabelDataFile>
    <LabelDataUrl>Data URL:</LabelDataUrl>
    <LabelEndDate>End Date:</LabelEndDate>
    <labelFileName>File Name:</labelFileName>
    <LabelForeground>Foreground:</LabelForeground>
    <LabelFromReport>From Report</LabelFromReport>
    <LabelFromReportCode>From Report (define in code)</LabelFromReportCode>
    <LabelInviteUser>Invite new users to your team. Send invitations to their emails.</LabelInviteUser>
    <LabelLastLogin>Last Log in:</LabelLastLogin>
    <LabelLastTime>Last Time:</LabelLastTime>
    <LabelModified>Modified:</LabelModified>
    <LabelNewPassword>New Password:</LabelNewPassword>
    <LabelNextTime>Next Time:</LabelNextTime>
    <labelPassword>Password:</labelPassword>
    <LabelPermission>Permission:</LabelPermission>
    <LabelPicture>Picture:</LabelPicture>
    <LabelRenderedReport>Rendered report</LabelRenderedReport>
    <LabelResponseAsFile>Response as File</LabelResponseAsFile>
    <LabelResultType>Result Type:</LabelResultType>
    <LabelSeparateReport>Separate report (and scripts) file</LabelSeparateReport>
    <LabelShowReport>Show report:</LabelShowReport>
    <LabelStartDate>Start Date:</LabelStartDate>
    <labelUserName>User Name:</labelUserName>
    <License>License</License>
    <LicenseInformation>License Information</LicenseInformation>
    <LicenseKey>License Key</LicenseKey>
    <Login>Log in</Login>
    <MyAccount>My Account</MyAccount>
    <MyCloud>My Cloud</MyCloud>
    <MyPlan>My Plan</MyPlan>
    <MyTeam>My Team</MyTeam>
    <NofM>{0}: {1} of {2}</NofM>
    <Open>Open</Open>
    <OpenFile>Open File</OpenFile>
    <OperationCreate>Create '{0}'</OperationCreate>
    <OperationDelete>Delete '{0}' from Server</OperationDelete>
    <OperationDownload>Download from Server</OperationDownload>
    <OperationGetList>Get List of Files from Server</OperationGetList>
    <OperationLogin>Log in to Server</OperationLogin>
    <OperationRename>Rename '{0}' to '{1}'</OperationRename>
    <OperationUpload>Upload '{0}' to Server</OperationUpload>
    <page>page</page>
    <Platforms>Platforms</Platforms>
    <Port>Port</Port>
    <PrivacyPolicy>Privacy</PrivacyPolicy>
    <Products>Products</Products>
    <Proxy>Proxy</Proxy>
    <PublishMessage>In order to publish this report you should submit it to our service publish.stimulsoft.com</PublishMessage>
    <questionOpenThisFile>Are you sure want to open '{0}' item?</questionOpenThisFile>
    <questionOverrideItem>Do you really want to override '{0}' item?</questionOverrideItem>
    <questionRemoveItem>Do you really want to remove '{0}' item?</questionRemoveItem>
    <RefreshList>Refresh List</RefreshList>
    <ReportDocumentFormatNotRecognized>Format of '{0}' item is not recognized as rendered report format!</ReportDocumentFormatNotRecognized>
    <ReportTemplateFormatNotRecognized>Format of '{0}' item is not recognized as report template format!</ReportTemplateFormatNotRecognized>
    <RequestChangesWhenSavingToCloud>Request Changes when Saving to Stimulsoft Cloud</RequestChangesWhenSavingToCloud>
    <RibbonButtonAddRole>Add Role</RibbonButtonAddRole>
    <RibbonButtonAddUser>Add User</RibbonButtonAddUser>
    <RibbonButtonAddUserParameter>Add User Parameter</RibbonButtonAddUserParameter>
    <RibbonButtonAddWorkspace>Add Workspace</RibbonButtonAddWorkspace>
    <RibbonButtonFolder>Folder</RibbonButtonFolder>
    <RibbonTabUsers>Users</RibbonTabUsers>
    <Root>Root</Root>
    <RootFolder>Root Folder</RootFolder>
    <Save>Save</Save>
    <SaveAccountSettings>Save Account Settings</SaveAccountSettings>
    <SaveAsType>Save as type</SaveAsType>
    <SaveFile>Save File</SaveFile>
    <SavingToStimulsoftCloudPleaseWait>Saving the report to Stimulsoft Cloud. Please wait!</SavingToStimulsoftCloudPleaseWait>
    <SearchForOnlineTemplates>Search Stimulsoft templates online</SearchForOnlineTemplates>
    <ShareWindowTitleNew>Share</ShareWindowTitleNew>
    <ShowAllFiles>Show All Files</ShowAllFiles>
    <ShowNotificationMessages>Show Notification Messages</ShowNotificationMessages>
    <Submissions>Submissions</Submissions>
    <Subscriptions>Subscriptions</Subscriptions>
    <TabItemEmbedCode>Embed Code</TabItemEmbedCode>
    <TabItemQRCode>QR Code</TabItemQRCode>
    <TabItemShare>Link to Share</TabItemShare>
    <Team>Team</Team>
    <TermsOfUse>Terms</TermsOfUse>
    <TextActivated>Activated</TextActivated>
    <TextActivationDate>Activation Date</TextActivationDate>
    <TextDelete>Delete</TextDelete>
    <TextDeletingItems>Deleting items...</TextDeletingItems>
    <TextDescriptionChanges>Please, type a description of the changes:</TextDescriptionChanges>
    <TextFirstName>First Name</TextFirstName>
    <TextFromTo>From {0} to {1} (Total - {2})</TextFromTo>
    <TextItemsWorkspace>Workspace</TextItemsWorkspace>
    <TextLastName>Last Name</TextLastName>
    <TextModify>Modify</TextModify>
    <TextNewsLetters>News Letters</TextNewsLetters>
    <TextNoFavoriteFiles>No favorite files</TextNoFavoriteFiles>
    <TextNoFiles>No files</TextNoFiles>
    <TextNoNotifications>No Notifications</TextNoNotifications>
    <TextNoRecentFiles>No recent files</TextNoRecentFiles>
    <TextOwner>Owner</TextOwner>
    <TextProfile>Profile</TextProfile>
    <TextReports>Reports</TextReports>
    <TextRestoringItems>Restoring items...</TextRestoringItems>
    <TextRole>Role</TextRole>
    <TextRun>Run</TextRun>
    <TextUser>User</TextUser>
    <TextUserName>User Name (Email)</TextUserName>
    <TimeHoursAgoFive>Five hours ago, {0:t}</TimeHoursAgoFive>
    <TimeHoursAgoFour>Four hours ago, {0:t}</TimeHoursAgoFour>
    <TimeHoursAgoOne>One hour ago, {0:t}</TimeHoursAgoOne>
    <TimeHoursAgoThree>Three hours ago, {0:t}</TimeHoursAgoThree>
    <TimeHoursAgoTwo>Two hours ago, {0:t}</TimeHoursAgoTwo>
    <TimeMinutesAgoFive>Five minutes ago, {0:t}</TimeMinutesAgoFive>
    <TimeMinutesAgoFour>Four minutes ago, {0:t}</TimeMinutesAgoFour>
    <TimeMinutesAgoLessOne>Less than a minute ago</TimeMinutesAgoLessOne>
    <TimeMinutesAgoN>{0} minutes ago, {1:t}</TimeMinutesAgoN>
    <TimeMinutesAgoOne>One minute ago, {0:t}</TimeMinutesAgoOne>
    <TimeMinutesAgoThree>Three minutes ago, {0:t}</TimeMinutesAgoThree>
    <TimeMinutesAgoTwo>Two minutes ago, {0:t}</TimeMinutesAgoTwo>
    <TimeToday>Today, {0:t}</TimeToday>
    <TimeYesterday>Yesterday, {0:t}</TimeYesterday>
    <ToolTipAddRole>Creating a new role in the workspace.</ToolTipAddRole>
    <ToolTipAddUser>Creating a new user in the selected role.</ToolTipAddUser>
    <ToolTipAspNet>Publish the selected report to the ASP.NET application.</ToolTipAspNet>
    <ToolTipAspNetMvc>Publish the selected report to the ASP.NET MVC application.</ToolTipAspNetMvc>
    <ToolTipAttach>Attach items to the selected report.</ToolTipAttach>
    <ToolTipCreate>Create a new item.</ToolTipCreate>
    <ToolTipDelete>Delete the selected items.</ToolTipDelete>
    <ToolTipDeleted>Show deleted items</ToolTipDeleted>
    <ToolTipDownload>Download the selected item.</ToolTipDownload>
    <ToolTipEdit>Edit the selected item.</ToolTipEdit>
    <ToolTipGridMode>List view</ToolTipGridMode>
    <ToolTipInfo>View details</ToolTipInfo>
    <ToolTipJs>Publish the selected report as the HTML page</ToolTipJs>
    <ToolTipPublish>Prepare the selected report to be deployed in your application.</ToolTipPublish>
    <ToolTipRecover>Recover the selected items.</ToolTipRecover>
    <ToolTipRunWithoutPreview>Render the selected report without preview</ToolTipRunWithoutPreview>
    <ToolTipShare>Share the selected file.</ToolTipShare>
    <ToolTipSort>Sort options</ToolTipSort>
    <ToolTipThumbnailMode>Thumbnail View</ToolTipThumbnailMode>
    <ToolTipViewFile>Show the selected file.</ToolTipViewFile>
    <ToolTipViewReport>Show the selected report in the viewer.</ToolTipViewReport>
    <WeDidntFindAnything>We didn't find anything to show here.</WeDidntFindAnything>
    <WindowDescriptionDelete>Are you sure you want to delete the selected items?</WindowDescriptionDelete>
    <WindowDescriptionRecover>Selected items will be recovered from the Recycle Bin.</WindowDescriptionRecover>
    <WindowTitleDelete>Delete Items</WindowTitleDelete>
    <WindowTitleForgotPassword>Forgot Password</WindowTitleForgotPassword>
    <WindowTitleLogin>Log in</WindowTitleLogin>
    <WindowTitleRecover>Recover Items</WindowTitleRecover>
    <WindowTitleRoleEdit>Edit Role</WindowTitleRoleEdit>
    <WindowTitleRoleNew>New Role</WindowTitleRoleNew>
    <WindowTitleSignUp>Sign up</WindowTitleSignUp>
    <WindowTitleUserEdit>Edit User</WindowTitleUserEdit>
    <WindowTitleUserNew>New User</WindowTitleUserNew>
    <WindowTitleUserParameterEdit>Edit User Parameter</WindowTitleUserParameterEdit>
    <WindowTitleUserParameterNew>New User Parameter</WindowTitleUserParameterNew>
    <WindowTitleWorkspaceEdit>Edit Workspace</WindowTitleWorkspaceEdit>
    <WindowTitleWorkspaceNew>New Workspace</WindowTitleWorkspaceNew>
    <WizardBlankReportDescription>Create a blank report</WizardBlankReportDescription>
    <WizardExcelDescription>Create a report with Excel data.</WizardExcelDescription>
    <WizardJsonDescription>Create a report with JSON data.</WizardJsonDescription>
    <WizardPrivateShare>No Access</WizardPrivateShare>
    <WizardPrivateShareDescription>External access to the item is restricted.</WizardPrivateShareDescription>
    <WizardPublicShare>Public Access</WizardPublicShare>
    <WizardPublicShareDescription>External access for any unauthorized user.</WizardPublicShareDescription>
    <WizardRegisteredShare>Authorized Access</WizardRegisteredShare>
    <WizardRegisteredShareDescription>External access only for registered users from any workspace.</WizardRegisteredShareDescription>
    <WizardTeamShare>Team Access</WizardTeamShare>
    <WizardTeamShareDescription>External access for users of one workspace only.</WizardTeamShareDescription>
    <WizardXmlDescription>Create a report with XML data.</WizardXmlDescription>
    <YourPlan>Your Plan</YourPlan>
  </Cloud>
  <Components>
    <StiBarCode>Bar Code</StiBarCode>
    <StiButton>Button</StiButton>
    <StiCards>Cards</StiCards>
    <StiChart>Chart</StiChart>
    <StiCheckBox>Check Box</StiCheckBox>
    <StiChildBand>Child</StiChildBand>
    <StiClone>Clone</StiClone>
    <StiColumnFooterBand>Column Footer</StiColumnFooterBand>
    <StiColumnHeaderBand>Column Header</StiColumnHeaderBand>
    <StiComboBox>Combo Box</StiComboBox>
    <StiComponent>Component</StiComponent>
    <StiContainer>Container</StiContainer>
    <StiContourText>Contour Text</StiContourText>
    <StiCrossColumn>Cross-Column</StiCrossColumn>
    <StiCrossColumnTotal>Cross-Column Total</StiCrossColumnTotal>
    <StiCrossDataBand>Cross-Data</StiCrossDataBand>
    <StiCrossFooterBand>Cross-Footer</StiCrossFooterBand>
    <StiCrossGroupFooterBand>Cross-Group Footer</StiCrossGroupFooterBand>
    <StiCrossGroupHeaderBand>Cross-Group Header</StiCrossGroupHeaderBand>
    <StiCrossHeaderBand>Cross-Header</StiCrossHeaderBand>
    <StiCrossRow>Cross-Row</StiCrossRow>
    <StiCrossRowTotal>Cross-Row Total</StiCrossRowTotal>
    <StiCrossSummary>Cross-Summary</StiCrossSummary>
    <StiCrossSummaryHeader>Cross-Summary Header</StiCrossSummaryHeader>
    <StiCrossTab>Cross-Tab</StiCrossTab>
    <StiCrossTitle>Cross-Title</StiCrossTitle>
    <StiDashboard>Dashboard</StiDashboard>
    <StiDataBand>Data</StiDataBand>
    <StiDatePicker>Date Picker</StiDatePicker>
    <StiElectronicSignature>Electronic Signature</StiElectronicSignature>
    <StiEmptyBand>Empty Data</StiEmptyBand>
    <StiFooterBand>Footer</StiFooterBand>
    <StiGauge>Gauge</StiGauge>
    <StiGroupFooterBand>Group Footer</StiGroupFooterBand>
    <StiGroupHeaderBand>Group Header</StiGroupHeaderBand>
    <StiHeaderBand>Header</StiHeaderBand>
    <StiHierarchicalBand>Hierarchical Data</StiHierarchicalBand>
    <StiHorizontalLinePrimitive>Horizontal Line</StiHorizontalLinePrimitive>
    <StiImage>Image</StiImage>
    <StiIndicator>Indicator</StiIndicator>
    <StiListBox>List Box</StiListBox>
    <StiMap>Map</StiMap>
    <StiMathFormula>Math Formula</StiMathFormula>
    <StiMultipleSelection>Multiple Selection</StiMultipleSelection>
    <StiNumberBox>Number Box</StiNumberBox>
    <StiOnlineMap>Online Map</StiOnlineMap>
    <StiOverlayBand>Overlay</StiOverlayBand>
    <StiPage>Page</StiPage>
    <StiPageFooterBand>Page Footer</StiPageFooterBand>
    <StiPageHeaderBand>Page Header</StiPageHeaderBand>
    <StiPanel>Panel</StiPanel>
    <StiPdfDigitalSignature>PDF Digital Signature</StiPdfDigitalSignature>
    <StiPdfDigitalSignatureElement>PDF Element</StiPdfDigitalSignatureElement>
    <StiPivotColumn>Pivot Column</StiPivotColumn>
    <StiPivotRow>Pivot Row</StiPivotRow>
    <StiPivotSummary>Pivot Summary</StiPivotSummary>
    <StiPivotTable>Pivot Table</StiPivotTable>
    <StiProgress>Progress</StiProgress>
    <StiRectanglePrimitive>Rectangle</StiRectanglePrimitive>
    <StiRegionMap>Region Map</StiRegionMap>
    <StiReport>Report</StiReport>
    <StiReportSummaryBand>Report Summary</StiReportSummaryBand>
    <StiReportTitleBand>Report Title</StiReportTitleBand>
    <StiRichText>Rich Text</StiRichText>
    <StiRoundedRectanglePrimitive>Rounded Rectangle</StiRoundedRectanglePrimitive>
    <StiScreen>Screen</StiScreen>
    <StiShape>Shape</StiShape>
    <StiSignature>Signature</StiSignature>
    <StiSingleSelection>Single Selection</StiSingleSelection>
    <StiSparkline>Sparkline</StiSparkline>
    <StiSubReport>Sub-Report</StiSubReport>
    <StiSystemText>System Text</StiSystemText>
    <StiTable>Table</StiTable>
    <StiTableOfContents>Table of Contents</StiTableOfContents>
    <StiText>Text</StiText>
    <StiTextInCells>Text in Cells</StiTextInCells>
    <StiTreeView>Tree View</StiTreeView>
    <StiTreeViewBox>Tree View Box</StiTreeViewBox>
    <StiVerticalLinePrimitive>Vertical Line</StiVerticalLinePrimitive>
    <StiWebContent>Web Content</StiWebContent>
    <StiWinControl>Win Control</StiWinControl>
    <StiZipCode>ZIP Code</StiZipCode>
  </Components>
  <CustomMapWindow>
    <CheckedAll>Checked All</CheckedAll>
    <HideText>Hide Text</HideText>
    <KeysChangePosition>Keys (Left, Top, Right, Bottom) change the position of the text</KeysChangePosition>
    <KeysChangeSize>Shift + keys (Left, Top, Right, Bottom) change the size of the text area</KeysChangeSize>
    <UncheckedAll>Unchecked All</UncheckedAll>
  </CustomMapWindow>
  <Dashboard>
    <AddRange>Add Range</AddRange>
    <AfterGroupingData>Apply after grouping data</AfterGroupingData>
    <AfterSortingData>Apply after grouping and sorting data</AfterSortingData>
    <AllowUserColumnSelection>Allow User Column Selection</AllowUserColumnSelection>
    <AllowUserDrillDown>Allow User Drill-Down</AllowUserDrillDown>
    <AllowUserFiltering>Allow User Filtering</AllowUserFiltering>
    <AllowUserSorting>Allow User Sorting</AllowUserSorting>
    <BeforeTransformation>Apply before data transformation</BeforeTransformation>
    <Blank>(Blank)</Blank>
    <Blanks>(Blanks)</Blanks>
    <BooleanFilters>Boolean Filters</BooleanFilters>
    <CannotLoadDashboard>You cannot load a report with dashboards.</CannotLoadDashboard>
    <ChangeChartType>Change Chart Type</ChangeChartType>
    <ChangeMapType>Change Map Type</ChangeMapType>
    <ClearAllFormatting>Clear All Formatting</ClearAllFormatting>
    <ClearFilterFrom>Clear Filter from '{0}'</ClearFilterFrom>
    <ColorScale>Color Scale</ColorScale>
    <ColumnInteractions>Column Interactions</ColumnInteractions>
    <CustomFilter>Custom &amp;Filter...</CustomFilter>
    <DashboardsNotSupportedIntheWPFDesigner>Dashboards are not supported in the WPF Designer.</DashboardsNotSupportedIntheWPFDesigner>
    <DataBars>Data Bars</DataBars>
    <DataFilterGrouping>This operation is used for grouping different columns only.</DataFilterGrouping>
    <DataNotDefined>Data is not defined</DataNotDefined>
    <DateFilters>Date Filters</DateFilters>
    <Dimension>Dimension</Dimension>
    <Dimensions>Dimensions</Dimensions>
    <DragDropBlocks>Drag &amp; drop blocks from panel</DragDropBlocks>
    <DragDropData>Drag &amp; drop data</DragDropData>
    <DragDropDataFromDictionary>Drag &amp; drop data from Dictionary</DragDropDataFromDictionary>
    <DrillDown>Drill-Down</DrillDown>
    <DrillDownFiltered>Drill-Down Filtered</DrillDownFiltered>
    <DrillDownSelected>Drill-Down Selected</DrillDownSelected>
    <DrillUp>Drill Up</DrillUp>
    <DuplicateField>Duplicate Field</DuplicateField>
    <EditExpression>Edit Expression</EditExpression>
    <EditField>Edit Field</EditField>
    <EmptyDashboardFooter>Drag &amp; drop component from ToolBox or data from Dictionary</EmptyDashboardFooter>
    <EmptyDashboardHeader>Dashboard is empty</EmptyDashboardHeader>
    <FieldInteractions>Field Interactions</FieldInteractions>
    <FieldTypeRestrictionHint>Only '{0}' type field can be used!</FieldTypeRestrictionHint>
    <FirstLastPoints>First / Last Points</FirstLastPoints>
    <FirstRowIndex>First Row Index</FirstRowIndex>
    <FullRowSelect>Full Row Select</FullRowSelect>
    <HighLowPoints>High / Low Points</HighLowPoints>
    <ImageNotSpecified>Image not specified</ImageNotSpecified>
    <Indicator>Indicator</Indicator>
    <InitialValue>Initial Value</InitialValue>
    <LimitRows>Skip and Limit Rows</LimitRows>
    <Measure>Measure</Measure>
    <Measures>Measures</Measures>
    <Metrics>Metrics</Metrics>
    <NewDimension>New Dimension</NewDimension>
    <NewField>New Field</NewField>
    <NewMeasure>New Measure</NewMeasure>
    <NoRanges>No Ranges</NoRanges>
    <NoResult>No Result</NoResult>
    <NSelected>{0} selected</NSelected>
    <Nulls>(Nulls)</Nulls>
    <NumberFilters>Number Filters</NumberFilters>
    <ParentElement>Parent Element</ParentElement>
    <Priority>Priority</Priority>
    <RangeMode>Range Mode</RangeMode>
    <RangeType>Range Type</RangeType>
    <RemoveActions>R&amp;emove Actions</RemoveActions>
    <RemoveAllFields>Remove All Fields</RemoveAllFields>
    <RemoveField>Remove Field</RemoveField>
    <RemoveMobileSurface>Remove Mobile Surface</RemoveMobileSurface>
    <ReplaceValues>Replace Values</ReplaceValues>
    <ReportSnapshot>Report Snapshot</ReportSnapshot>
    <RowsCount>Rows Count</RowsCount>
    <RunFieldsEditor>Run Fields Editor</RunFieldsEditor>
    <RunFieldsEditorInfo>Run the Fields Editor to change interaction of each table column.</RunFieldsEditorInfo>
    <SelectAll>(Select &amp;All)</SelectAll>
    <ShowAllValue>Show (All) Value</ShowAllValue>
    <ShowAsPercentages>Show as Percentages</ShowAsPercentages>
    <ShowBlanks>Show Blanks</ShowBlanks>
    <SkipFirstRows>Skip First Rows</SkipFirstRows>
    <SortAZ>Sort A to Z</SortAZ>
    <SortLargestToSmallest>Sort Largest to Smallest</SortLargestToSmallest>
    <SortNewestToOldest>Sort Newest to Oldest</SortNewestToOldest>
    <SortOldestToNewest>Sort Oldest to Newest</SortOldestToNewest>
    <SortSmallestToLargest>Sort Smallest to Largest</SortSmallestToLargest>
    <SortZA>Sort Z to A</SortZA>
    <Sparklines>Sparklines</Sparklines>
    <StringFilters>String Filters</StringFilters>
    <TransformationHint>Click on the table header to apply sorting, filtering or change data. This transformation will be applied only to the data of this item.</TransformationHint>
    <Trend>Trend</Trend>
    <ViewEditCompilationNotice>You can't edit the report which has the 'Report.CalculationMode' property equals to 'Compilation' value. Please set that property to an 'Interpretation' value.</ViewEditCompilationNotice>
    <ViewModeDesktop>Desktop</ViewModeDesktop>
    <ViewModeMobile>Mobile</ViewModeMobile>
  </Dashboard>
  <Database>
    <Connection>Connection</Connection>
    <Database>{0} Connection</Database>
    <DatabaseDB2>IBM DB2 Connection</DatabaseDB2>
    <DatabaseFirebird>Firebird Connection</DatabaseFirebird>
    <DatabaseInformix>Informix Connection</DatabaseInformix>
    <DatabaseJson>JSON Data</DatabaseJson>
    <DatabaseMySQL>MySQL Connection</DatabaseMySQL>
    <DatabaseOdbc>ODBC Connection</DatabaseOdbc>
    <DatabaseOleDb>OLE DB Connection</DatabaseOleDb>
    <DatabaseOracle>Oracle Connection</DatabaseOracle>
    <DatabaseOracleODP>Oracle ODP.NET Connection</DatabaseOracleODP>
    <DatabasePostgreSQL>PostgreSQL Connection</DatabasePostgreSQL>
    <DatabaseSql>SQL Connection</DatabaseSql>
    <DatabaseSqlCe>SQLServerCE Connection</DatabaseSqlCe>
    <DatabaseSQLite>SQLite Connection</DatabaseSQLite>
    <DatabaseTeradata>Teradata Connection</DatabaseTeradata>
    <DatabaseUniDirect>Uni Direct Connection</DatabaseUniDirect>
    <DatabaseVistaDB>VistaDB Connection</DatabaseVistaDB>
    <DatabaseXml>XML Data</DatabaseXml>
  </Database>
  <DatePickerRanges>
    <CurrentMonth>Current Month</CurrentMonth>
    <CurrentQuarter>Current Quarter</CurrentQuarter>
    <CurrentWeek>Current Week</CurrentWeek>
    <CurrentYear>Current Year</CurrentYear>
    <DaysAgo>{0} Days Ago</DaysAgo>
    <FirstQuarter>First Quarter</FirstQuarter>
    <FourthQuarter>Fourth Quarter</FourthQuarter>
    <Index>Index</Index>
    <Last14Days>Last 14 Days</Last14Days>
    <Last30Days>Last 30 Days</Last30Days>
    <Last7Days>Last 7 Days</Last7Days>
    <MonthToDate>Month To Date</MonthToDate>
    <NextMonth>Next Month</NextMonth>
    <NextQuarter>Next Quarter</NextQuarter>
    <NextWeek>Next Week</NextWeek>
    <NextYear>Next Year</NextYear>
    <PreviousMonth>Previous Month</PreviousMonth>
    <PreviousQuarter>Previous Quarter</PreviousQuarter>
    <PreviousWeek>Previous Week</PreviousWeek>
    <PreviousYear>Previous Year</PreviousYear>
    <Quarter>Quarter</Quarter>
    <QuarterToDate>Quarter To Date</QuarterToDate>
    <SecondQuarter>Second Quarter</SecondQuarter>
    <ThirdQuarter>Third Quarter</ThirdQuarter>
    <Today>Today</Today>
    <Tomorrow>Tomorrow</Tomorrow>
    <WeekToDate>Week To Date</WeekToDate>
    <Year>Year</Year>
    <YearToDate>Year To Date</YearToDate>
    <Yesterday>Yesterday</Yesterday>
  </DatePickerRanges>
  <DesignerFx>
    <AlreadyExists>'{0}' already exists.</AlreadyExists>
    <CanNotLoadThisReportTemplate>Can't load this template.</CanNotLoadThisReportTemplate>
    <CloseDataSourceEditor>Do you want to close data source editor?</CloseDataSourceEditor>
    <CloseEditor>Do you want to close editor?</CloseEditor>
    <CompilingReport>Compiling Report</CompilingReport>
    <Connecting>Connecting to Server</Connecting>
    <ConnectionError>Connection error</ConnectionError>
    <ConnectionSuccessfull>Connection was successful</ConnectionSuccessfull>
    <Continue>Continue</Continue>
    <DecryptionError>Decryption error: Wrong password or corrupted file.</DecryptionError>
    <EmailSuccessfullySent>The Email has been successfully sent.</EmailSuccessfullySent>
    <ErrorAtSaving>Error at saving</ErrorAtSaving>
    <ErrorCode>Error at saving. Error code: {0}</ErrorCode>
    <ErrorServer>Error at saving. Server doesn't respond.</ErrorServer>
    <ExportingReport>Exporting Report</ExportingReport>
    <LoadingCode>Loading Code</LoadingCode>
    <LoadingConfiguration>Loading Configuration</LoadingConfiguration>
    <LoadingData>Loading Data</LoadingData>
    <LoadingDocument>Loading Document</LoadingDocument>
    <LoadingImages>Loading Images</LoadingImages>
    <LoadingLanguage>Loading Language</LoadingLanguage>
    <LoadingReport>Loading Report</LoadingReport>
    <PreviewAs>Preview as {0}</PreviewAs>
    <RenderingReport>Rendering Report</RenderingReport>
    <ReportSuccessfullySaved>Report is successfully saved.</ReportSuccessfullySaved>
    <RetrieveError>Retrieve columns error</RetrieveError>
    <RetrievingColumns>Retrieving Columns</RetrievingColumns>
    <Saving>Saving</Saving>
    <SavingConfiguration>Saving Configuration</SavingConfiguration>
    <SavingReport>Saving Report</SavingReport>
    <TestConnection>Test Connection</TestConnection>
    <TextNotFound>The specified text was not found. Text : {0}</TextNotFound>
  </DesignerFx>
  <Desktop>
    <Beginner>Beginner</Beginner>
    <ButtonAddCloud>Add Cloud Folder</ButtonAddCloud>
    <ButtonAddFolder>Add Folder</ButtonAddFolder>
    <ButtonCreateDashboard>Create Dashboard</ButtonCreateDashboard>
    <ButtonCreateReport>Create Report</ButtonCreateReport>
    <ChooseYourSkillLevel>Choose your skill level</ChooseYourSkillLevel>
    <Creator>Creator</Creator>
    <Developer>Developer</Developer>
    <DoYouWantToInstallReports>Do you want to install templates of reports and dashboards?</DoYouWantToInstallReports>
    <InstallSamplesDesc>Let's install our sample projects. They will help you learn the basics of working with our software.</InstallSamplesDesc>
    <PleaseAnswerWhoAreYou>Please, answer who you are?</PleaseAnswerWhoAreYou>
    <SkillLevel>Skill Level</SkillLevel>
    <WhoAreYou>Who Are you?</WhoAreYou>
    <WhoAreYouBeginnerDescription>Perfect fit if you are doing the first run of the designer.</WhoAreYouBeginnerDescription>
    <WhoAreYouCreatorDescription>Perfect fit for creating reports. The necessary functionality for design is available.</WhoAreYouCreatorDescription>
    <WhoAreYouDeveloperDescription>Perfect fit for creating applications. Complete functionality for the report design and embedding components included.</WhoAreYouDeveloperDescription>
  </Desktop>
  <Dialogs>
    <StiButtonControl>Button</StiButtonControl>
    <StiCheckBoxControl>Check Box</StiCheckBoxControl>
    <StiCheckedListBoxControl>Checked List Box</StiCheckedListBoxControl>
    <StiComboBoxControl>Combo Box</StiComboBoxControl>
    <StiDateTimePickerControl>Date Time Picker</StiDateTimePickerControl>
    <StiForm>Form</StiForm>
    <StiGridControl>Grid</StiGridControl>
    <StiGroupBoxControl>Group Box</StiGroupBoxControl>
    <StiLabelControl>Label</StiLabelControl>
    <StiListBoxControl>List Box</StiListBoxControl>
    <StiListViewControl>List View</StiListViewControl>
    <StiLookUpBoxControl>LookUp Box</StiLookUpBoxControl>
    <StiNumericUpDownControl>Numeric Up Down</StiNumericUpDownControl>
    <StiPanelControl>Panel</StiPanelControl>
    <StiPictureBoxControl>Picture Box</StiPictureBoxControl>
    <StiRadioButtonControl>Radio Button</StiRadioButtonControl>
    <StiRadioGroupControl>Radio Group</StiRadioGroupControl>
    <StiReportControl>Report Control</StiReportControl>
    <StiRichTextBoxControl>Rich Text Box</StiRichTextBoxControl>
    <StiSliderControl>Slider</StiSliderControl>
    <StiTextBoxControl>Text Box</StiTextBoxControl>
    <StiToggleControl>Toggle</StiToggleControl>
    <StiTreeViewControl>Tree View</StiTreeViewControl>
  </Dialogs>
  <Editor>
    <AllowSQLFormatting>Allow SQL Formatting</AllowSQLFormatting>
    <BetweenMetrics>Between 1 and 10 metrics can be selected.</BetweenMetrics>
    <CantFind>Cannot find the data you're searching for.</CantFind>
    <CollapseToDefinitions>C&amp;ollapse to Definitions</CollapseToDefinitions>
    <Column>Column: {0}</Column>
    <EntireScope>&amp;Entire Scope</EntireScope>
    <Find>&amp;Find</Find>
    <FindNext>&amp;Find Next</FindNext>
    <FindWhat>Find What:</FindWhat>
    <FromCursor>From Cursor</FromCursor>
    <GotoLine>Go To &amp;Line</GotoLine>
    <InsertField>Insert Field</InsertField>
    <InsertLink>Insert Link</InsertLink>
    <InsertSymbol>Insert Symbol</InsertSymbol>
    <Line>Line: {0}</Line>
    <LineNumber>Line Number:</LineNumber>
    <LineNumberIndex>Line Number ({0} - {1})</LineNumberIndex>
    <MarkAll>&amp;Mark All</MarkAll>
    <MatchCase>Match &amp;Case</MatchCase>
    <MatchWholeWord>Match &amp;Whole Word</MatchWholeWord>
    <MaximumDimensions>Maximum 7 dimensions can be selected.</MaximumDimensions>
    <Outlining>Out&amp;lining</Outlining>
    <PromptOnReplace>Prompt on Replace</PromptOnReplace>
    <Replace>&amp;Replace</Replace>
    <ReplaceAll>Replace &amp;All</ReplaceAll>
    <ReplaceWith>Replace With:</ReplaceWith>
    <Search>Search</Search>
    <SearchHiddenText>Hidden Text</SearchHiddenText>
    <SearchUp>Search &amp;Up</SearchUp>
    <SelectionOnly>Selection &amp;Only</SelectionOnly>
    <ShowAutoGeneratedCode>Show Auto Generated Code</ShowAutoGeneratedCode>
    <ShowLineNumbers>Show Line Numbers</ShowLineNumbers>
    <StopOutlining>Sto&amp;p Outlining</StopOutlining>
    <titleFind>Find</titleFind>
    <titleGotoLine>Go To Line</titleGotoLine>
    <titleReplace>Replace</titleReplace>
    <ToggleAllOutlining>Toggle A&amp;ll Outlining</ToggleAllOutlining>
    <ToggleOutliningExpansion>&amp;Toggle Outlining Expansion</ToggleOutliningExpansion>
    <TypeToSearch>Type to search</TypeToSearch>
    <UseRegularExpressions>Use &amp;Regular Expressions</UseRegularExpressions>
  </Editor>
  <Errors>
    <ComponentIsNotRelease>Component is not release "{0}".</ComponentIsNotRelease>
    <ContainerIsNotValidForComponent>Container {0} is not valid for component {1}.</ContainerIsNotValidForComponent>
    <DataNotFound>Data not found.</DataNotFound>
    <DataNotLoaded>No data loaded.</DataNotLoaded>
    <Error>Error!</Error>
    <ErrorsList>Errors List</ErrorsList>
    <FieldRequire>Field "{0}" required filling.</FieldRequire>
    <FileNotFound>File "{0}" not found.</FileNotFound>
    <IdentifierIsNotValid>Identifier '{0}' is not valid.</IdentifierIsNotValid>
    <ImpossibleFindDataSource>Impossible to Find Data Source.</ImpossibleFindDataSource>
    <NameExists>There is already an object named '{0}'. Objects must have unique names.</NameExists>
    <NoServices>Services are not found in '{0}'</NoServices>
    <NotAssign>Data Source is not specified.</NotAssign>
    <NotCorrectFormat>Input string was not in a correct format.</NotCorrectFormat>
    <OneTableOfContentsAllowed>Only one 'Table of Contents' component can be used in a report at the same time!</OneTableOfContentsAllowed>
    <PrimaryColumnAction>Unable to apply this action to the primary column of the '{0}'.</PrimaryColumnAction>
    <RelationsNotFound>Relations not found.</RelationsNotFound>
    <ReportCannotBeSaveDueToErrors>Report cannot be saved due to errors!</ReportCannotBeSaveDueToErrors>
    <ServiceNotFound>'{0}' Service not found.</ServiceNotFound>
  </Errors>
  <ExceptionProvider>
    <Assemblies>Assemblies</Assemblies>
    <ClickIgnore>Click 'Ignore' to delete all auto-saved files.</ClickIgnore>
    <Exception>Exception</Exception>
    <ExceptionReport>Exception Report</ExceptionReport>
    <Framework>Framework</Framework>
    <General>General</General>
    <Message>Message</Message>
    <Number>Number</Number>
    <PleaseEnterDetailedInformation>Please enter detailed information about events which cause this exception.</PleaseEnterDetailedInformation>
    <SaveToFile>Save to File</SaveToFile>
    <SendErrorFailed>Failed to deliver the error message.</SendErrorFailed>
    <SendErrorSuccess>The error message delivered successfully.</SendErrorSuccess>
    <Source>Source</Source>
    <StackTrace>Stack Trace</StackTrace>
    <YouCanRestore>You can restore unsaved changes from a backup file.</YouCanRestore>
    <YourPreviousWorkSession>In your previous work session, the designer terminated unexpectedly.</YourPreviousWorkSession>
  </ExceptionProvider>
  <Export>
    <AddPageBreaks>Add Page Breaks</AddPageBreaks>
    <AllBands>All bands</AllBands>
    <AllowAddOrModifyTextAnnotations>Allow Add or Modify Text Annotations</AllowAddOrModifyTextAnnotations>
    <AllowCopyTextAndGraphics>Allow Copy Text and Graphics</AllowCopyTextAndGraphics>
    <AllowEditable>Allow Editable:</AllowEditable>
    <AllowModifyContents>Allow Modify Contents</AllowModifyContents>
    <AllowPrintDocument>Allow Print Document</AllowPrintDocument>
    <Auto>Auto</Auto>
    <BandsFilter>Bands Filter:</BandsFilter>
    <CancelExport>Cancel Export</CancelExport>
    <Color>Color</Color>
    <Compressed>Compressed</Compressed>
    <CompressToArchive>Compress to Archive</CompressToArchive>
    <ContinuousPages>Continuous Pages</ContinuousPages>
    <DataAndHeaders>Data and Headers</DataAndHeaders>
    <DataAndHeadersFooters>Data and Headers/Footers</DataAndHeadersFooters>
    <DataOnly>Data only</DataOnly>
    <DigitalSignature>Digital Signature</DigitalSignature>
    <DigitalSignatureCertificateNotSelected>Certificate is not selected</DigitalSignatureCertificateNotSelected>
    <DigitalSignatureError>Digital Signature error at step</DigitalSignatureError>
    <DocumentSecurity>Document Security</DocumentSecurity>
    <DotMatrixMode>Dot-Matrix Mode</DotMatrixMode>
    <EmbeddedFonts>Embedded Fonts</EmbeddedFonts>
    <EmbeddedImageData>Embedded Image Data</EmbeddedImageData>
    <EnableAnimation>Enable Animation</EnableAnimation>
    <Encoding>Encoding:</Encoding>
    <EncryptionError>Encryption error at step</EncryptionError>
    <EscapeCodes>Escape Codes</EscapeCodes>
    <Exactly>Exactly</Exactly>
    <ExceptEditableFields>Except Editable Fields</ExceptEditableFields>
    <ExportDataOnly>Export Data Only</ExportDataOnly>
    <ExportEachPageToSheet>Export Each Page to Sheet</ExportEachPageToSheet>
    <Exporting>Exporting</Exporting>
    <ExportingCalculatingCoordinates>Calculating Coordinates</ExportingCalculatingCoordinates>
    <ExportingCreatingDocument>Creating Document</ExportingCreatingDocument>
    <ExportingFormatingObjects>Formatting Objects</ExportingFormatingObjects>
    <ExportingReport>Exporting Report</ExportingReport>
    <ExportMode>Export Mode:</ExportMode>
    <ExportModeFrame>Frame</ExportModeFrame>
    <ExportModeTable>Table</ExportModeTable>
    <ExportObjectFormatting>Export Object Formatting</ExportObjectFormatting>
    <ExportPageBreaks>Export Page Breaks</ExportPageBreaks>
    <ExportRtfTextAsImage>Export Rich Text as Image</ExportRtfTextAsImage>
    <ExportTypeBmpFile>BMP Image...</ExportTypeBmpFile>
    <ExportTypeCalcFile>OpenDocument Calc File...</ExportTypeCalcFile>
    <ExportTypeCsvFile>CSV File...</ExportTypeCsvFile>
    <ExportTypeDataFile>Data File...</ExportTypeDataFile>
    <ExportTypeDbfFile>dBase DBF File...</ExportTypeDbfFile>
    <ExportTypeDifFile>Data Interchange Format (DIF) File...</ExportTypeDifFile>
    <ExportTypeExcel2007File>Microsoft Excel 2007 File...</ExportTypeExcel2007File>
    <ExportTypeExcelFile>Microsoft Excel File...</ExportTypeExcelFile>
    <ExportTypeExcelXmlFile>Microsoft Excel XML File...</ExportTypeExcelXmlFile>
    <ExportTypeGifFile>GIF Image...</ExportTypeGifFile>
    <ExportTypeHtml5File>HTML5 File...</ExportTypeHtml5File>
    <ExportTypeHtmlFile>HTML File...</ExportTypeHtmlFile>
    <ExportTypeImageFile>Image File...</ExportTypeImageFile>
    <ExportTypeJpegFile>JPEG Image...</ExportTypeJpegFile>
    <ExportTypeJsonFile>JSON File...</ExportTypeJsonFile>
    <ExportTypeMetafile>Windows Metafile...</ExportTypeMetafile>
    <ExportTypeMhtFile>MHT Web Archive...</ExportTypeMhtFile>
    <ExportTypePcxFile>PCX Image...</ExportTypePcxFile>
    <ExportTypePdfFile>Adobe PDF File...</ExportTypePdfFile>
    <ExportTypePngFile>PNG Image...</ExportTypePngFile>
    <ExportTypePpt2007File>Microsoft PowerPoint File...</ExportTypePpt2007File>
    <ExportTypeRtfFile>Rich Text File...</ExportTypeRtfFile>
    <ExportTypeSvgFile>Scalable Vector Graphics (SVG) File...</ExportTypeSvgFile>
    <ExportTypeSvgzFile>Compressed SVG (SVGZ) File...</ExportTypeSvgzFile>
    <ExportTypeSylkFile>Symbolic Link (SYLK) File...</ExportTypeSylkFile>
    <ExportTypeTiffFile>TIFF Image...</ExportTypeTiffFile>
    <ExportTypeTxtFile>Text File...</ExportTypeTxtFile>
    <ExportTypeWord2007File>Microsoft Word File...</ExportTypeWord2007File>
    <ExportTypeWriterFile>OpenDocument Writer File...</ExportTypeWriterFile>
    <ExportTypeXmlFile>XML File...</ExportTypeXmlFile>
    <ExportTypeXpsFile>Microsoft XPS File...</ExportTypeXpsFile>
    <GetCertificateFromCryptoUI>Get Certificate from Crypto UI</GetCertificateFromCryptoUI>
    <ImageCompressionMethod>Image Compression Method:</ImageCompressionMethod>
    <ImageCutEdges>Cut Edges</ImageCutEdges>
    <ImageFormat>Image Format:</ImageFormat>
    <ImageGrayscale>Grayscale</ImageGrayscale>
    <ImageMonochrome>Monochrome</ImageMonochrome>
    <ImageQuality>Image Quality:</ImageQuality>
    <ImageResolution>Image Resolution:</ImageResolution>
    <ImageResolutionMode>Image Resolution Mode:</ImageResolutionMode>
    <ImageType>Image Type</ImageType>
    <labelEncryptionKeyLength>Encryption Key Length:</labelEncryptionKeyLength>
    <labelOwnerPassword>Owner Password:</labelOwnerPassword>
    <labelSubjectNameString>Subject Name String:</labelSubjectNameString>
    <labelUserPassword>User Password:</labelUserPassword>
    <MonochromeDitheringType>Monochrome Dithering Type:</MonochromeDitheringType>
    <MoreSettings>More Settings</MoreSettings>
    <MultipleFiles>Multiple Files</MultipleFiles>
    <NoMoreThan>No More Than</NoMoreThan>
    <OpenAfterExport>Open After Export</OpenAfterExport>
    <PdfACompliance>PDF/A Compliance</PdfACompliance>
    <PrintingReport>Printing Report</PrintingReport>
    <RemoveEmptySpaceAtBottom>Remove Empty Space at Bottom of Page</RemoveEmptySpaceAtBottom>
    <RestrictEditing>Restrict Editing:</RestrictEditing>
    <Scale>Scale:</Scale>
    <Separator>Separator:</Separator>
    <Settings>Settings</Settings>
    <SkipColumnHeaders>Skip Column Headers</SkipColumnHeaders>
    <StandardPDFFonts>Standard PDF Fonts</StandardPDFFonts>
    <TiffCompressionScheme>TIFF Compression Scheme:</TiffCompressionScheme>
    <title>Export Settings</title>
    <TxtBorderType>Border Type</TxtBorderType>
    <TxtBorderTypeDouble>Unicode-Double</TxtBorderTypeDouble>
    <TxtBorderTypeSimple>Simple</TxtBorderTypeSimple>
    <TxtBorderTypeSingle>Unicode-Single</TxtBorderTypeSingle>
    <TxtCutLongLines>Cut Long Lines</TxtCutLongLines>
    <TxtDrawBorder>Draw Border</TxtDrawBorder>
    <TxtKillSpaceGraphLines>Kill Space Graph Lines</TxtKillSpaceGraphLines>
    <TxtKillSpaceLines>Kill Space Lines</TxtKillSpaceLines>
    <TxtPutFeedPageCode>Put Feed Page Code</TxtPutFeedPageCode>
    <Type>Type:</Type>
    <UseDefaultSystemEncoding>Use Default System Encoding</UseDefaultSystemEncoding>
    <UseDigitalSignature>Use Digital Signature</UseDigitalSignature>
    <UseEscapeCodes>Use Escape Codes</UseEscapeCodes>
    <UseOnePageHeaderAndFooter>Use One Page Header and Footer</UseOnePageHeaderAndFooter>
    <UsePageHeadersAndFooters>Use Page Headers and Footers</UsePageHeadersAndFooters>
    <UseUnicode>Use Unicode</UseUnicode>
    <X>X:</X>
    <Y>Y:</Y>
    <Zoom>Zoom:</Zoom>
  </Export>
  <FileFilters>
    <AllFiles>All Files</AllFiles>
    <AllImageFiles>All Image Files</AllImageFiles>
    <BitmapFiles>Bitmap Files</BitmapFiles>
    <BmpFiles>BMP Images (*.bmp)|*.bmp</BmpFiles>
    <CalcFiles>OpenDocument Calc Files (*.ods)|*.ods</CalcFiles>
    <ConditionsFiles>Stimulsoft Conditions (*.stc)|*.stc</ConditionsFiles>
    <CsvFiles>CSV Files (*.csv)|*.csv</CsvFiles>
    <DataSetXmlData>DataSet XML Data (*.xml)|*.xml</DataSetXmlData>
    <DataSetXmlSchema>DataSet XML Schema (*.xsd)|*.xsd</DataSetXmlSchema>
    <DbfFiles>DBF Files (*.dbf)|*.dbf</DbfFiles>
    <DictionaryFiles>Stimulsoft Dictionaries (*.dct)|*.dct</DictionaryFiles>
    <DifFiles>DIF Files (*.dif)|*.dif</DifFiles>
    <DllFiles>DLL Files (*.dll)|*.dll</DllFiles>
    <DocumentFiles>Stimulsoft Documents (*.mdc)|*.mdc</DocumentFiles>
    <EmfFiles>Metafiles (*.emf)|*.emf</EmfFiles>
    <EncryptedDocumentFiles>Stimulsoft Encrypted Documents (*.mdx)|*.mdx</EncryptedDocumentFiles>
    <EncryptedReportFiles>Stimulsoft Encrypted Templates (*.mrx)|*.mrx</EncryptedReportFiles>
    <Excel2007Files>Microsoft Excel files (*.xlsx)|*.xlsx</Excel2007Files>
    <ExcelAllFiles>Microsoft Excel files (*.xlsx;*.xls)|*.xlsx;*.xls</ExcelAllFiles>
    <ExcelCsvAllFiles>Microsoft Excel files (*.xlsx;*.xls;*.csv)|*.xlsx;*.xls;*.csv</ExcelCsvAllFiles>
    <ExcelFiles>Microsoft Excel 97-2003 files (*.xls)|*.xls</ExcelFiles>
    <ExcelXmlFiles>Microsoft Excel XML files (*.xml)|*.xml</ExcelXmlFiles>
    <ExeFiles>EXE Files (*.exe)|*.exe</ExeFiles>
    <GifFiles>GIF Image (*.gif)|*.gif</GifFiles>
    <HtmlFiles>HTML Files (*.html)|*.html</HtmlFiles>
    <InheritedLanguageFiles>{0} Classes for Inherited Reports (*.{1})|*.{2}</InheritedLanguageFiles>
    <JpegFiles>JPEG image (*.jpg;*.jpeg)|*.jpg;*.jpeg</JpegFiles>
    <JsonDocumentFiles>Stimulsoft JSON Documents (*.mdc)|*.mdc</JsonDocumentFiles>
    <JsonFiles>JSON Files (*.json)|*.json</JsonFiles>
    <JsonReportFiles>Stimulsoft JSON Templates (*.mrt)|*.mrt</JsonReportFiles>
    <LanguageFiles>{0} Classes (*.{1})|*.{2}</LanguageFiles>
    <LanguageForSilverlightFiles>{0} Classes for Silverlight Reports (*.{1})|*.{2}</LanguageForSilverlightFiles>
    <MetaFiles>Metafiles</MetaFiles>
    <MhtFiles>MHT Web Archives (*.mht)|*.mht</MhtFiles>
    <PackedDocumentFiles>Stimulsoft Packed Documents (*.mdz)|*.mdz</PackedDocumentFiles>
    <PackedReportFiles>Stimulsoft Packed Templates (*.mrz)|*.mrz</PackedReportFiles>
    <PageFiles>Stimulsoft Pages (*.pg)|*.pg</PageFiles>
    <PcxFiles>PCX Images (*.pcx)|*.pcx</PcxFiles>
    <PdfFiles>Adobe PDF Files (*.pdf)|*.pdf</PdfFiles>
    <PngFiles>PNG Images (*.png)|*.png</PngFiles>
    <Ppt2007Files>Microsoft PowerPoint 2007/2013 Files (*.pptx)|*.pptx</Ppt2007Files>
    <ReportEmbededDataFiles>Stimulsoft Templates with Embedded Data (*.mrt)|*.mrt</ReportEmbededDataFiles>
    <ReportFiles>Stimulsoft Templates (*.mrt)|*.mrt</ReportFiles>
    <RtfFiles>Rich Text (*.rtf)|*.rtf</RtfFiles>
    <StandaloneReportFiles>Stimulsoft Standalone Files (*.exe)|*.exe</StandaloneReportFiles>
    <StylesFiles>Stimulsoft Styles (*.sts)|*.sts</StylesFiles>
    <SvgFiles>SVG Images (*.svg)|*.svg</SvgFiles>
    <SvgzFiles>Compressed SVG Images (*.svgz)|*.svgz</SvgzFiles>
    <SylkFiles>SYLK Files (*.slk)|*.slk</SylkFiles>
    <TiffFiles>TIFF Images (*.tiff)|*.tiff</TiffFiles>
    <TxtFiles>Plain Text Files (*.txt)|*.txt</TxtFiles>
    <Word2007Files>Microsoft Word 2007-2016 Files (*.docx)|*.docx</Word2007Files>
    <WordFiles>Microsoft Word 97-2003 Files (*.doc)|*.doc</WordFiles>
    <WriterFiles>OpenDocument Writer Files (*.odt)|*.odt</WriterFiles>
    <XmlFiles>XML Files (*.xml)|*.xml</XmlFiles>
    <XpsFiles>Microsoft XPS Files (*.xps)|*.xps</XpsFiles>
    <ZipArchives>ZIP Archives (*.zip)|*.zip</ZipArchives>
  </FileFilters>
  <Formats>
    <custom01>d</custom01>
    <custom02>D</custom02>
    <custom03>f</custom03>
    <custom04>F</custom04>
    <custom05>yy/MM/dd</custom05>
    <custom06>yyyy/MM/dd</custom06>
    <custom07>[green]+0.00;[red]-0.00;[blue](0)</custom07>
    <custom08>G</custom08>
    <custom09>$0.00</custom09>
    <custom10>$0</custom10>
    <custom11>c</custom11>
    <custom12>c1</custom12>
    <custom13>c2</custom13>
    <custom14>#.00</custom14>
    <custom15>#.#</custom15>
    <custom16>n</custom16>
    <custom17>n1</custom17>
    <custom18>n2</custom18>
    <custom19>(###) ### - ####</custom19>
    <date01>*d</date01>
    <date02>*D</date02>
    <date03>M.dd</date03>
    <date04>yy.M.dd</date04>
    <date05>yy.MM.dd</date05>
    <date06>MMM.dd</date06>
    <date07>yy.MMM.dd</date07>
    <date08>yyyy, MMMM</date08>
    <date09>*f</date09>
    <date10>*F</date10>
    <date11>MM.dd.yyyy</date11>
    <date12>dd/MM/yyyy</date12>
    <date13>*g</date13>
    <date14>*G</date14>
    <date15>*y</date15>
    <date16>*m</date16>
    <date17>s</date17>
    <date18>u</date18>
    <date19>*Q</date19>
    <date20>*YQ</date20>
    <date21>QI</date21>
    <date22>YQI</date22>
    <time01>*t</time01>
    <time02>*T</time02>
    <time03>HH:mm</time03>
    <time04>H:mm</time04>
    <time06>HH:mm:ss</time06>
  </Formats>
  <FormBand>
    <AddFilter>&amp;Add Filter</AddFilter>
    <AddGroup>&amp;Add Group</AddGroup>
    <AddResult>&amp;Add Result</AddResult>
    <AddSort>&amp;Add Sort</AddSort>
    <And>and</And>
    <Ascending>Ascending</Ascending>
    <Descending>Descending</Descending>
    <NoFilters>No Filters</NoFilters>
    <NoSort>No Sorting</NoSort>
    <RemoveFilter>&amp;Remove Filter</RemoveFilter>
    <RemoveGroup>&amp;Remove Group</RemoveGroup>
    <RemoveResult>&amp;Remove Result</RemoveResult>
    <RemoveSort>&amp;Remove Sort</RemoveSort>
    <SortBy>Sort by</SortBy>
    <ThenBy>Then by</ThenBy>
    <title>Data Setup</title>
  </FormBand>
  <FormColorBoxPopup>
    <Color>Color</Color>
    <Custom>Custom</Custom>
    <NoColor>No Color</NoColor>
    <Others>Others...</Others>
    <System>System</System>
    <Web>Web</Web>
  </FormColorBoxPopup>
  <FormConditions>
    <AaBbCcYyZz>AaBbCcYyZz</AaBbCcYyZz>
    <AddCondition>&amp;Add Condition</AddCondition>
    <AddLevel>Add Level</AddLevel>
    <AssignExpression>Assign Expression</AssignExpression>
    <BreakIfTrue>Break if True</BreakIfTrue>
    <BreakIfTrueToolTip>Breaks condition processing if current one is fulfilled</BreakIfTrueToolTip>
    <ChangeBorder>Change Border...</ChangeBorder>
    <ChangeFont>Change Font...</ChangeFont>
    <ComponentIsEnabled>Component is Enabled</ComponentIsEnabled>
    <NoConditions>No Conditions</NoConditions>
    <RemoveCondition>&amp;Remove Condition</RemoveCondition>
    <SelectStyle>Select Style</SelectStyle>
    <title>Conditions</title>
  </FormConditions>
  <FormCrossTabDesigner>
    <Columns>Columns:</Columns>
    <DataSource>Data Source:</DataSource>
    <Properties>Properties:</Properties>
    <Rows>Rows:</Rows>
    <Summary>Summary:</Summary>
    <Swap>Swap Rows / Columns</Swap>
    <title>Cross-Tab Designer</title>
  </FormCrossTabDesigner>
  <FormDatabaseEdit>
    <AccessToken>Access token</AccessToken>
    <AccountKey>Account Key</AccountKey>
    <AccountName>Account Name</AccountName>
    <AuthorizationCode>Authorization Code</AuthorizationCode>
    <BlobContentType>Blob Content Type</BlobContentType>
    <BlobName>Blob Name</BlobName>
    <ClientId>Client Id</ClientId>
    <ClientSecret>Client Secret</ClientSecret>
    <ConnectionString>Connection String:</ConnectionString>
    <ContainerName>Container Name</ContainerName>
    <DashboardConnections>Dashboard Connections</DashboardConnections>
    <DatabaseSecret>Database Secret</DatabaseSecret>
    <DatasetId>Dataset Id</DatasetId>
    <DB2Edit>Edit IBM DB2 Connection</DB2Edit>
    <DB2New>New IBM DB2 Connection</DB2New>
    <EditConnection>Edit {0} Connection</EditConnection>
    <Favorites>Favorites</Favorites>
    <FirebirdEdit>Edit Firebird Connection</FirebirdEdit>
    <FirebirdNew>New Firebird Connection</FirebirdNew>
    <FirstRowIsHeader>First Row is Header</FirstRowIsHeader>
    <GetAuthorizationCode>Get authorization code</GetAuthorizationCode>
    <GetTokens>Get tokens</GetTokens>
    <GisEdit>Edit GIS Data</GisEdit>
    <GisNew>New GIS Data</GisNew>
    <ImportData>Import Data</ImportData>
    <InformixEdit>Edit Informix Connection</InformixEdit>
    <InformixNew>New Informix Connection</InformixNew>
    <InitialCatalog>Initial Catalog:</InitialCatalog>
    <InsertSampleConnectionString>Insert sample connection string</InsertSampleConnectionString>
    <JsonEdit>Edit JSON Data</JsonEdit>
    <JsonNew>New JSON Data</JsonNew>
    <MySQLEdit>Edit MySQL Connection</MySQLEdit>
    <MySQLNew>New MySQL Connection</MySQLNew>
    <NewConnection>New {0} Connection</NewConnection>
    <OdbcEdit>Edit ODBC Connection</OdbcEdit>
    <OdbcNew>New ODBC Connection</OdbcNew>
    <OleDbEdit>Edit OLE DB Connection</OleDbEdit>
    <OleDbNew>New OLE DB Connection</OleDbNew>
    <OracleEdit>Edit Oracle Connection</OracleEdit>
    <OracleNew>New Oracle Connection</OracleNew>
    <OracleODPEdit>Edit Oracle ODP.NET Connection</OracleODPEdit>
    <OracleODPNew>New Oracle ODP.NET Connection</OracleODPNew>
    <PathData>Path to XML Data:</PathData>
    <PathGisData>Path to Gis Data:</PathGisData>
    <PathJsonData>Path to JSON Data:</PathJsonData>
    <PathSchema>Path to XSD Schema:</PathSchema>
    <PathToData>Path to Data:</PathToData>
    <Pin>Pin</Pin>
    <PostgreSQLEdit>Edit PostgreSQL Connection</PostgreSQLEdit>
    <PostgreSQLNew>New PostgreSQL Connection</PostgreSQLNew>
    <ProjectId>Project Id</ProjectId>
    <PromptUserNameAndPassword>Prompt User Name and Password</PromptUserNameAndPassword>
    <RecentConnections>Recent</RecentConnections>
    <RedirectUrl>Redirect URL</RedirectUrl>
    <RefreshAccessToken>Refresh access token</RefreshAccessToken>
    <RefreshToken>Refresh token</RefreshToken>
    <RelationDirection>Relation Direction:</RelationDirection>
    <ReportConnections>Report Connections</ReportConnections>
    <SelectData>Select Data</SelectData>
    <ServiceAccountKeyFile>Service Account Key File</ServiceAccountKeyFile>
    <SpreadsheetId>Spreadsheet Id</SpreadsheetId>
    <SqlCeEdit>Edit SQLServerCE Connection</SqlCeEdit>
    <SqlCeNew>New SQLServerCE Connection</SqlCeNew>
    <SqlEdit>Edit SQL Connection</SqlEdit>
    <SQLiteEdit>Edit SQLite Connection</SQLiteEdit>
    <SQLiteNew>New SQLite Connection</SQLiteNew>
    <SqlNew>New SQL Connection</SqlNew>
    <TeradataEdit>Edit Teradata Connection</TeradataEdit>
    <TeradataNew>New Teradata Connection</TeradataNew>
    <Token>Token</Token>
    <UniDirectEdit>Edit Uni Direct Connection</UniDirectEdit>
    <UniDirectNew>New Uni Direct Connection</UniDirectNew>
    <Unpin>Unpin</Unpin>
    <UseBearerAuthentication>Use Bearer Authentication</UseBearerAuthentication>
    <UseOwnClientID>Use own "Client ID" and "Client Secret"</UseOwnClientID>
    <UseToken>Use Token</UseToken>
    <VistaDBEdit>Edit VistaDB Connection</VistaDBEdit>
    <VistaDBNew>New VistaDB Connection</VistaDBNew>
    <XmlEdit>Edit XML Data</XmlEdit>
    <XmlNew>New XML Data</XmlNew>
    <XmlType>XML Type:</XmlType>
  </FormDatabaseEdit>
  <FormDesigner>
    <Code>Code</Code>
    <ColumnsOne>One</ColumnsOne>
    <ColumnsThree>Three</ColumnsThree>
    <ColumnsTwo>Two</ColumnsTwo>
    <CompilingReport>Compiling Report</CompilingReport>
    <CopyPropertyName>Copy Property Name</CopyPropertyName>
    <DockingPanels>Panels</DockingPanels>
    <HtmlPreview>HTML Preview</HtmlPreview>
    <JsPreview>JS Preview</JsPreview>
    <labelPleaseSelectTypeOfInterface>Please, select type of interface</labelPleaseSelectTypeOfInterface>
    <LoadImage>Load Image...</LoadImage>
    <LocalizePropertyGrid>Localize Property Grid</LocalizePropertyGrid>
    <MarginsNarrow>Narrow</MarginsNarrow>
    <MarginsNormal>Normal</MarginsNormal>
    <MarginsWide>Wide</MarginsWide>
    <OrderToolbars>Order Toolbars</OrderToolbars>
    <Others>Others</Others>
    <Pages>Pages</Pages>
    <Preview>Preview</Preview>
    <PropertyChange>Change of property '{0}'</PropertyChange>
    <RTPreview>WinRT Preview</RTPreview>
    <SetupToolbox>Setup Toolbox</SetupToolbox>
    <ShowDescription>Show Description</ShowDescription>
    <SLPreview>Silverlight Preview</SLPreview>
    <title>Designer</title>
    <WebPreview>Flash Preview</WebPreview>
  </FormDesigner>
  <FormDictionaryDesigner>
    <Actions>Actions</Actions>
    <AutoSort>Auto Sort</AutoSort>
    <BusinessObjectEdit>Edit Business Object</BusinessObjectEdit>
    <CalcColumnEdit>Edit Calculated Column</CalcColumnEdit>
    <CalcColumnNew>New Calculated Column</CalcColumnNew>
    <CategoryEdit>Edit Category</CategoryEdit>
    <CategoryNew>New Category</CategoryNew>
    <Child>Child</Child>
    <ChildOfBusinessObject>Child of Business Object</ChildOfBusinessObject>
    <ChildSource>Child Data Source:</ChildSource>
    <ClickHere>Click here</ClickHere>
    <ColumnEdit>Edit Column</ColumnEdit>
    <ColumnNew>New Column</ColumnNew>
    <CreateNewDataSource>to create the new data source</CreateNewDataSource>
    <CreateNewReport>to create the new report</CreateNewReport>
    <CsvSeparatorComma>Comma</CsvSeparatorComma>
    <CsvSeparatorOther>Other</CsvSeparatorOther>
    <CsvSeparatorSemicolon>Semicolon</CsvSeparatorSemicolon>
    <CsvSeparatorSpace>Space</CsvSeparatorSpace>
    <CsvSeparatorSystem>System</CsvSeparatorSystem>
    <CsvSeparatorTab>Tab</CsvSeparatorTab>
    <DatabaseEdit>Edit Database</DatabaseEdit>
    <DatabaseNew>New Database</DatabaseNew>
    <DataMonitor>Data Monitor</DataMonitor>
    <DataParameterEdit>Edit Parameter</DataParameterEdit>
    <DataParameterNew>New Parameter</DataParameterNew>
    <DataSetToBusinessObjects>DataSet To Business Objects</DataSetToBusinessObjects>
    <DataSourceEdit>Edit Data Source</DataSourceEdit>
    <DataSourceNew>New Data Source</DataSourceNew>
    <DataSourcesNew>New Data Sources</DataSourcesNew>
    <DataTransformationEdit>Edit Data Transformation</DataTransformationEdit>
    <DataTransformationNew>New Data Transformation</DataTransformationNew>
    <Delete>Delete</Delete>
    <DesignTimeQueryText>Design-Time Query Text</DesignTimeQueryText>
    <DictionaryMerge>Merge Dictionary...</DictionaryMerge>
    <DictionaryNew>New Dictionary...</DictionaryNew>
    <DictionaryOpen>Open Dictionary...</DictionaryOpen>
    <DictionarySaveAs>Save Dictionary As...</DictionarySaveAs>
    <DragNewDataSource>drag your data directly to this panel</DragNewDataSource>
    <DragNewReport>drag your report directly to this panel</DragNewReport>
    <EditQuery>Edit Query</EditQuery>
    <EnableDataMonitor>Enable Data Monitor</EnableDataMonitor>
    <ExecutedSQLStatementSuccessfully>SQL statement executed successfully</ExecutedSQLStatementSuccessfully>
    <ExpressionNew>New Expression</ExpressionNew>
    <FunctionEdit>Edit Function</FunctionEdit>
    <FunctionNew>New Function</FunctionNew>
    <GetColumnsFromAssembly>Get Columns from Assembly</GetColumnsFromAssembly>
    <HideUnsupportedDatabases>Hide Unsupported Data Sources</HideUnsupportedDatabases>
    <ImportRelations>Import Relations</ImportRelations>
    <LabelSeparator>Separator:</LabelSeparator>
    <MarkUsedItems>Mark Used Items</MarkUsedItems>
    <NewBusinessObject>New Business Object</NewBusinessObject>
    <NewItem>New Item</NewItem>
    <OpenAssembly>Open Assembly</OpenAssembly>
    <Parent>Parent</Parent>
    <ParentSource>Parent Source:</ParentSource>
    <Queries>Queries</Queries>
    <QueryNew>New Query</QueryNew>
    <QueryText>Query Text</QueryText>
    <QueryTimeout>Query Timeout</QueryTimeout>
    <RelationEdit>Edit Relation</RelationEdit>
    <RelationNew>New Relation</RelationNew>
    <ResourceEdit>Edit Resource</ResourceEdit>
    <ResourceNew>New Resource</ResourceNew>
    <RetrieveColumns>Retrieve Columns</RetrieveColumns>
    <RetrieveColumnsAllowRun>Allow Run StoredProc</RetrieveColumnsAllowRun>
    <RetrieveColumnsAndParameters>Retrieve Columns and Parameters</RetrieveColumnsAndParameters>
    <RetrieveParameters>Retrieve Parameters</RetrieveParameters>
    <RetrievingDatabaseInformation>Retrieving database information...</RetrievingDatabaseInformation>
    <Run>Run</Run>
    <SelectTypeOfBusinessObject>Select Type of Business Object</SelectTypeOfBusinessObject>
    <ShowMoreDataSources>Show More Data Sources</ShowMoreDataSources>
    <SkipSchemaWizard>Skip Schema Wizard</SkipSchemaWizard>
    <SortItems>Sort Items</SortItems>
    <Synchronize>Synchronize</Synchronize>
    <SynchronizeHint>Synchronizes contents of the Data Store and contents of the Dictionary</SynchronizeHint>
    <TextDropDataFileHere>Drop Data File Here</TextDropDataFileHere>
    <TextDropFileHere>Drop File Here</TextDropFileHere>
    <TextDropImageHere>Drop Image File Here</TextDropImageHere>
    <title>Dictionary Designer</title>
    <ValueNew>New Value</ValueNew>
    <VariableEdit>Edit Variable</VariableEdit>
    <VariableNew>New Variable</VariableNew>
    <ViewData>View Data</ViewData>
    <ViewQuery>View Query</ViewQuery>
  </FormDictionaryDesigner>
  <FormFormatEditor>
    <Boolean>Boolean</Boolean>
    <BooleanDisplay>Display:</BooleanDisplay>
    <BooleanValue>Value:</BooleanValue>
    <Currency>Currency</Currency>
    <CurrencySymbol>Currency Symbol:</CurrencySymbol>
    <Custom>Custom</Custom>
    <Date>Date</Date>
    <DateTimeFormat>Date Time Format</DateTimeFormat>
    <DecimalDigits>Decimal Digits:</DecimalDigits>
    <DecimalSeparator>Decimal Separator:</DecimalSeparator>
    <FormatMask>Format Mask:</FormatMask>
    <Formats>Formats</Formats>
    <General>General</General>
    <GroupSeparator>Group Separator:</GroupSeparator>
    <GroupSize>Group Size:</GroupSize>
    <nameFalse>False</nameFalse>
    <nameNo>No</nameNo>
    <nameOff>Off</nameOff>
    <nameOn>On</nameOn>
    <nameTrue>True</nameTrue>
    <nameYes>Yes</nameYes>
    <NegativeInRed>Negative in Red</NegativeInRed>
    <NegativePattern>Negative Pattern:</NegativePattern>
    <Number>Number</Number>
    <Percentage>Percentage</Percentage>
    <PercentageSymbol>Percentage Symbol:</PercentageSymbol>
    <PositivePattern>Positive Pattern:</PositivePattern>
    <Properties>Properties</Properties>
    <Sample>Sample</Sample>
    <SampleText>Sample Text</SampleText>
    <TextFormat>Text Format</TextFormat>
    <Time>Time</Time>
    <title>Format</title>
    <UseAbbreviation>Use Abbreviation</UseAbbreviation>
    <UseGroupSeparator>Use Group Separator</UseGroupSeparator>
    <UseLocalSetting>Use Local Setting</UseLocalSetting>
  </FormFormatEditor>
  <FormGlobalizationEditor>
    <AddCulture>&amp;Add Culture</AddCulture>
    <AutoLocalizeReportOnRun>Auto Localize Report on Run</AutoLocalizeReportOnRun>
    <CreateNewCulture>to create the new culture</CreateNewCulture>
    <GetCulture>Get Culture Settings from Report</GetCulture>
    <qnGetCulture>Do you really want to get culture settings from report and override current culture settings?</qnGetCulture>
    <qnSetCulture>Do you really want to set culture settings to report components?</qnSetCulture>
    <RemoveCulture>&amp;Remove Culture</RemoveCulture>
    <SetCulture>Set Culture Settings to Report</SetCulture>
    <title>Globalization Editor</title>
  </FormGlobalizationEditor>
  <FormInteraction>
    <HyperlinkExternalDocuments>Hyperlink to External Document</HyperlinkExternalDocuments>
    <HyperlinkUsingInteractionBookmark>Hyperlink using Bookmark</HyperlinkUsingInteractionBookmark>
    <HyperlinkUsingInteractionTag>Hyperlink using Tag</HyperlinkUsingInteractionTag>
  </FormInteraction>
  <FormOptions>
    <AutoSave>Auto Save</AutoSave>
    <AutoSaveReportToReportClass>Auto Save Report to C# or VB.NET File</AutoSaveReportToReportClass>
    <BlankDashboard>Blank Dashboard</BlankDashboard>
    <BlankForm>Blank Form</BlankForm>
    <BlankReport>Blank Report</BlankReport>
    <BlankScreen>Blank Screen</BlankScreen>
    <Default>Default</Default>
    <DefaultSaveFormat>Default Save Format</DefaultSaveFormat>
    <DefaultScriptMode>Default Script Mode</DefaultScriptMode>
    <DesignSize>Design Size</DesignSize>
    <Drawing>Drawing</Drawing>
    <DrawMarkersWhenMoving>Draw Markers When Moving</DrawMarkersWhenMoving>
    <EditAfterInsert>Edit After Insert</EditAfterInsert>
    <EnableAutoSaveMode>Enable Auto Save Mode</EnableAutoSaveMode>
    <FillBands>Fill Bands</FillBands>
    <FillComponents>Fill Components</FillComponents>
    <FillContainers>Fill Containers</FillContainers>
    <FillCrossBands>Fill Cross Bands</FillCrossBands>
    <GenerateLocalizedName>Generate Localized Name</GenerateLocalizedName>
    <Grid>Grid</Grid>
    <GridDots>Dots</GridDots>
    <GridLines>Lines</GridLines>
    <GridMode>Grid Mode</GridMode>
    <GridSize>Grid Size</GridSize>
    <groupAutoSaveOptions>Auto save options</groupAutoSaveOptions>
    <groupColorScheme>Please select color scheme of GUI</groupColorScheme>
    <groupGridDrawingOptions>Grid drawing options</groupGridDrawingOptions>
    <groupGridOptions>Grid options</groupGridOptions>
    <groupGridSize>Grid size</groupGridSize>
    <groupMainOptions>Main options for working with designer</groupMainOptions>
    <groupMarkersStyle>Marker style</groupMarkersStyle>
    <groupOptionsOfQuickInfo>Options of Quick Info</groupOptionsOfQuickInfo>
    <groupPleaseSelectTypeOfGui>Please, select the type of GUI</groupPleaseSelectTypeOfGui>
    <groupReportDisplayOptions>Report display options</groupReportDisplayOptions>
    <labelColorScheme>Color Scheme:</labelColorScheme>
    <labelInfoAutoSave>Change parameters of reports autosaving</labelInfoAutoSave>
    <labelInfoDrawing>Setting parameters of report drawing</labelInfoDrawing>
    <labelInfoGrid>How Grid is shown and used in a report</labelInfoGrid>
    <labelInfoGui>Select the mode of using GUI in the report designer</labelInfoGui>
    <labelInfoMain>Setting basic parameters of the report designer</labelInfoMain>
    <labelInfoQuickInfo>Components Quick Info on a page</labelInfoQuickInfo>
    <Main>Main</Main>
    <MarkersStyle>Markers Style</MarkersStyle>
    <MarkersStyleCorners>Corners</MarkersStyleCorners>
    <MarkersStyleDashedRectangle>Dashed Rectangle</MarkersStyleDashedRectangle>
    <MarkersStyleNone>None</MarkersStyleNone>
    <MessageLeftRightNotValid>Left and Right value is not a valid.</MessageLeftRightNotValid>
    <MessageTopBottomNotValid>Top and Bottom value is not a valid.</MessageTopBottomNotValid>
    <Minutes>{0} minutes</Minutes>
    <SaveReportEvery>Save Report Every:</SaveReportEvery>
    <ScaleMode>Scale Mode</ScaleMode>
    <SelectUILanguage>Select UI Language</SelectUILanguage>
    <ShowDialogForms>Show Dialog Forms</ShowDialogForms>
    <ShowDimensionLines>Show Dimension Lines</ShowDimensionLines>
    <ShowOldGaugeEditor>Show Old Gauge Editor</ShowOldGaugeEditor>
    <ShowSearch>Show Search</ShowSearch>
    <ShowSmartGuides>Show Smart Guides</ShowSmartGuides>
    <StartScreen>Start Screen</StartScreen>
    <title>Options</title>
    <UseComponentColor>Use Component Color for Filling</UseComponentColor>
    <UseLastFormat>Use Last Format</UseLastFormat>
    <ViewSize>View Size</ViewSize>
    <Welcome>Welcome</Welcome>
  </FormOptions>
  <FormPageSetup>
    <ApplyTo>Apply to</ApplyTo>
    <Bottom>Bottom:</Bottom>
    <Columns>Columns</Columns>
    <groupColumns>Page columns</groupColumns>
    <groupImage>Watermark image</groupImage>
    <groupMargins>Page margins</groupMargins>
    <groupOrientation>Paper orientation</groupOrientation>
    <groupPaper>Paper size</groupPaper>
    <groupPaperSource>Paper source</groupPaperSource>
    <groupText>Watermark text</groupText>
    <Height>Height:</Height>
    <labelAngle>Angle:</labelAngle>
    <labelColumnGaps>Column Gaps:</labelColumnGaps>
    <labelColumnWidth>Column Width:</labelColumnWidth>
    <labelImageAlignment>Image Alignment:</labelImageAlignment>
    <labelImageTransparency>Image Transparency:</labelImageTransparency>
    <labelInfoColumns>Setting page columns</labelInfoColumns>
    <labelInfoPaper>Setting size and orientation for the current page</labelInfoPaper>
    <labelInfoUnit>Page margins are specified in the current units</labelInfoUnit>
    <labelInfoWatermark>Setting parameters for showing watermark</labelInfoWatermark>
    <labelMultipleFactor>Multiple Factor:</labelMultipleFactor>
    <labelPaperSourceOfFirstPage>Paper Source of First Page:</labelPaperSourceOfFirstPage>
    <labelPaperSourceOfOtherPages>Paper Source of Other Pages:</labelPaperSourceOfOtherPages>
    <labelSelectBrush>Select Brush:</labelSelectBrush>
    <labelSelectColor>Select Color:</labelSelectColor>
    <labelSelectFont>Select Font:</labelSelectFont>
    <labelSelectImage>Select Image:</labelSelectImage>
    <labelText>Text:</labelText>
    <Left>Left:</Left>
    <Margins>Margins</Margins>
    <NumberOfColumns>Number of Columns:</NumberOfColumns>
    <Orientation>Orientation</Orientation>
    <PageOrientationLandscape>Landscape</PageOrientationLandscape>
    <PageOrientationPortrait>Portrait</PageOrientationPortrait>
    <Paper>Paper:</Paper>
    <RebuildReport>Rebuild Report</RebuildReport>
    <Right>Right:</Right>
    <ScaleContent>Scale Content</ScaleContent>
    <Size>Size:</Size>
    <title>Page Setup</title>
    <Top>Top:</Top>
    <Width>Width:</Width>
  </FormPageSetup>
  <FormReportSetup>
    <groupDates>A date of report creation and a date of the last report change</groupDates>
    <groupDescription>Report description</groupDescription>
    <groupMainParameters>Parameters which effect on report rendering</groupMainParameters>
    <groupNames>Report name, report alias, and report author</groupNames>
    <groupScript>Script language of your report</groupScript>
    <groupUnits>Size and coordinates in a report will be in specified units</groupUnits>
    <labelInfoDescription>Indicate the information of a report</labelInfoDescription>
    <labelInfoMain>Change of basic report parameters</labelInfoMain>
    <labelNumberOfPass>Number of Pass:</labelNumberOfPass>
    <labelReportCacheMode>Report Cache Mode:</labelReportCacheMode>
    <ReportChanged>Report Changed:</ReportChanged>
    <ReportCreated>Report Created:</ReportCreated>
    <title>Report Options</title>
  </FormReportSetup>
  <FormRichTextEditor>
    <Bullets>Bullets</Bullets>
    <FontName>Font Name</FontName>
    <FontSize>Font Size</FontSize>
    <Insert>Insert Expression</Insert>
    <title>Rich Text Editor</title>
  </FormRichTextEditor>
  <FormStyleDesigner>
    <Add>Add Style</Add>
    <AddCollectionName>Add Collection Name</AddCollectionName>
    <ApplyStyleCollectionToReportComponents>Apply Style Collection to Report Components</ApplyStyleCollectionToReportComponents>
    <ApplyStyles>Apply Styles</ApplyStyles>
    <ColorCollectionEditor>Color Collection Editor</ColorCollectionEditor>
    <CreateNewComponentStyle>to create the new component style</CreateNewComponentStyle>
    <CreateStyleCollection>Create Style Collection</CreateStyleCollection>
    <CreateStyleOnBase>Create a style based on another style</CreateStyleOnBase>
    <Duplicate>Duplicate Style</Duplicate>
    <EditColors>Edit Colors</EditColors>
    <FromStyle>From Style</FromStyle>
    <GetStyle>Get Style from Selected Components</GetStyle>
    <MoreStyles>More Styles</MoreStyles>
    <NotSpecified>Not Specified</NotSpecified>
    <Open>Open Style</Open>
    <Predefined>Predefined</Predefined>
    <qnApplyStyleCollection>Do you want to apply style collection to report components?</qnApplyStyleCollection>
    <Remove>Remove Style</Remove>
    <RemoveExistingStyles>Remove Existing Styles</RemoveExistingStyles>
    <Save>Save Style</Save>
    <Style>Style</Style>
    <StyleCollectionsNotFound>Style Collections Not Found!</StyleCollectionsNotFound>
    <title>Style Designer</title>
  </FormStyleDesigner>
  <FormSystemTextEditor>
    <Condition>Condition</Condition>
    <LabelDataBand>Data Band:</LabelDataBand>
    <LabelDataColumn>Data Column:</LabelDataColumn>
    <LabelShowInsteadNullValues>Show Instead Null Values:</LabelShowInsteadNullValues>
    <LabelSummaryFunction>Summary Function:</LabelSummaryFunction>
    <pageExpression>Expression</pageExpression>
    <pageSummary>Summary</pageSummary>
    <pageSystemVariable>System Variable</pageSystemVariable>
    <RunningTotal>Running Total</RunningTotal>
    <SummaryRunning>Summary Running</SummaryRunning>
    <SummaryRunningByColumn>Column</SummaryRunningByColumn>
    <SummaryRunningByPage>Page</SummaryRunningByPage>
    <SummaryRunningByReport>Report</SummaryRunningByReport>
  </FormSystemTextEditor>
  <FormTitles>
    <ChartWizardForm>Chart Wizard</ChartWizardForm>
    <CodeEditorForm>Code Editor</CodeEditorForm>
    <ConditionEditorForm>Condition</ConditionEditorForm>
    <ConnectionSelectForm>Select Type of Connection</ConnectionSelectForm>
    <ContainerSelectForm>Select Container</ContainerSelectForm>
    <DataAdapterServiceSelectForm>Select Type of Data</DataAdapterServiceSelectForm>
    <DataRelationSelectForm>Select Data Relation</DataRelationSelectForm>
    <DataSetName>Enter DataSet Name</DataSetName>
    <DataSourceSelectForm>Select Data Source</DataSourceSelectForm>
    <DataSourcesNewForm>New Data Sources</DataSourcesNewForm>
    <DataStoreViewerForm>Data Store Viewer</DataStoreViewerForm>
    <DesignerApplication>Report Designer</DesignerApplication>
    <EventEditorForm>Event Editor</EventEditorForm>
    <ExpressionEditorForm>Expression Editor</ExpressionEditorForm>
    <GroupConditionForm>Group</GroupConditionForm>
    <InteractionDrillDownPageSelectForm>Select Drill-Down Page</InteractionDrillDownPageSelectForm>
    <MasterComponentSelectForm>Select Master Component</MasterComponentSelectForm>
    <PageAddForm>Add Page</PageAddForm>
    <PageSizeForm>Page Size</PageSizeForm>
    <PagesManagerForm>Pages Manager</PagesManagerForm>
    <PromptForm>Enter information to logon on to the database</PromptForm>
    <ReportWizard>Report Wizard</ReportWizard>
    <ServiceSelectForm>Select Service</ServiceSelectForm>
    <SqlExpressionsForm>SQL Expressions</SqlExpressionsForm>
    <SubReportPageSelectForm>Select Sub-Report Page</SubReportPageSelectForm>
    <TextEditorForm>Text Editor</TextEditorForm>
    <ViewDataForm>View Data</ViewDataForm>
    <ViewerApplication>Report Viewer</ViewerApplication>
  </FormTitles>
  <FormViewer>
    <Bookmarks>Bookmarks</Bookmarks>
    <Close>Close</Close>
    <CollapseAll>Collapse All</CollapseAll>
    <CompressedDocumentFile>Compressed Document File</CompressedDocumentFile>
    <ContextMenu>Context Menu</ContextMenu>
    <DocumentFile>Document File...</DocumentFile>
    <Editor>Editor</Editor>
    <EncryptedDocumentFile>Encrypted Document File</EncryptedDocumentFile>
    <ExpandAll>Expand All</ExpandAll>
    <Export>Export...</Export>
    <Find>Find</Find>
    <FirstPage>First Page</FirstPage>
    <FullScreen>Full Screen</FullScreen>
    <GoToPage>Go To Page</GoToPage>
    <HorScrollBar>Horizontal Scroll Bar</HorScrollBar>
    <LabelPageN>Page:</LabelPageN>
    <LastPage>Last Page</LastPage>
    <NextPage>Next Page</NextPage>
    <Open>Open...</Open>
    <PageControl>Page Control</PageControl>
    <PageDelete>Delete Page</PageDelete>
    <PageDesign>Edit Page...</PageDesign>
    <PageNew>New Page</PageNew>
    <PageNofM>Page {0} of {1}</PageNofM>
    <PageofM>of {0}</PageofM>
    <PageSize>Page Size...</PageSize>
    <PageViewModeContinuous>Continuous</PageViewModeContinuous>
    <PageViewModeMultiplePages>Multiple Pages</PageViewModeMultiplePages>
    <PageViewModeSinglePage>Single Page</PageViewModeSinglePage>
    <Parameters>Parameters</Parameters>
    <PrevPage>Previous Page</PrevPage>
    <Print>Print...</Print>
    <qnPageDelete>Do you want to delete page?</qnPageDelete>
    <Save>Save...</Save>
    <SendEMail>Send Email...</SendEMail>
    <StatusBar>Status Bar</StatusBar>
    <Thumbnails>Thumbnails</Thumbnails>
    <title>Viewer</title>
    <titlePageSettings>Page Settings</titlePageSettings>
    <Toolbar>Tool bar</Toolbar>
    <VerScrollBar>Vertical Scroll Bar</VerScrollBar>
    <ViewMode>View Mode</ViewMode>
    <Zoom>Zoom</Zoom>
    <ZoomMultiplePages>Multiple Pages</ZoomMultiplePages>
    <ZoomOnePage>One Page</ZoomOnePage>
    <ZoomPageWidth>Page Width</ZoomPageWidth>
    <ZoomTwoPages>Two Pages</ZoomTwoPages>
    <ZoomXXPages>{0} X {1} Pages</ZoomXXPages>
    <ZoomXXPagesCancel>Cancel</ZoomXXPagesCancel>
  </FormViewer>
  <FormViewerFind>
    <Close>Close</Close>
    <FindNext>Find Next</FindNext>
    <FindPrevious>Find Previous</FindPrevious>
    <FindWhat>Find What:</FindWhat>
  </FormViewerFind>
  <Gauge>
    <AddNewItem>Add New Item</AddNewItem>
    <BarRangeList>Bar Range List</BarRangeList>
    <GaugeEditorForm>Gauge Editor</GaugeEditorForm>
    <Kind>Kind</Kind>
    <LinearBar>Linear Bar</LinearBar>
    <LinearMarker>Linear Marker</LinearMarker>
    <LinearRange>Linear Range</LinearRange>
    <LinearRangeList>Linear Range List</LinearRangeList>
    <LinearScale>Linear Scale</LinearScale>
    <LinearTickLabelCustom>Linear Tick Label Custom</LinearTickLabelCustom>
    <LinearTickLabelMajor>Linear Tick Label Major</LinearTickLabelMajor>
    <LinearTickLabelMinor>Linear Tick Label Minor</LinearTickLabelMinor>
    <LinearTickMarkCustom>Linear Tick Mark Custom</LinearTickMarkCustom>
    <LinearTickMarkMajor>Linear Tick Mark Major</LinearTickMarkMajor>
    <LinearTickMarkMinor>Linear Tick Mark Minor</LinearTickMarkMinor>
    <Needle>Needle</Needle>
    <RadialBar>Radial Bar</RadialBar>
    <RadialMarker>Radial Marker</RadialMarker>
    <RadialRange>Radial Range</RadialRange>
    <RadialRangeList>Radial Range List</RadialRangeList>
    <RadialScale>Radial Scale</RadialScale>
    <RadialTickLabelCustom>Radial Tick Label Custom</RadialTickLabelCustom>
    <RadialTickLabelMajor>Radial Tick Label Major</RadialTickLabelMajor>
    <RadialTickLabelMinor>Radial Tick Label Minor</RadialTickLabelMinor>
    <RadialTickMarkCustom>Radial Tick Mark Custom</RadialTickMarkCustom>
    <RadialTickMarkMajor>Radial Tick Mark Major</RadialTickMarkMajor>
    <RadialTickMarkMinor>Radial Tick Mark Minor</RadialTickMarkMinor>
    <StateIndicator>State Indicator</StateIndicator>
    <StateIndicatorFilter>State Indicator Filter</StateIndicatorFilter>
    <TickCustomValue>Tick Custom Value</TickCustomValue>
  </Gauge>
  <Gui>
    <AccentColor>Accent Color</AccentColor>
    <barname_cancel>Cancel</barname_cancel>
    <barname_caption>New Toolbar</barname_caption>
    <barname_msginvalidname>Toolbar name cannot be empty.</barname_msginvalidname>
    <barname_name>&amp;Toolbar Name:</barname_name>
    <barname_ok>OK</barname_ok>
    <barrename_caption>Rename Toolbar</barrename_caption>
    <barsys_autohide_tooltip>Auto-Hide</barsys_autohide_tooltip>
    <barsys_close_tooltip>Close</barsys_close_tooltip>
    <barsys_customize_tooltip>Customize</barsys_customize_tooltip>
    <ClickAndDragPickColor>Click and Drag to pick color</ClickAndDragPickColor>
    <colorpicker_morecolors>&amp;More Colors...</colorpicker_morecolors>
    <colorpicker_nofill>&amp;No Fill</colorpicker_nofill>
    <colorpicker_standardcolorslabel>Standard Colors</colorpicker_standardcolorslabel>
    <colorpicker_themecolorslabel>Theme Colors</colorpicker_themecolorslabel>
    <colorpickerdialog_alphalabel>&amp;Alpha:</colorpickerdialog_alphalabel>
    <colorpickerdialog_bluelabel>&amp;Blue:</colorpickerdialog_bluelabel>
    <colorpickerdialog_cancelbutton>Cancel</colorpickerdialog_cancelbutton>
    <colorpickerdialog_caption>Colors</colorpickerdialog_caption>
    <colorpickerdialog_colormodellabel>Color Model:</colorpickerdialog_colormodellabel>
    <colorpickerdialog_currentcolorlabel>Current</colorpickerdialog_currentcolorlabel>
    <colorpickerdialog_customcolorslabel>Colors:</colorpickerdialog_customcolorslabel>
    <colorpickerdialog_greenlabel>&amp;Green:</colorpickerdialog_greenlabel>
    <colorpickerdialog_newcolorlabel>New</colorpickerdialog_newcolorlabel>
    <colorpickerdialog_okbutton>OK</colorpickerdialog_okbutton>
    <colorpickerdialog_redlabel>&amp;Red:</colorpickerdialog_redlabel>
    <colorpickerdialog_rgblabel>RGB</colorpickerdialog_rgblabel>
    <colorpickerdialog_standardcolorslabel>Colors:</colorpickerdialog_standardcolorslabel>
    <colorpickerdialog_tabcustom>Custom</colorpickerdialog_tabcustom>
    <colorpickerdialog_tabstandard>Standard</colorpickerdialog_tabstandard>
    <cust_btn_close>Close</cust_btn_close>
    <cust_btn_delete>Delete</cust_btn_delete>
    <cust_btn_keyboard>&amp;Keyboard...</cust_btn_keyboard>
    <cust_btn_new>&amp;New...</cust_btn_new>
    <cust_btn_rename>&amp;Rename...</cust_btn_rename>
    <cust_btn_reset>&amp;Reset...</cust_btn_reset>
    <cust_btn_resetusage>&amp;Reset my usage data</cust_btn_resetusage>
    <cust_caption>Customize</cust_caption>
    <cust_cbo_fade>Fade</cust_cbo_fade>
    <cust_cbo_none>(None)</cust_cbo_none>
    <cust_cbo_random>Random</cust_cbo_random>
    <cust_cbo_slide>Slide</cust_cbo_slide>
    <cust_cbo_system>System Default</cust_cbo_system>
    <cust_cbo_unfold>Unfold</cust_cbo_unfold>
    <cust_chk_delay>Show full menus after a short delay</cust_chk_delay>
    <cust_chk_fullmenus>Always show full menus</cust_chk_fullmenus>
    <cust_chk_showsk>Show &amp;Shortcut Keys in ScreenTips</cust_chk_showsk>
    <cust_chk_showst>Show Screen&amp;Tips on Toolbars</cust_chk_showst>
    <cust_lbl_cats>Cate&amp;gories:</cust_lbl_cats>
    <cust_lbl_cmds>Comman&amp;ds:</cust_lbl_cmds>
    <cust_lbl_cmdsins>To add a command to Bar select the category and drag the command out of this box to a Bar.</cust_lbl_cmdsins>
    <cust_lbl_menuan>Menu Animation:</cust_lbl_menuan>
    <cust_lbl_other>Other:</cust_lbl_other>
    <cust_lbl_pmt>Personalized Menus and Toolbars</cust_lbl_pmt>
    <cust_lbl_tlbs>Toolb&amp;ars:</cust_lbl_tlbs>
    <cust_mnu_addremove>&amp;Add or Remove Buttons</cust_mnu_addremove>
    <cust_mnu_cust>Customize...</cust_mnu_cust>
    <cust_mnu_reset>Reset Bar</cust_mnu_reset>
    <cust_mnu_tooltip>Bar Options</cust_mnu_tooltip>
    <cust_msg_delete>Are you sure you want to delete the &lt;barname&gt; toolbar?</cust_msg_delete>
    <cust_pm_begingroup>Begin Group</cust_pm_begingroup>
    <cust_pm_delete>Delete</cust_pm_delete>
    <cust_pm_name>Name:</cust_pm_name>
    <cust_pm_reset>Reset</cust_pm_reset>
    <cust_pm_stydef>Default Style</cust_pm_stydef>
    <cust_pm_styimagetext>Image and Text (Always)</cust_pm_styimagetext>
    <cust_pm_stytextonly>Text Only (Always)</cust_pm_stytextonly>
    <cust_tab_commands>Commands</cust_tab_commands>
    <cust_tab_options>Options</cust_tab_options>
    <cust_tab_toolbar_alignment>Toolbar Alignment</cust_tab_toolbar_alignment>
    <cust_tab_toolbars>Toolbars</cust_tab_toolbars>
    <mdisysmenu_close>Close</mdisysmenu_close>
    <mdisysmenu_maximize>Maximize</mdisysmenu_maximize>
    <mdisysmenu_minimize>Minimize</mdisysmenu_minimize>
    <mdisysmenu_move>Move</mdisysmenu_move>
    <mdisysmenu_next>Next</mdisysmenu_next>
    <mdisysmenu_restore>Restore</mdisysmenu_restore>
    <mdisysmenu_size>Size</mdisysmenu_size>
    <mdisystt_close>Close</mdisystt_close>
    <mdisystt_minimize>Minimize</mdisystt_minimize>
    <mdisystt_restore>Restore</mdisystt_restore>
    <monthcalendar_clearbutton>Clear</monthcalendar_clearbutton>
    <monthcalendar_todaybutton>Today</monthcalendar_todaybutton>
    <navbar_navpaneoptions>Na&amp;vigation Pane Options...</navbar_navpaneoptions>
    <navbar_showfewerbuttons>Show &amp;Fewer Buttons</navbar_showfewerbuttons>
    <navbar_showmorebuttons>Show &amp;More Buttons</navbar_showmorebuttons>
    <navPaneCollapseTooltip>Collapse the Navigation Pane</navPaneCollapseTooltip>
    <navPaneExpandTooltip>Expand the Navigation Pane</navPaneExpandTooltip>
    <sys_custombar>Custom Bar</sys_custombar>
    <sys_morebuttons>More Buttons</sys_morebuttons>
  </Gui>
  <HelpComponents>
    <StiBarCode>This component allows showing bar codes in a report. Bar code data are sent to an object as a string. The string may contain any symbols but only allowed symbols for the selected chart can be displayed.</StiBarCode>
    <StiButtonElement>This element is used to execute an event script when a user is interacting.</StiButtonElement>
    <StiCardsElement>This element is used to display grouped data as cards.</StiCardsElement>
    <StiChart>This component allows showing charts in a report. Different types of charts are available. Among them are bar, line, pie, doughnut, lines, areas, Gantt, scatter charts etc. </StiChart>
    <StiChartElement>This element allows showing charts in a dashboard. Different types of charts are available.</StiChartElement>
    <StiCheckBox>This component allows showing a checkbox in a report. It can display two modes: "enable" or "disable".</StiCheckBox>
    <StiChildBand>The Child band can be used to output two bands on one data row.</StiChildBand>
    <StiClone>This component is used to clone parts of a report into a required part of a report. Cloning can be applied only to the panel contents.</StiClone>
    <StiColumnFooterBand>This band is used to output footers of columns on the Data band. The Column Footer band is output once under each column. All components which are placed on this band will also be output under each column.</StiColumnFooterBand>
    <StiColumnHeaderBand>This band is used to output column headers. The Column Header band is used to output only once. All components which are placed on the band can be output above each column.</StiColumnHeaderBand>
    <StiComboBoxElement>Represents the ComboBox that is used to either type a value directly into the control or choose from the list of existing options.</StiComboBoxElement>
    <StiContainer>This is the rectangular region where other components, including bands, can be placed. When moving the container the components in it will be moved too. The container can be placed both on a band and on a page.</StiContainer>
    <StiCrossDataBand>This band is connected to the data source. It is output as many times as there are rows in the data source. A Cross band is output from left to right.</StiCrossDataBand>
    <StiCrossFooterBand>This band is used to output footers of the Cross-Data band. The band is output once after all rows of the Data band. A Cross band is output from left to right.</StiCrossFooterBand>
    <StiCrossGroupFooterBand>This band is used to output footers of a group. It is placed under the Cross-Data band. Each Cross-Footer belongs to the specified Cross-Header band. A Cross band is output from left to right.</StiCrossGroupFooterBand>
    <StiCrossGroupHeaderBand>This band is the basic one for the report rendering with grouping, when Cross-Data are used. It is impossible to create grouping without this band. The components for showing information by the group are placed on the group header. The information can be group name, date, condition of grouping etc. The band is output in the beginning of each group. A Cross band is output from left to right.</StiCrossGroupHeaderBand>
    <StiCrossHeaderBand>This band is used to output headers. It is used in association with the Cross band. A Cross band is output from left to right.</StiCrossHeaderBand>
    <StiCrossTab>The cross-tab component is used for the structured data representation as a table.</StiCrossTab>
    <StiDataBand>The Data band is connected to the data source and is output as many times as there are rows in the data source.</StiDataBand>
    <StiDatePickerElement>The element is used to select the date and time.</StiDatePickerElement>
    <StiElectronicSignature>This component is used to add a graphic signature, image, initials, and text.</StiElectronicSignature>
    <StiEmptyBand>This band is used to fill free space on the bottom of a page. This band fills free space on each page of a report.</StiEmptyBand>
    <StiFilterCategory>A group of elements used for selecting and filtering values.</StiFilterCategory>
    <StiFooterBand>This band is used to output summary by the Data band. It is placed under the Data band and is output once after all data rows which Data band outputs.</StiFooterBand>
    <StiGauge>The component allows you to display a value within a predefined range. It can have different graphical representation.</StiGauge>
    <StiGaugeElement>The element allows you to display a value within a predefined range. It can have different graphical representation.</StiGaugeElement>
    <StiGroupFooterBand>The Group Footer band is used to output the group footer. This band is placed after the Data band. And this is the Data band with what the Group Header band is bound. Each Group Footer band belongs to the specified Group Header band. The Group Footer band will not be output without the Group Header band.</StiGroupFooterBand>
    <StiGroupHeaderBand>This band is the basic band for rendering reports with grouping. The components for showing information by this group are placed on the group header. It can be group name, date, condition of grouping etc. This band is output once in the beginning of each group.</StiGroupHeaderBand>
    <StiHeaderBand>This band is used to output headers. It is used together with the data band.</StiHeaderBand>
    <StiHierarchicalBand>This band is connected to the data source and output as many times as there are rows in the data source. Data are output as a tree.</StiHierarchicalBand>
    <StiHorizontalLinePrimitive>This component is used to output a line.</StiHorizontalLinePrimitive>
    <StiImage>This component is used to output images.</StiImage>
    <StiImageElement>This element is used to output images. It supports the following graphic formats, such as BMP, JPG, JPEG, GIF, TIFF, PNG, ICO, WMF, EMF, SVG.</StiImageElement>
    <StiIndicatorElement>The element displays the difference in values between the input data.</StiIndicatorElement>
    <StiListBoxElement>The element allows selecting items from a list.</StiListBoxElement>
    <StiMap>This component is used to display data that is geographically targeted.</StiMap>
    <StiMapCategory>Group of elements for working with maps and data.</StiMapCategory>
    <StiMapElement>This element is used to display data that is geographically targeted.</StiMapElement>
    <StiMathFormula>This component is used to display math formulas in a report.</StiMathFormula>
    <StiNumberBoxElement>This element is used to select a numeric value or set a range of numeric values.</StiNumberBoxElement>
    <StiOnlineMapElement>This element is used to display geographic markers on the online map.</StiOnlineMapElement>
    <StiOverlayBand>This band is used to output watermarks on a page.</StiOverlayBand>
    <StiPageFooterBand>This band is used to output the information on the bottom of each page such as page numbers, dates, and other additional information.</StiPageFooterBand>
    <StiPageHeaderBand>This band is used to output the page header such as page numbers, dates, and other additional information. It is output on the top of each page.</StiPageHeaderBand>
    <StiPanel>A rectangular region that can contain other components, including bands. When the panel is moved the components in it are moved too. A panel can be placed either on a band or on a page.</StiPanel>
    <StiPanelElement>A rectangular region that can contain other elements. When the panel is moved the elements in it are moved too.</StiPanelElement>
    <StiPdfDigitalSignature>This interactive component is used to specify a place for the digital signature after the report has been exported to Adobe PDF.</StiPdfDigitalSignature>
    <StiPdfDigitalSignatureElement>This component is used to specify a space for the digital signature and fields from the certificate when exporting to PDF.</StiPdfDigitalSignatureElement>
    <StiPivotTableElement>The pivot table element is used for the structured data representation as a table.</StiPivotTableElement>
    <StiProgressElement>The element displays the ratio of the input data. It can be presented in different modes - a pie, donut, bars.</StiProgressElement>
    <StiRectanglePrimitive>This component is used to output rectangles.</StiRectanglePrimitive>
    <StiRegionMapElement>This element is used to display data that is geographically targeted.</StiRegionMapElement>
    <StiReportSummaryBand>This band is used to output summaries through the entire report. It is output once in the end of a report.</StiReportSummaryBand>
    <StiReportTitleBand>This band is used to output the report title. It is output in the beginning of a report.</StiReportTitleBand>
    <StiRichText>This component is used to output and edit the RTF text. It also supports loading and saving files in the RTF format.</StiRichText>
    <StiRoundedRectanglePrimitive>This component is used to output rounded rectangles.</StiRoundedRectanglePrimitive>
    <StiShape>This component is used to insert ready-made shapes, such as arrows, diagonal line down, diagonal line up, horizontal line, left and right lines, oval, rectangle, rounded rectangle, top and bottom lines, triangle, vertical line.</StiShape>
    <StiShapeElement>This element is used to insert ready-made shapes, such as arrows, diagonal line down, diagonal line up, horizontal line, left and right lines, oval, rectangle, rounded rectangle, top and bottom lines, triangle, vertical line.</StiShapeElement>
    <StiSignature>This component is used to add an electronic or digital signature to a report.</StiSignature>
    <StiSparkline>This component is used to create micrographics in a report. It allows you to track dynamically changing data as bars, lines, areas.</StiSparkline>
    <StiSubReport>This component is used to output additional data in different places of a report.</StiSubReport>
    <StiTable>This component is a set of data elements that is organized using a model of vertical columns and horizontal rows.</StiTable>
    <StiTableElement>This element is a set of data elements that is organized using a model of vertical columns and horizontal rows.</StiTableElement>
    <StiTableOfContents>This component is used to create a table of contents in a report.</StiTableOfContents>
    <StiText>This component is a basic object to output data as a text.</StiText>
    <StiTextElement>This element is a basic object to output data as a text.</StiTextElement>
    <StiTextInCells>The component is used to output a text in cells. It is frequently used for creating forms.</StiTextInCells>
    <StiTreeViewBoxElement>The element is used to select values with a drop-down list as a tree.</StiTreeViewBoxElement>
    <StiTreeViewElement>Represents the element that is used to show the hierarchical data as a tree.</StiTreeViewElement>
    <StiVerticalLinePrimitive>This component is used to output vertical lines.</StiVerticalLinePrimitive>
    <StiWebContentElement>The component allows to add web-content to dashboards.</StiWebContentElement>
    <StiWinControl>The component allows showing visual controls from .NET Framework.</StiWinControl>
    <StiZipCode>This component is used to output the ZIP code.</StiZipCode>
  </HelpComponents>
  <HelpDesigner>
    <ActiveRelation>Sets the priority Relation to bind two tables. It is used in Data Transformation and Dashboards. Only one Relation in each Data Source can be active.</ActiveRelation>
    <Align>Change the location of selected components.</Align>
    <AlignBottom>Align the contents of a component to bottom.</AlignBottom>
    <AlignCenter>Align the contents of a component to center.</AlignCenter>
    <AlignComponentBottom>Allows you to align objects horizontally along the bottom edge of selected components.</AlignComponentBottom>
    <AlignComponentCenter>Allows you to align objects horizontally along the center edge of selected components.</AlignComponentCenter>
    <AlignComponentLeft>Allows you to align objects vertically along the left edge of selected components.</AlignComponentLeft>
    <AlignComponentMiddle>Allows you to align objects vertically along the center edge of selected components.</AlignComponentMiddle>
    <AlignComponentRight>Allows you to align objects vertically along the right edge of selected components.</AlignComponentRight>
    <AlignComponentTop>Allows you to align objects horizontally along the top edge of selected components.</AlignComponentTop>
    <AlignLeft>Align the contents of a component to left.</AlignLeft>
    <AlignMiddle>Center the contents by the top and bottom borders of a component.</AlignMiddle>
    <AlignRight>Align contents of a component to right.</AlignRight>
    <AlignToGrid>Align the selected components to grid nodes.</AlignToGrid>
    <AlignTop>Align the contents of a component to top.</AlignTop>
    <AlignWidth>Justify the contents of a component.</AlignWidth>
    <Angle>Rotate the contents of the component.</Angle>
    <AngleWatermark>The watermark text rotation angle.</AngleWatermark>
    <Background>Change the background of the selected components.</Background>
    <biConditions>Control list of conditions of the selected components.</biConditions>
    <BorderColor>Select the border color of the selected component.</BorderColor>
    <BorderSidesAll>Switch on all borders sides of the selected components.</BorderSidesAll>
    <BorderSidesBottom>Switch on bottom border side of the selected components.</BorderSidesBottom>
    <BorderSidesLeft>Switch on left border side of the selected components.</BorderSidesLeft>
    <BorderSidesNone>Switch off all borders sides of the selected components.</BorderSidesNone>
    <BorderSidesRight>Switch on right border side of the selected components.</BorderSidesRight>
    <BorderSidesTop>Switch on top border side of the selected components.</BorderSidesTop>
    <BorderStyle>Select the border style of the selected components.</BorderStyle>
    <BringToFront>Bring the selected component to front.</BringToFront>
    <CenterHorizontally>Allows you to align a component horizontally relative to container edges in what it is placed.</CenterHorizontally>
    <CenterVertically>Allows you to align a component vertically relative to container edges in what it is placed.</CenterVertically>
    <Close>Close the Report Designer.</Close>
    <Columns>Split a page into columns.</Columns>
    <CompilationAccess1>This document uses the compilation process. If the document is from a trusted source, you can click the "Open" button to access it.</CompilationAccess1>
    <CompilationAccess2>If you're unsure about the document's source but still want to view it, you can click the "Open in safe mode" button. In this case, you will use interpretation instead of compilation, which enables you to run the document safely.</CompilationAccess2>
    <CompilationAccess3>This document uses the compilation process and it can be unsafe. You can't open it.</CompilationAccess3>
    <CompilationAccessAllow>Allow opening documents with compilation without any warnings or restrictions.</CompilationAccessAllow>
    <CompilationAccessAsk>If a document uses the compilation process, a warning window with predefined actions will be displayed.</CompilationAccessAsk>
    <CompilationAccessDeny>Deny opening any document that uses the compilation process. A special window will be displayed.</CompilationAccessDeny>
    <CompilationAccessForceInterpretation>Documents with compilation will be opened without any prompt but will be automatically converted to interpretation mode, which does not use the compilation process.</CompilationAccessForceInterpretation>
    <CompilationModeEnabling>You are switching to the compilation mode. This mode allows to run different code and sometimes it could be harmful. Please, use this mode only if you understand all the risks and agree with them. Click the {0} button to proceed.</CompilationModeEnabling>
    <ComponentSize>Change the size of the selected components.</ComponentSize>
    <CopyStyle>Copy Style</CopyStyle>
    <CopyToClipboard>Copy to Clipboard</CopyToClipboard>
    <CurrencySymbol>Select the currency symbol.</CurrencySymbol>
    <DashboardNew>Create a new dashboard.</DashboardNew>
    <DataStore>Show data, registered in a report.</DataStore>
    <DateTimeFormat>Select the date and time format for the selected components.</DateTimeFormat>
    <DockingPanels>Panel settings.</DockingPanels>
    <DockStyleBottom>Dock selected components to the bottom side.</DockStyleBottom>
    <DockStyleFill>Dock selected components to all sides.</DockStyleFill>
    <DockStyleLeft>Dock selected components to the left side.</DockStyleLeft>
    <DockStyleNone>Undock selected components.</DockStyleNone>
    <DockStyleRight>Dock selected components to the right side.</DockStyleRight>
    <DockStyleTop>Dock selected components to the top side.</DockStyleTop>
    <EventsAccess1>This document uses Events. If the document is from a trusted source, you can click the "Open" button to access it.</EventsAccess1>
    <EventsAccess2>If you're unsure about the document's source but still want to view it, you can click the "Open in safe mode" button. In this case, events will be ignored and not executed.</EventsAccess2>
    <FontGrow>Make the text size larger.</FontGrow>
    <FontName>The text font.</FontName>
    <FontNameWatermark>The watermark text font.</FontNameWatermark>
    <FontShrink>Make the text size smaller.</FontShrink>
    <FontSize>Font size.</FontSize>
    <FontSizeWatermark>Change the font size.</FontSizeWatermark>
    <FontStyleBold>Make the text bold.</FontStyleBold>
    <FontStyleBoldWatermark>Make the text of the watermark bold.</FontStyleBoldWatermark>
    <FontStyleItalic>Make the text Italic.</FontStyleItalic>
    <FontStyleItalicWatermark>Make the watermark text Italic.</FontStyleItalicWatermark>
    <FontStyleStrikeout>Make the text strikeout.</FontStyleStrikeout>
    <FontStyleUnderline>Make the text underlined.</FontStyleUnderline>
    <FontStyleUnderlineWatermark>Make the watermark text underlined.</FontStyleUnderlineWatermark>
    <FormatBoolean>This format is used to format values of the boolean type.</FormatBoolean>
    <FormatCurrency>Display a value as currency. It allows you to display a number with the default currency symbol.</FormatCurrency>
    <FormatCustom>This type is used to show values according to custom requirements. This type allows data formatting in the Format Mask.</FormatCustom>
    <FormatDate>Display a value as date. The date format is based on the regional date settings.</FormatDate>
    <FormatGeneral>Display a value without specific format.</FormatGeneral>
    <FormatNumber>It is used for general display of numbers.</FormatNumber>
    <FormatPercentage>Display a value as percentage. Numbers are multiplied by 100 to convert them to percentages.</FormatPercentage>
    <FormatTime>Display a value as time. The time format is based on the regional time settings.</FormatTime>
    <FormNew>Create a new dialog form.</FormNew>
    <GridMode>Show grid in lines or dots.</GridMode>
    <ImageAlignment>Put a watermark image on a page.</ImageAlignment>
    <ImageTransparency>Change the transparency of the watermark image.</ImageTransparency>
    <Interaction>Control an interaction of the selected components.</Interaction>
    <LineSpacing>Choose how much space appears between lines of text or between paragraphs.</LineSpacing>
    <Link>Link the component to the current container.</Link>
    <LoadImage>Load watermark images from the file.</LoadImage>
    <Lock>Lock the component. The component cannot be moved and resized.</Lock>
    <MainMenu>Click here to see the list of possible operations with a report, including opening, closing, and previewing.</MainMenu>
    <MakeHorizontalSpacingEqual>Allows you to set equal horizontal spacing between selected components.</MakeHorizontalSpacingEqual>
    <MakeVerticalSpacingEqual>Allows you to set equal vertical spacing between selected components.</MakeVerticalSpacingEqual>
    <Margins>Select the margins of the current page.</Margins>
    <menuCheckIssues>Check to find errors, warnings and get recommendations.</menuCheckIssues>
    <menuDesignerOptions>Setup report designer options.</menuDesignerOptions>
    <menuEditClearContents>Clear the contents.</menuEditClearContents>
    <menuEditCopy>Copy the selected components and put them on the Clipboard.</menuEditCopy>
    <menuEditCut>Cut the selected components from a report and put them on the Clipboard.</menuEditCut>
    <menuEditDelete>Delete selected components.</menuEditDelete>
    <menuEditPaste>Paste the contents of the Clipboard into report.</menuEditPaste>
    <menuFAQPage>Go to the web page with frequently asked questions.</menuFAQPage>
    <menuGlobalizationStrings>Call the Globalization Strings editor of the current report.</menuGlobalizationStrings>
    <menuHelpAboutProgramm>Get information about the version of report generator and the version of .NET Framework.</menuHelpAboutProgramm>
    <menuHomePage>Go to the Home page of the product.</menuHomePage>
    <menuPageOptions>Setup the basic parameters of the current page. All page options can be changed using the Property panel.</menuPageOptions>
    <menuPagesManager>Run the Pages Manager. It allows moving pages, deleting pages, and creating new pages.</menuPagesManager>
    <menuPreviewSettings>Call the Preview Settings editor of the current report. Settings will be applied only when viewing the current report.</menuPreviewSettings>
    <menuPrint>Select a printer, number of copies, and other printing options before printing.</menuPrint>
    <menuPrintPreview>Preview a report before printing.</menuPrintPreview>
    <menuPrintQuick>Print a report directly to the default printer. The printing dialog is not displayed.</menuPrintQuick>
    <menuReportOptions>Setup the basic report options. All options can be changed using the Property panel.</menuReportOptions>
    <menuStyleDesigner>Call the Style Designer of the current report.</menuStyleDesigner>
    <menuSupport>Go to the support page to ask a question.</menuSupport>
    <menuViewAlignToGrid>Align components to grid.</menuViewAlignToGrid>
    <menuViewNormal>Normal view of a page.</menuViewNormal>
    <menuViewPageBreakPreview>The mode of showing a page with borders of segments.</menuViewPageBreakPreview>
    <menuViewQuickInfo>Show quick information of components: component name, alias, contents, events etc.</menuViewQuickInfo>
    <menuViewShowGrid>Turn on grid lines to which you can align objects in a report.</menuViewShowGrid>
    <menuViewShowHeaders>Show headers of bands.</menuViewShowHeaders>
    <menuViewShowOrder>Show order of components on a page.</menuViewShowOrder>
    <menuViewShowRulers>View the rulers, used to measure and line up objects on a page.</menuViewShowRulers>
    <menuViewShowSmartGuides>Display Smart Guides to position components relative to each other.</menuViewShowSmartGuides>
    <MoveBackward>Move a component to one level higher in order of placing components on a page.</MoveBackward>
    <MoveForward>Move a component to one level lower in order of placing components on a page.</MoveForward>
    <NotPossibleEnableCompilationMode>It is impossible to enable compilation mode for the report. This is prohibited by the 'Compilation Access' option in the designer settings.</NotPossibleEnableCompilationMode>
    <Orientation>Switch the pages between portrait and landscape layouts.</Orientation>
    <PageDelete>Delete current page from a report.</PageDelete>
    <PageNew>Create a new page.</PageNew>
    <PageSetup>Show the Page Setup dialog box.</PageSetup>
    <PageSize>Choose the paper size for the current page of a report.</PageSize>
    <PagesManager>Run the Pages Manager.</PagesManager>
    <PressF1>Press F1 for More Details</PressF1>
    <Redo>Redo the previously canceled change in a report.</Redo>
    <ReportNew>Create a new report.</ReportNew>
    <ReportOpen>Open a report in the designer.</ReportOpen>
    <ReportPreview>Preview an edited report in the viewer.</ReportPreview>
    <ReportSave>Save currently edited report.</ReportSave>
    <SelectAll>Select all components on the current page.</SelectAll>
    <SelectUILanguage>Select the UI language.</SelectUILanguage>
    <SendToBack>Move the selected component to back relative to other components.</SendToBack>
    <ServicesConfigurator>Run the Services Configurator.</ServicesConfigurator>
    <Shadow>Show the shadow of a component.</Shadow>
    <ShowBehind>Show the watermark text behind the components.</ShowBehind>
    <ShowImageBehind>Put the watermark image behind all components on a page.</ShowImageBehind>
    <ShowToolbox>Show the toolbox.</ShowToolbox>
    <StimulsoftHelp>Stimulsoft Help</StimulsoftHelp>
    <StyleDesigner>Run Style Designer.</StyleDesigner>
    <TellMeMore>Tell me more</TellMeMore>
    <Text>Watermark text.</Text>
    <TextBrush>Change the text color.</TextBrush>
    <TextBrushWatermark>Change the text color of the watermark.</TextBrushWatermark>
    <TextColor>Change the text color.</TextColor>
    <TextFormat>Select the format of values.</TextFormat>
    <ToolbarStyle>Set selected style to all selected components.</ToolbarStyle>
    <Undo>Undo the latest change in a report.</Undo>
    <WordWrap>Wrap the text of a component.</WordWrap>
    <Zoom>Specify the zoom level of the report.</Zoom>
  </HelpDesigner>
  <HelpDialogs>
    <StiButtonControl>Represents the Button control.</StiButtonControl>
    <StiCheckBoxControl>Represents the Flag control.</StiCheckBoxControl>
    <StiCheckedListBoxControl>Shows the List object, in what a flag on the left is shown for each elements.</StiCheckedListBoxControl>
    <StiComboBoxControl>Represents the ComboBox that is used to either type a value directly into the control or choose from the list of existing options.</StiComboBoxControl>
    <StiDateTimePickerControl>Represents the control that allows selecting the date and time. It also allows you to output the date and time in specified format.</StiDateTimePickerControl>
    <StiGridControl>Represents the Grid control that consist of rows and columns.</StiGridControl>
    <StiGroupBoxControl>Represents the control that creates a container with borders and a header for the UI content.</StiGroupBoxControl>
    <StiLabelControl>A label is an user interface control which displays text on a form. It is usually a static control having no interactivity.</StiLabelControl>
    <StiListBoxControl>Contains the list of elements for selection.</StiListBoxControl>
    <StiListViewControl>Represents the ListView control that shows the list of data elements.</StiListViewControl>
    <StiLookUpBoxControl>Contains the list of elements for selection.</StiLookUpBoxControl>
    <StiNumericUpDownControl>Represents the control that shows numeric values.</StiNumericUpDownControl>
    <StiPanelControl>The Panel elements are used for placing and arrangement objects.</StiPanelControl>
    <StiPictureBoxControl>Represents the control for showing an image.</StiPictureBoxControl>
    <StiRadioButtonControl>Represents the Radio button control that allows the user to choose only one of a predefined set of options. </StiRadioButtonControl>
    <StiRichTextBoxControl>Represents the RichText control with widen editing.</StiRichTextBoxControl>
    <StiTextBoxControl>Represents the Text control that is used for showing or editing a text.</StiTextBoxControl>
    <StiTreeViewControl>Represents the control that is used to show the hierachical data as a tree.</StiTreeViewControl>
  </HelpDialogs>
  <HelpViewer>
    <AddPageBreaks>Visual separator of report pages.</AddPageBreaks>
    <AllowAddOrModifyTextAnnotations>Limited access to work with annotations in the document.</AllowAddOrModifyTextAnnotations>
    <AllowCopyTextAndGraphics>Limited access to copying information.</AllowCopyTextAndGraphics>
    <AllowEditable>Allows changing components with the Editable property enabled.</AllowEditable>
    <AllowModifyContents>Limited access to the text editing.</AllowModifyContents>
    <AllowPrintDocument>Limited access to the print operation.</AllowPrintDocument>
    <Bookmarks>Show the bookmark panel that is used for quick navigation to jump directly to a bookmarked location.</Bookmarks>
    <BorderType>The border type of components: simple - drawing borders of components with characters +, -, |; Unicode single - drawing the borders with single box-drawing characters, Unicode double - drawing the borders with double box-drawing characters.</BorderType>
    <Close>Close the Report Preview.</Close>
    <CloseDotMatrix>Close the Dot Matrix Viewer.</CloseDotMatrix>
    <Compressed>Compression of the ready document. It is recommended to always include file compression.</Compressed>
    <CompressToArchive>Pack all files and folders in the zip archive.</CompressToArchive>
    <ContinuousPages>The mode of placing report pages as a vertical strip.</ContinuousPages>
    <CurrentPage>Processing the current page. If this option is selected, then a selected report page will be processed.</CurrentPage>
    <CutEdges>Trim the borders of report pages.</CutEdges>
    <CutLongLines>Trim the long lines (text lines) by the borders of components.</CutLongLines>
    <DashboardContains1>Sorry, but this report contains a dashboard.</DashboardContains1>
    <DashboardContains2>You should open this report directly in the designer!</DashboardContains2>
    <DashboardContains3>You cannot do that from the report viewer!</DashboardContains3>
    <DigitalSignature>The digital signature of the file.</DigitalSignature>
    <DitheringType>Dithering type: None - no dithering, Ordered, FloydSt. - with dithering.</DitheringType>
    <DotMatrixMode>This mode allows you to see how will a report look like if to print it on a dot matrix printer.</DotMatrixMode>
    <DrawBorder>Drawing the borders of components with graphic characters.</DrawBorder>
    <Edit>Edit components.</Edit>
    <EmbeddedFonts>Embed the font files into a PDF file.</EmbeddedFonts>
    <EmbeddedImageData>Embed images directly into the HTML file.</EmbeddedImageData>
    <Encoding>Encoding the report text after export.</Encoding>
    <EncodingData>Encoding data file.</EncodingData>
    <EncryptionKeyLength>The length of the encryption key. The longer the length is, the more difficult it is to decrypt the document, and, accordingly, the document security is on higher priority.</EncryptionKeyLength>
    <ExportDataOnly>Export only Data bands (the Table component, Hierachical band).</ExportDataOnly>
    <ExportEachPageToSheet>Export each report page in a separate Excel sheet.</ExportEachPageToSheet>
    <ExportMode>Apply a filter condition when exporting. Data Only - only data bands (Table component, Hierarchical Band) will be exported. Data and Headers/Footers - data bands (Table component, Hierarchical Band) and their headers/footers will be exported. All Bands - All the report bands will be exported.</ExportMode>
    <ExportModeHtml>The way of the HTML page markup.</ExportModeHtml>
    <ExportModeRtf>Presentation of the report data after export. The Table - the report will look like a table, where each report component is a table cell. Frame - each component will look like a single frame, but without any relationship between them.</ExportModeRtf>
    <ExportObjectFormatting>Apply formatting to export data from Data bands (Table component, Hierachical band).</ExportObjectFormatting>
    <ExportPageBreaks>Show the borders of the report pages on the Excel sheet.</ExportPageBreaks>
    <ExportRtfTextAsImage>Convert the RTF text into the image. If the option is enabled, then, when exporting, RichText decomposes into simpler primitives supported by the PDF format. RichText with complex formatting (embedded images, tables) cannot always be converted correctly. In this case it is recommended to enable this option.</ExportRtfTextAsImage>
    <Find>Find a text in the report.</Find>
    <FullScreen>Full screen reading.</FullScreen>
    <GetCertificateFromCryptoUI>Using the interface of the system cryptography library.</GetCertificateFromCryptoUI>
    <ImageCompressionMethod>The compression method: JPEG - this may cause loss of quality, Flate – no quality loss, Simple, Ordered, FloydSt. - images are output in monochrome.</ImageCompressionMethod>
    <ImageFormat>The image format in the finished file.</ImageFormat>
    <ImageQuality>Allows you to choose the ratio of the image quality/size of the file. The higher the quality is, the larger is the size of the finished file.</ImageQuality>
    <ImageQualityPdf>The option is available only in JPEG compression, and allows you to select the ratio of the image quality/size of the file. The higher the quality is, the larger is the size of the finished file.</ImageQualityPdf>
    <ImageResolution>The number of pixels per inch. The higher the number of pixels is, the better is the quality of the image. The size of the finished file is much larger.</ImageResolution>
    <ImageType>The color scheme of the image: color - image after exporting will fully match the image in the viewer; gray – an image after exporting will be of the gray shade; monochrome - the images will be strictly black and white. At the same time, it should be considered that the monochrome has three modes None, Ordered, FloydSt.</ImageType>
    <KillSpaceLines>Remove blank lines (rows) in the document.</KillSpaceLines>
    <MultipleFiles>Each report page can be a separate file.</MultipleFiles>
    <Open>Open the previously saved report in the window of preview.</Open>
    <OpenAfterExport>Automatic opening of the created document (after export) by the program set for these file types.</OpenAfterExport>
    <OwnerPassword>The password to access operations with files.</OwnerPassword>
    <PageAll>Processing of all report pages.</PageAll>
    <PageDelete>Delete the selected report page.</PageDelete>
    <PageDesign>Edit the selected page in the report designer.</PageDesign>
    <PageFirst>Go to the first report page.</PageFirst>
    <PageGoTo>Go to the specified report page.</PageGoTo>
    <PageLast>Go to the last report page.</PageLast>
    <PageNew>Add a new page to a report.</PageNew>
    <PageNext>Go to the next report page.</PageNext>
    <PagePrevious>Go to the previous report page.</PagePrevious>
    <PageSize>Change the page parameters in a report.</PageSize>
    <Parameters>Showing parameters panel which is used when report rendering.</Parameters>
    <PdfACompliance>Support for the standard of the long-term archiving and storing of electronic documents.</PdfACompliance>
    <Print>Print a report.</Print>
    <PutFeedPageCode>Feed pages in the final document with a special character.</PutFeedPageCode>
    <RangePages>The page numbers to be processed. You can specify a single page, a list of pages (using a comma as the separator), as well as specify the range by setting the start page of the range separated by "-" and the end page of the range. For example: 1,3,5-12.</RangePages>
    <RemoveEmptySpaceAtBottom>Minimize the empty space at the bottom of the page.</RemoveEmptySpaceAtBottom>
    <Resources>Showing resources panel</Resources>
    <RestrictEditing>Limits on changes in a Word document.</RestrictEditing>
    <Save>Save a report for further using.</Save>
    <ScaleHtml>The size (scale) of report pages and items after the export.</ScaleHtml>
    <ScaleImage>The size (scale) of the report after exporting. The lower scale is, the greater is the number of pixels per inch, and vice versa.</ScaleImage>
    <SendEMail>Send a report via Email.</SendEMail>
    <Separator>Separator between the data in the CSV file.</Separator>
    <SkipColumnHeaders>Enable/disable the column headers.</SkipColumnHeaders>
    <StandardPdfFonts>14 standard Adobe fonts. If this option is enabled, then only standard 14 fonts will be used in the PDF file. All report fonts are converted into them.</StandardPdfFonts>
    <SubjectNameString>Certificate identifier. The identifier is the name of the certificate owner (full line) or a part of the name (substring).</SubjectNameString>
    <Thumbnails>Show the thumbnails that can be used for quick navigation to find the section of the report that you want to jump to.</Thumbnails>
    <TiffCompressionScheme>Compression scheme for TIFF files.</TiffCompressionScheme>
    <ToolEditor>This tool allows you to edit contents of text components directly in the report viewer.</ToolEditor>
    <TypeExport>The file the report will be converted into.</TypeExport>
    <UnsafeCompilation1>This document uses the compilation process. In some cases, this may not be safe. Therefore, the document will be loaded in safe mode to minimize any potential risks.</UnsafeCompilation1>
    <UnsafeCompilation2>If you require this document to be used in its original form, please contact your developers.</UnsafeCompilation2>
    <UseDefaultSystemEncoding>Use system coding by default or specify the encoding by standard.</UseDefaultSystemEncoding>
    <UseOnePageHeaderAndFooter>Define the page bands Header and Footer as the header and footer of the Microsoft Word document.</UseOnePageHeaderAndFooter>
    <UsePageHeadersAndFooters>Define the bands Page Header and Footer as the header and footer of the document in Microsoft Word.</UsePageHeadersAndFooters>
    <UserPassword>The password required to open the document.</UserPassword>
    <UseUnicode>Extended support for encoding characters. It affects on the internal character encoding within the PDF file, and improves the copying of the text from the PDF file.</UseUnicode>
    <ViewModeContinuous>Show all report pages as a vertical ribbon.</ViewModeContinuous>
    <ViewModeMultiplePages>Zoom the report so that as many pages as can be fit in window are displayed.</ViewModeMultiplePages>
    <ViewModeSinglePage>Show a single page in the window of preview.</ViewModeSinglePage>
    <ZoomMultiplePages>Zoom the report so that the selected pages fit in the window.</ZoomMultiplePages>
    <ZoomOnePage>Zoom the report so that an entire page fits in the window.</ZoomOnePage>
    <ZoomPageWidth>Zoom the report so that the width of the page matches the width of the window.</ZoomPageWidth>
    <ZoomTwoPages>Zoom the report so that two pages fit in the window.</ZoomTwoPages>
    <ZoomTxt>The report size (scale): X - change the horizontal scale, Y - to change the vertical scale.</ZoomTxt>
  </HelpViewer>
  <Interface>
    <Mouse>Mouse</Mouse>
    <MouseDescription>Optimized for used with mouse</MouseDescription>
    <Touch>Touch</Touch>
    <TouchDescription>Optimized for used with touch</TouchDescription>
  </Interface>
  <MainMenu>
    <menuCheckIssues>Check for Issues</menuCheckIssues>
    <menuContextClone>Clone...</menuContextClone>
    <menuContextDesign>Design...</menuContextDesign>
    <menuContextTextFormat>Text Format...</menuContextTextFormat>
    <menuConvertToCheckBox>Convert to CheckBox</menuConvertToCheckBox>
    <menuConvertToImage>Convert to Image</menuConvertToImage>
    <MenuConvertToRichText>Convert to RichText</MenuConvertToRichText>
    <menuConvertToText>Convert to Text</menuConvertToText>
    <menuDeleteColumn>Delete Column</menuDeleteColumn>
    <menuDeleteRow>Delete Row</menuDeleteRow>
    <menuEdit>&amp;Edit</menuEdit>
    <menuEditBusinessObjectFromDataSetNew>New Business Object From DataSet...</menuEditBusinessObjectFromDataSetNew>
    <menuEditBusinessObjectNew>New Business Object...</menuEditBusinessObjectNew>
    <menuEditCalcColumnNew>New Calculated Column...</menuEditCalcColumnNew>
    <menuEditCantRedo>Can't Redo</menuEditCantRedo>
    <menuEditCantUndo>Can't Undo</menuEditCantUndo>
    <menuEditCategoryNew>New Category...</menuEditCategoryNew>
    <menuEditClearContents>Clear Contents</menuEditClearContents>
    <menuEditColumnNew>New Column...</menuEditColumnNew>
    <menuEditConnectionNew>New Connection...</menuEditConnectionNew>
    <menuEditCopy>&amp;Copy</menuEditCopy>
    <menuEditCut>Cu&amp;t</menuEditCut>
    <menuEditDataParameterNew>New Parameter...</menuEditDataParameterNew>
    <menuEditDataSourceNew>New Data Source...</menuEditDataSourceNew>
    <menuEditDataSourcesNew>New Data Sources...</menuEditDataSourcesNew>
    <menuEditDataTransformationNew>New Data Transformation...</menuEditDataTransformationNew>
    <menuEditDelete>&amp;Delete</menuEditDelete>
    <menuEditEdit>Edit</menuEditEdit>
    <menuEditFunctionNew>New Function...</menuEditFunctionNew>
    <menuEditImportRelations>Import Relations...</menuEditImportRelations>
    <menuEditPaste>&amp;Paste</menuEditPaste>
    <menuEditRedo>&amp;Redo</menuEditRedo>
    <menuEditRedoText>&amp;Redo {0}</menuEditRedoText>
    <menuEditRelationNew>New Relation...</menuEditRelationNew>
    <menuEditRemoveUnused>Remove Unused Items</menuEditRemoveUnused>
    <menuEditResourceNew>New Resource...</menuEditResourceNew>
    <menuEditSelectAll>Select &amp;All</menuEditSelectAll>
    <menuEditSynchronize>Synchronize</menuEditSynchronize>
    <menuEditUndo>&amp;Undo</menuEditUndo>
    <menuEditUndoText>&amp;Undo {0}</menuEditUndoText>
    <menuEditVariableNew>New Variable...</menuEditVariableNew>
    <menuEditViewData>View Data...</menuEditViewData>
    <menuEmbedAllDataToResources>Embed all data to resources</menuEmbedAllDataToResources>
    <menuFile>&amp;File</menuFile>
    <menuFileClose>&amp;Close</menuFileClose>
    <menuFileDashboardDelete>Delete Dashboard</menuFileDashboardDelete>
    <menuFileDashboardNew>New Dashboard</menuFileDashboardNew>
    <menuFileDashboardOpen>&amp;Open Dashboard...</menuFileDashboardOpen>
    <menuFileDashboardSaveAs>Save Dashboard &amp;As...</menuFileDashboardSaveAs>
    <menuFileExit>E&amp;xit</menuFileExit>
    <menuFileExportXMLSchema>Export XML Schema...</menuFileExportXMLSchema>
    <menuFileFormNew>New Form</menuFileFormNew>
    <menuFileFormOpen>Open Form</menuFileFormOpen>
    <menuFileImportXMLSchema>Import XML Schema...</menuFileImportXMLSchema>
    <menuFileMerge>Merge...</menuFileMerge>
    <menuFileMergeXMLSchema>Merge XML Schema...</menuFileMergeXMLSchema>
    <menuFileNew>&amp;New</menuFileNew>
    <menuFileOpen>&amp;Open...</menuFileOpen>
    <menuFilePageDelete>Delete Page</menuFilePageDelete>
    <menuFilePageNew>New Page</menuFilePageNew>
    <menuFilePageOpen>Open Page...</menuFilePageOpen>
    <menuFilePageSaveAs>Save Page As...</menuFilePageSaveAs>
    <menuFilePageSetup>Page Setup...</menuFilePageSetup>
    <menuFileRecentDocuments>Recent Documents</menuFileRecentDocuments>
    <menuFileRecentLocations>Recent Locations</menuFileRecentLocations>
    <menuFileReportNew>&amp;New Report...</menuFileReportNew>
    <menuFileReportOpen>&amp;Open Report...</menuFileReportOpen>
    <menuFileReportOpenFromGoogleDocs>Open Report from Google Docs...</menuFileReportOpenFromGoogleDocs>
    <menuFileReportPreview>&amp;Preview</menuFileReportPreview>
    <menuFileReportSave>&amp;Save Report</menuFileReportSave>
    <menuFileReportSaveAs>Save Report &amp;As...</menuFileReportSaveAs>
    <menuFileReportSaveAsToGoogleDocs>Save Report As to Google Docs...</menuFileReportSaveAsToGoogleDocs>
    <menuFileReportSetup>Report &amp;Setup...</menuFileReportSetup>
    <menuFileReportWizardNew>New Report with &amp;Wizard...</menuFileReportWizardNew>
    <menuFileSave>&amp;Save</menuFileSave>
    <menuFileSaveAs>Save As...</menuFileSaveAs>
    <menuFileScreenNew>New Screen</menuFileScreenNew>
    <menuHelp>&amp;Help</menuHelp>
    <menuHelpAboutProgramm>&amp;About...</menuHelpAboutProgramm>
    <menuHelpContents>&amp;Contents</menuHelpContents>
    <menuHelpDemos>Demos</menuHelpDemos>
    <menuHelpDocumentation>Documentation</menuHelpDocumentation>
    <menuHelpFAQPage>FAQ Page</menuHelpFAQPage>
    <menuHelpForum>Forum</menuHelpForum>
    <menuHelpHowToRegister>How to Register</menuHelpHowToRegister>
    <menuHelpProductHomePage>Product Home Page</menuHelpProductHomePage>
    <menuHelpSamples>Samples</menuHelpSamples>
    <menuHelpSupport>&amp;Support</menuHelpSupport>
    <menuHelpTrainingCourses>Training Courses</menuHelpTrainingCourses>
    <menuHelpVideos>Videos</menuHelpVideos>
    <menuInsertColumnToLeft>Insert Column To Left</menuInsertColumnToLeft>
    <menuInsertColumnToRight>Insert Column To Right</menuInsertColumnToRight>
    <menuInsertRowAbove>Insert Row Above</menuInsertRowAbove>
    <menuInsertRowBelow>Insert Row Below</menuInsertRowBelow>
    <menuJoinCells>Join Cells</menuJoinCells>
    <menuMakeThisRelationActive>Make This Relation Active</menuMakeThisRelationActive>
    <menuSelectColumn>Select Column</menuSelectColumn>
    <menuSelectRow>Select Row</menuSelectRow>
    <menuTable>Table</menuTable>
    <menuTools>&amp;Tools</menuTools>
    <menuToolsDataStore>Data &amp;Store...</menuToolsDataStore>
    <menuToolsDictionary>&amp;Dictionary...</menuToolsDictionary>
    <menuToolsOptions>&amp;Options...</menuToolsOptions>
    <menuToolsPagesManager>&amp;Pages Manager...</menuToolsPagesManager>
    <menuToolsServicesConfigurator>Services &amp;Configurator...</menuToolsServicesConfigurator>
    <menuToolsStyleDesigner>Style &amp;Designer...</menuToolsStyleDesigner>
    <menuView>&amp;View</menuView>
    <menuViewAlignToGrid>Align to Grid</menuViewAlignToGrid>
    <menuViewNormal>&amp;Normal</menuViewNormal>
    <menuViewOptions>Options</menuViewOptions>
    <menuViewPageBreakPreview>Page &amp;Break Preview</menuViewPageBreakPreview>
    <menuViewQuickInfo>Quick Info</menuViewQuickInfo>
    <menuViewQuickInfoNone>None</menuViewQuickInfoNone>
    <menuViewQuickInfoOverlay>Display Over Components</menuViewQuickInfoOverlay>
    <menuViewQuickInfoShowAliases>Show Aliases</menuViewQuickInfoShowAliases>
    <menuViewQuickInfoShowComponentsNames>Show Components Names</menuViewQuickInfoShowComponentsNames>
    <menuViewQuickInfoShowContent>Show Content</menuViewQuickInfoShowContent>
    <menuViewQuickInfoShowEvents>Show Events</menuViewQuickInfoShowEvents>
    <menuViewQuickInfoShowFields>Show Fields</menuViewQuickInfoShowFields>
    <menuViewQuickInfoShowFieldsOnly>Show Fields Only</menuViewQuickInfoShowFieldsOnly>
    <menuViewShowGrid>Show Grid</menuViewShowGrid>
    <menuViewShowHeaders>Show Headers</menuViewShowHeaders>
    <menuViewShowInsertTab>Show 'Insert' Tab</menuViewShowInsertTab>
    <menuViewShowOrder>Show Order</menuViewShowOrder>
    <menuViewShowRulers>Show Rulers</menuViewShowRulers>
    <menuViewShowToolbox>Show Toolbox</menuViewShowToolbox>
    <menuViewToolbars>Toolbars</menuViewToolbars>
  </MainMenu>
  <Map>
    <LinkDataForm>Link Data</LinkDataForm>
    <MapEditorForm>Map Editor</MapEditorForm>
  </Map>
  <MathFormula>
    <Alphabets>Alphabets</Alphabets>
    <Arrows>Arrows</Arrows>
    <Formulas>Formulas</Formulas>
    <Maths>Maths</Maths>
    <Operators>Operators</Operators>
  </MathFormula>
  <Messages>
    <ChangeRequestTimeout>The request timeout for SQL queries exceeds the request timeout of the report designer. Set the request timeout for the designer component in no less than {0} seconds.</ChangeRequestTimeout>
    <DatabaseTypeChangeWarning>Be careful! This function converts the data source to {0}. To successfully retrieve data, all table and column names must match. Queries will not be converted. Do you want to proceed?</DatabaseTypeChangeWarning>
    <DoNotShowAgain>Do not show again</DoNotShowAgain>
    <LatexFormat>The LaTeX format is used to display mathematical formulas.</LatexFormat>
    <MessageTimeOutExpired>Command timeout has expired!</MessageTimeOutExpired>
    <MustBeFile>Must be {0} file.</MustBeFile>
    <RenderingWillOccurInTheInterpretationMode>When viewing the shared report, the rendering will occur in the interpretation mode!</RenderingWillOccurInTheInterpretationMode>
    <ResourceCannotBeDeleted>The resource "{0}" cannot be deleted, because it is used in the report!</ResourceCannotBeDeleted>
    <ShareURLOfTheItemHasBeenUpdated>The share URL of the item has been updated!</ShareURLOfTheItemHasBeenUpdated>
    <ShareYourReportYouShouldSave>To share your report you should save the report to Stimulsoft Cloud!</ShareYourReportYouShouldSave>
    <SwitchingBetweenModes>Switching between modes will cause the loss of changes. Do you want to proceed?</SwitchingBetweenModes>
    <SwitchParametersOrientation>Please change the value of the 'ParametersOrientation' report property to '{0}' to show this category on the parameters panel.</SwitchParametersOrientation>
    <TextRegistrationSuccessfully>
        Registration completed successfully!

        The Email was sent to "{0}".
        It contains a link to confirm your registration. To complete the registration please follow the link from the Email you received.
    </TextRegistrationSuccessfully>
    <ThisFieldIsNotSpecified>This Field is Not Specified!</ThisFieldIsNotSpecified>
    <ThisFunctionEmbedsAllReportDataToTheReport>This function embeds all report data to the report resources and makes your report standalone. Be careful! All your data settings will be changed and can't be restored. Please, make a backup copy of your report firstly.{0}Are you sure?</ThisFunctionEmbedsAllReportDataToTheReport>
    <YouNeedToLoginFirstToStartUsingTheSoftware>You need to login first to start using the software. The application will close if you quit the login form.</YouNeedToLoginFirstToStartUsingTheSoftware>
  </Messages>
  <Notices>
    <AccessDenied>Access Denied!</AccessDenied>
    <AccountLocked>Your account is locked! In order to resolve it, please send your <NAME_EMAIL>!</AccountLocked>
    <ActivationContact1>Please contact our sales department at </ActivationContact1>
    <ActivationContact2> to resolve this.</ActivationContact2>
    <ActivationExpiriedBeforeFirstRelease>Your Subscription of Stimulsoft Reports.Ultimate expired earlier than the first version of our Stimulsoft Server was released!</ActivationExpiriedBeforeFirstRelease>
    <ActivationLicenseIsNotCorrect>License file is not correct! Please contact our sales <NAME_EMAIL>!</ActivationLicenseIsNotCorrect>
    <ActivationLockedAccount>Your account is locked. Please contact our sales <NAME_EMAIL>!</ActivationLockedAccount>
    <ActivationLockedAccountExt>Your account is locked.</ActivationLockedAccountExt>
    <ActivationMaxActivationsReached>You reached the maximum number of activations. Please contact our sales <NAME_EMAIL>!</ActivationMaxActivationsReached>
    <ActivationMaxActivationsReached1>You can manage the registered computers from your </ActivationMaxActivationsReached1>
    <ActivationMaxActivationsReached2>Stimulsoft Account.</ActivationMaxActivationsReached2>
    <ActivationMaxComputersReached>You have already registered the maximum number of computers allowed under your subscription plan.</ActivationMaxComputersReached>
    <ActivationMaxDeactivationsReached>Device deactivation is not possible as you have reached the limit for this operation. Please contact our sales <NAME_EMAIL>!</ActivationMaxDeactivationsReached>
    <ActivationServerIsNotAvailableNow>Stimulsoft Activation Server is not available now! Please try again later.</ActivationServerIsNotAvailableNow>
    <ActivationServerVersionNotAllowed>You cannot activate this version of Stimulsoft Server because your Subscription Program expired! Please log in to your account and check which version of the Stimulsoft Server you can install and activate.</ActivationServerVersionNotAllowed>
    <ActivationSomeTroublesOccurred>An error occurred during activation. Please try again later.</ActivationSomeTroublesOccurred>
    <ActivationTrialExpired>You can continue to use Stimulsoft Designer by purchasing the software.</ActivationTrialExpired>
    <ActivationTrialExtend1>If you are interested in extending your trial, please </ActivationTrialExtend1>
    <ActivationTrialExtend2>tell us why.</ActivationTrialExtend2>
    <ActivationUserNameOrPasswordIsWrong>Your user name (Email) or password is wrong!</ActivationUserNameOrPasswordIsWrong>
    <ActivationWrongAccountType>Your account type does not allow to activate Stimulsoft Server!</ActivationWrongAccountType>
    <AITokensExhausted>Your tokens have been exhausted! Please contact our sales <NAME_EMAIL>!</AITokensExhausted>
    <Alert>Alert</Alert>
    <AuthAccountAlreadyExists>The account with this User Name (email) already exists. To associate it with your {0} account, please use your Stimulsoft account password.</AuthAccountAlreadyExists>
    <AuthAccountCantBeUsedNow>The account cannot be used now!</AuthAccountCantBeUsedNow>
    <AuthAccountIsNotActivated>The account is not activated yet! Please follow the instructions sent to the Email during registration.</AuthAccountIsNotActivated>
    <AuthCantChangeRoleBecauseLastAdministratorUser>The user role cannot be changed because this is the last administrator user in this workspace!</AuthCantChangeRoleBecauseLastAdministratorUser>
    <AuthCantChangeRoleBecauseLastSupervisorUser>The user role cannot be changed because this is the last supervisor user at this server!</AuthCantChangeRoleBecauseLastSupervisorUser>
    <AuthCantChangeSystemRole>The system role cannot be changed!</AuthCantChangeSystemRole>
    <AuthCantDeleteHimselfUser>The user cannot delete himself!</AuthCantDeleteHimselfUser>
    <AuthCantDeleteLastAdministratorUser>The user cannot be deleted because this is the last administrator user in this workspace!</AuthCantDeleteLastAdministratorUser>
    <AuthCantDeleteLastSupervisorUser>The user cannot be deleted because this is the last supervisor user at this server!</AuthCantDeleteLastSupervisorUser>
    <AuthCantDeleteSystemRole>Cannot delete this role, because it is a system role!</AuthCantDeleteSystemRole>
    <AuthCantDisableUserBecauseLastAdministratorUser>The user cannot be disabled because this is the last administrator user in this workspace!</AuthCantDisableUserBecauseLastAdministratorUser>
    <AuthCantDisableUserBecauseLastSupervisorUser>The user cannot be disabled because this is the last supervisor user at this server!</AuthCantDisableUserBecauseLastSupervisorUser>
    <AuthFirstNameIsNotSpecified>The first name is not specified!</AuthFirstNameIsNotSpecified>
    <AuthLastNameIsNotSpecified>The last name is not specified!</AuthLastNameIsNotSpecified>
    <AuthOAuthIdNotSpecified>The OAuth identificator is not specified!</AuthOAuthIdNotSpecified>
    <AuthPasswordIsNotCorrect>The password is not correct!</AuthPasswordIsNotCorrect>
    <AuthPasswordIsNotSpecified>The password is not specified!</AuthPasswordIsNotSpecified>
    <AuthPasswordIsTooLong>The password is too long!</AuthPasswordIsTooLong>
    <AuthPasswordIsTooShort>The password is too short (a minimum length is 6 chars)!</AuthPasswordIsTooShort>
    <AuthRequestsLimitIsExceeded>Request limit exceeded! Please contact our sales <NAME_EMAIL>!</AuthRequestsLimitIsExceeded>
    <AuthRoleCantBeDeletedBecauseUsedByUsers>You cannot delete the role because it is used by other users.</AuthRoleCantBeDeletedBecauseUsedByUsers>
    <AuthRoleNameAlreadyExists>The role with the specified name "{0}" already exists!</AuthRoleNameAlreadyExists>
    <AuthRoleNameIsSystemRole>The role with the specified name "{0}" is a system role!</AuthRoleNameIsSystemRole>
    <AuthSendMessageWithInstructions>A message with further instructions is sent to "{0}"!</AuthSendMessageWithInstructions>
    <AuthTokenIsNotCorrect>Token is not correct!</AuthTokenIsNotCorrect>
    <AuthUserHasLoggedOut>You have logged out!</AuthUserHasLoggedOut>
    <AuthUserHasLoggedOutPrevDevice>You have been logged out because there was a login detected from another device!</AuthUserHasLoggedOutPrevDevice>
    <AuthUserNameAlreadyExists>The username (Email) is already in use!</AuthUserNameAlreadyExists>
    <AuthUserNameEmailIsBlocked>This Email address cannot be used!</AuthUserNameEmailIsBlocked>
    <AuthUserNameIsNotSpecified>The username (Email) is not specified!</AuthUserNameIsNotSpecified>
    <AuthUserNameIsTooLong>The username (Email) is too long!</AuthUserNameIsTooLong>
    <AuthUserNameNotAssociatedWithYourAccount>Username (Email) {0} is not associated with your {1} account!</AuthUserNameNotAssociatedWithYourAccount>
    <AuthUserNameOrPasswordIsNotCorrect>The username (Email) or password is incorrect!</AuthUserNameOrPasswordIsNotCorrect>
    <AuthUserNameShouldLookLikeAnEmailAddress>The username should be similar to the Email address!</AuthUserNameShouldLookLikeAnEmailAddress>
    <AuthWorkspaceNameAlreadyInUse>The workspace name is already in use!</AuthWorkspaceNameAlreadyInUse>
    <CommandTimeOut>Waiting time of processing the command is elapsed!</CommandTimeOut>
    <Congratulations>Congratulations!</Congratulations>
    <DeveloperInformation>Developer Information</DeveloperInformation>
    <EndDateShouldBeGreaterThanCurrentDate>The end date should be greater than the current date!</EndDateShouldBeGreaterThanCurrentDate>
    <EndDateShouldBeGreaterThanStartDate>The end date should be greater than start date!</EndDateShouldBeGreaterThanStartDate>
    <ExecutionError>Execution error</ExecutionError>
    <GracePeriod>The grace period of your subscription will expire in {0} days.</GracePeriod>
    <IsIdentical>"{0}" and "{1}" are identical."</IsIdentical>
    <IsNotAuthorized>No access to "{0}"!</IsNotAuthorized>
    <IsNotCorrect>"{0}" is incorrect!</IsNotCorrect>
    <IsNotDeleted>"{0}" is not deleted!</IsNotDeleted>
    <IsNotEqual>"{0}" is not equal!</IsNotEqual>
    <IsNotFound>"{0}" is not found!</IsNotFound>
    <IsNotRecognized>"{0}" is not recognized!</IsNotRecognized>
    <IsNotSpecified>"{0}" is not specified!</IsNotSpecified>
    <IsRequiredFile>You should add at least one file!</IsRequiredFile>
    <ItemCantBeAttachedToItself>The item cannot be attached to itself!</ItemCantBeAttachedToItself>
    <ItemCantBeDeletedBecauseItemIsAttachedToOtherItems>The elements "{0}" cannot be deleted because they are attached to other elements!</ItemCantBeDeletedBecauseItemIsAttachedToOtherItems>
    <ItemCantBeMovedToSpecifiedPlace>The item cannot be moved to a specified place!</ItemCantBeMovedToSpecifiedPlace>
    <ItemDoesNotSupport>The item does not support "{0}"!</ItemDoesNotSupport>
    <KeyAndToKeyAreEqual>A Key and ToKey are equal!</KeyAndToKeyAreEqual>
    <MaximumComputers>Subscription limit reached!</MaximumComputers>
    <MessageMaximumFileSizeExceeded>Attention! The size of the file that you are trying to add exceeds the maximum-allowed size. Do you want to add this file anyway (report processing may slow down significantly)?</MessageMaximumFileSizeExceeded>
    <NewDesignerAvailable>Stimulsoft Designer {0} is available.</NewDesignerAvailable>
    <NewProduct>The new product is added to your subscription!</NewProduct>
    <NewVersionsAvailable>New version</NewVersionsAvailable>
    <NotificationConnectPackage>Connect the "{0}" package for the correct work.</NotificationConnectPackage>
    <NotificationFailed>Failed result!</NotificationFailed>
    <NotificationFailedAddFollowingFiles>Failed to add the following files. Exceeded the size {0} MB.</NotificationFailedAddFollowingFiles>
    <NotificationFilesUploadingComplete>The files uploaded successfully.</NotificationFilesUploadingComplete>
    <NotificationFileUploading>The file "{0}" is uploading.</NotificationFileUploading>
    <NotificationItemDelete>The items are deleting.</NotificationItemDelete>
    <NotificationItemDeleteComplete>The items are deleted successfully.</NotificationItemDeleteComplete>
    <NotificationItemExtraComponentRequired>Extra component required</NotificationItemExtraComponentRequired>
    <NotificationItemInstallToRunWebView>Please install the WebView2 Runtime to run Web Content element correctly.</NotificationItemInstallToRunWebView>
    <NotificationItemInstallWebView>Please install the WebView2 Runtime to work with the Blockly editor correctly.</NotificationItemInstallWebView>
    <NotificationItemRestore>The items are restoring.</NotificationItemRestore>
    <NotificationItemRestoreComplete>The items are restored successfully.</NotificationItemRestoreComplete>
    <NotificationItemTransfer>The items are transferring.</NotificationItemTransfer>
    <NotificationItemTransferComplete>The items are transferred successfully.</NotificationItemTransferComplete>
    <NotificationItemWaitingProcessing>The items are waiting processing.</NotificationItemWaitingProcessing>
    <NotificationMailing>Sending</NotificationMailing>
    <NotificationMailingComplete>The mailing "{0}" is complete.</NotificationMailingComplete>
    <NotificationMailingWaitingProcessing>The mailing "{0}" is waiting processing.</NotificationMailingWaitingProcessing>
    <NotificationOperationAborted>Operation aborted!</NotificationOperationAborted>
    <NotificationPackageRequiredToDisplay>To display this content, please download the "{0}" package from NuGet.</NotificationPackageRequiredToDisplay>
    <NotificationRecycleBinCleaning>The Recycle Bin is cleaning.</NotificationRecycleBinCleaning>
    <NotificationRecycleBinCleaningComplete>The Recycle Bin is cleaned successfully.</NotificationRecycleBinCleaningComplete>
    <NotificationRecycleBinWaitingProcessing>The Recycle Bin cleaning is waiting processing.</NotificationRecycleBinWaitingProcessing>
    <NotificationReportExporting>The report "{0}" is exporting</NotificationReportExporting>
    <NotificationReportExportingComplete>The report "{0}" is exported successfully.</NotificationReportExportingComplete>
    <NotificationReportRendering>The report "{0}" is rendering</NotificationReportRendering>
    <NotificationReportRenderingComplete>The report "{0}" is rendered.</NotificationReportRenderingComplete>
    <NotificationReportWaitingProcessing>The report "{0}" is waiting processing.</NotificationReportWaitingProcessing>
    <NotificationReturnToDesigner>Return to the Stimulsoft Designer to continue.</NotificationReturnToDesigner>
    <NotificationSchedulerRunning>The scheduler "{0}" is running.</NotificationSchedulerRunning>
    <NotificationSchedulerRunningComplete>The scheduler "{0}" running complete.</NotificationSchedulerRunningComplete>
    <NotificationSchedulerWaitingProcessing>The scheduler "{0}" is waiting processing.</NotificationSchedulerWaitingProcessing>
    <NotificationTitleFilesUploading>Uploading files</NotificationTitleFilesUploading>
    <NotificationTitleItemRefreshing>Refreshing item "{0}"</NotificationTitleItemRefreshing>
    <NotificationTitleItemTransferring>Transferring item "{0}"</NotificationTitleItemTransferring>
    <NotificationTitleMailing>Mailing "{0}"</NotificationTitleMailing>
    <NotificationTitleReportExporting>Exporting report "{0}"</NotificationTitleReportExporting>
    <NotificationTitleReportRendering>Rendering report "{0}"</NotificationTitleReportRendering>
    <NotificationTitleSchedulerRunning>Running scheduler "{0}"</NotificationTitleSchedulerRunning>
    <NotificationTransferring>The "{0}" is tranferring to "{1}".</NotificationTransferring>
    <NotificationTransferringComplete>The "{0}" is succesfully transffered to "{1}".</NotificationTransferringComplete>
    <NotificationValueIsNotCorrect>This value is not correct for the type {0}</NotificationValueIsNotCorrect>
    <OutOfRange>Out of range ("{0}")!</OutOfRange>
    <ParsingCommandException>An exception of processing the command in the specified xml: {0}</ParsingCommandException>
    <PleaseLogin>Please login using your Stimulsoft account credentials or register a new account before publishing report.</PleaseLogin>
    <QuotaMaximumComputingCyclesCountExceeded>Maximum computing cycles exceeded.</QuotaMaximumComputingCyclesCountExceeded>
    <QuotaMaximumDataRowsCountExceeded>Maximum data rows in one data source exceeded.</QuotaMaximumDataRowsCountExceeded>
    <QuotaMaximumFileSizeExceeded>Maximum file size exceeded.</QuotaMaximumFileSizeExceeded>
    <QuotaMaximumItemsCountExceeded>Maximum items exceeded.</QuotaMaximumItemsCountExceeded>
    <QuotaMaximumRefreshCountExceeded>Maximum refresh count exceeded</QuotaMaximumRefreshCountExceeded>
    <QuotaMaximumReportPagesCountExceeded>Maximum report pages exceeded.</QuotaMaximumReportPagesCountExceeded>
    <QuotaMaximumResourcesCountExceeded>Maximum resources exceeded.</QuotaMaximumResourcesCountExceeded>
    <QuotaMaximumResourceSizeExceeded>Maximum resource size exceeded.</QuotaMaximumResourceSizeExceeded>
    <QuotaMaximumUsersCountExceeded>Maximum users exceeded.</QuotaMaximumUsersCountExceeded>
    <QuotaMaximumWorkspacesCountExceeded>Maximum workspaces exceeded.</QuotaMaximumWorkspacesCountExceeded>
    <SchedulerCantRunItSelf>The scheduler cannot run itself!</SchedulerCantRunItSelf>
    <SessionTimeOut>Session timeout</SessionTimeOut>
    <SnapshotAlreadyProcessed>The report snapshot is already processed!</SnapshotAlreadyProcessed>
    <SpecifiedItemIsNot>The specified item is not "{0}"!</SpecifiedItemIsNot>
    <SubscriptionExpired>Your subscription has expired!</SubscriptionExpired>
    <SubscriptionExpiredDate>Your subscription will expire on {0}</SubscriptionExpiredDate>
    <SubscriptionExpiredDays>Expires in {0} days</SubscriptionExpiredDays>
    <SubscriptionExpiredExt>Your subscription has expired. Update your subscription to get the last version.</SubscriptionExpiredExt>
    <SubscriptionExpiredShort>Subscription expired</SubscriptionExpiredShort>
    <SubscriptionsOut10>10 days left when the subscription expires!</SubscriptionsOut10>
    <SubscriptionsOut20>The subscription period will expire in 20 days!</SubscriptionsOut20>
    <SuccessfullyRenewed>Your subscription has been successfully updated!</SuccessfullyRenewed>
    <TrialToLicense>We would like to thank you for choosing our software!</TrialToLicense>
    <VersionCopyFromItem>Copy from item "{0}".</VersionCopyFromItem>
    <VersionCreatedFromFile>Created from file "{0}".</VersionCreatedFromFile>
    <VersionCreatedFromItem>Created from item "{0}".</VersionCreatedFromItem>
    <VersionLoadedFromFile>Loaded from file "{0}".</VersionLoadedFromFile>
    <VersionNewItemCreation>New item creation.</VersionNewItemCreation>
    <Warning>Warning</Warning>
    <WindowClosePreventWhileUploading>Some files are uploading now, if you close page they will lost. Are you sure you want to close page?</WindowClosePreventWhileUploading>
    <WithSpecifiedKeyIsNotFound>"{0}" with the specified key is not found!</WithSpecifiedKeyIsNotFound>
    <WouldYouLikeToUpdateNow>Would you like to update now?</WouldYouLikeToUpdateNow>
    <YourTimeSessionHasExpired>Your time session has expired!</YourTimeSessionHasExpired>
    <YourTrialHasExpired>Your trial has expired</YourTrialHasExpired>
    <YourTrialWillExpire>Your trial will expire in {0} days.</YourTrialWillExpire>
    <YouUsingTrialVersion>You are using a trial version!</YouUsingTrialVersion>
  </Notices>
  <NuGet>
    <AlreadyDownloaded>Already Downloaded</AlreadyDownloaded>
    <AssemblyLoadedSuccessfully>The assembly is loaded successfully.</AssemblyLoadedSuccessfully>
    <AssemblyNotFound>It is impossible to find {0} assembly to use the {1} connection!</AssemblyNotFound>
    <Author>Author</Author>
    <Dependencies>Dependencies:</Dependencies>
    <Download>Download</Download>
    <DownloadAll>Download All</DownloadAll>
    <DownloadAndInstall>Download and Install</DownloadAndInstall>
    <DownloadDataAdapter>Download Data Adapter</DownloadDataAdapter>
    <Downloads>Downloads:</Downloads>
    <IAccept>I Accept</IAccept>
    <IDecline>I Decline</IDecline>
    <LicenceFormDesc>The following package require that you accept their license terms before installing.</LicenceFormDesc>
    <LicenceFormDesc1>By clicking "I Accept" you agree to the license terms for the package listed above. If you do not agree to the license terms, click "I Decline".</LicenceFormDesc1>
    <LicenceFormTitle>License Acceptance</LicenceFormTitle>
    <License>License:</License>
    <ProjectUrl>Project URL:</ProjectUrl>
    <ReportAbuse>Report Abuse:</ReportAbuse>
    <RetrievingInformation>Retrieving information...</RetrievingInformation>
    <Tags>Tags:</Tags>
    <Title>Data Adapter from NuGet</Title>
    <ViewLicense>View License</ViewLicense>
  </NuGet>
  <Panels>
    <Dictionary>Dictionary</Dictionary>
    <Messages>Messages</Messages>
    <Properties>Properties</Properties>
    <ReportTree>Report Tree</ReportTree>
  </Panels>
  <Password>
    <gbPassword>Encrypting of the file</gbPassword>
    <lbPasswordLoad>Enter the password to open the file</lbPasswordLoad>
    <lbPasswordSave>Password:</lbPasswordSave>
    <PasswordNotEntered>The password is not entered</PasswordNotEntered>
    <StiLoadPasswordForm>Document encrypting</StiLoadPasswordForm>
    <StiSavePasswordForm>Password</StiSavePasswordForm>
  </Password>
  <Permissions>
    <AdminAPI>API</AdminAPI>
    <AdminBackgroundTasks>Background Tasks</AdminBackgroundTasks>
    <AdminPermissions>Permissions</AdminPermissions>
    <AdminRecycleBin>Recycle Bin</AdminRecycleBin>
    <AdminShare>Share</AdminShare>
    <AdminTransfers>Transfers</AdminTransfers>
    <ItemCalendars>Calendars</ItemCalendars>
    <ItemCloudStorages>Cloud Storages</ItemCloudStorages>
    <ItemContactLists>Contact Lists</ItemContactLists>
    <ItemDashboards>Dashboards</ItemDashboards>
    <ItemDataSources>Data Sources</ItemDataSources>
    <ItemFiles>Files</ItemFiles>
    <ItemFolders>Folders</ItemFolders>
    <ItemForms>Forms</ItemForms>
    <ItemReportSnapshots>Report Snapshots</ItemReportSnapshots>
    <ItemReportTemplates>Report Templates</ItemReportTemplates>
    <ItemSchedulers>Schedulers</ItemSchedulers>
    <ReportDesignerBusinessObjects>Business Objects</ReportDesignerBusinessObjects>
    <ReportDesignerDataColumns>Data Columns</ReportDesignerDataColumns>
    <ReportDesignerDataConnections>Data Connections</ReportDesignerDataConnections>
    <ReportDesignerDataRelations>Data Relations</ReportDesignerDataRelations>
    <ReportDesignerDataSources>Data Sources</ReportDesignerDataSources>
    <ReportDesignerDictionaryActions>Dictionary Actions</ReportDesignerDictionaryActions>
    <ReportDesignerRestrictions>Restrictions</ReportDesignerRestrictions>
    <ReportDesignerVariables>Variables</ReportDesignerVariables>
    <SystemBackupRestore>Backup &amp; Restore</SystemBackupRestore>
    <SystemEmailTemplates>Email Templates</SystemEmailTemplates>
    <SystemLicensing>Licensing</SystemLicensing>
    <SystemMonitoring>Monitoring</SystemMonitoring>
    <SystemUpdate>Update</SystemUpdate>
    <SystemWorkspaces>Workspaces</SystemWorkspaces>
    <TextAdministration>Administration</TextAdministration>
    <TextItems>Items</TextItems>
    <TextReportDesigner>Report Designer</TextReportDesigner>
    <TextSystem>System</TextSystem>
    <TextUsers>Users</TextUsers>
    <UserHimself>Himself</UserHimself>
    <UserRoles>Roles</UserRoles>
    <Users>Users</Users>
    <UserWorkspace>Workspace</UserWorkspace>
  </Permissions>
  <PlacementComponent>
    <MoveLeftFreeSpace>Moves a component to the left side of a free space, increasing the height of the component to the height of free space.</MoveLeftFreeSpace>
    <MoveRightFreeSpace>Moves a component to the right side of a free space, increasing the height of the component to the height of free space.</MoveRightFreeSpace>
  </PlacementComponent>
  <PromptForm>
    <CacheLoginData>Cache login data for current session</CacheLoginData>
  </PromptForm>
  <PropertyCategory>
    <AppearanceCategory>Appearance</AppearanceCategory>
    <AreaCategory>Area</AreaCategory>
    <ArgumentCategory>Argument</ArgumentCategory>
    <AxisCategory>Axis</AxisCategory>
    <BarCodeAdditionalCategory>Bar Code Additional</BarCodeAdditionalCategory>
    <BarCodeCategory>Bar Code</BarCodeCategory>
    <BehaviorCategory>Behavior</BehaviorCategory>
    <ButtonCategory>Button</ButtonCategory>
    <CapNeedle>Cap Needle</CapNeedle>
    <CardsCategory>Cards</CardsCategory>
    <CellCategory>Cell</CellCategory>
    <ChartAdditionalCategory>Chart Additional</ChartAdditionalCategory>
    <ChartCategory>Chart</ChartCategory>
    <ChartMap>Map</ChartMap>
    <CheckCategory>Check</CheckCategory>
    <ColorsCategory>Colors</ColorsCategory>
    <ColumnsCategory>Columns</ColumnsCategory>
    <ComboBoxCategory>Combo Box</ComboBoxCategory>
    <CommonCategory>Common</CommonCategory>
    <ControlCategory>Control</ControlCategory>
    <ControlsEventsCategory>Controls Events</ControlsEventsCategory>
    <CrossTabCategory>Cross-Tab</CrossTabCategory>
    <DashboardCategory>Dashboard</DashboardCategory>
    <DataCategory>Data</DataCategory>
    <DataCells>Data Cells</DataCells>
    <DatePickerCategory>Date Picker</DatePickerCategory>
    <DescriptionCategory>Description</DescriptionCategory>
    <DesignCategory>Design</DesignCategory>
    <DisplayCategory>Display</DisplayCategory>
    <EngineCategory>Engine</EngineCategory>
    <ExportCategory>Export</ExportCategory>
    <ExportEventsCategory>Export Events</ExportEventsCategory>
    <FooterTableCategory>Footer Table</FooterTableCategory>
    <GaugeCategory>Gauge</GaugeCategory>
    <GlobalizationCategory>Globalization</GlobalizationCategory>
    <GridLinesCategory>Grid Lines</GridLinesCategory>
    <HeaderTableCategory>Header Table</HeaderTableCategory>
    <HierarchicalCategory>Hierarchical</HierarchicalCategory>
    <ImageAdditionalCategory>Image Additional</ImageAdditionalCategory>
    <ImageCategory>Image</ImageCategory>
    <IndicatorCategory>Indicator</IndicatorCategory>
    <InterlacingCategory>Interlacing</InterlacingCategory>
    <LabelsCategory>Labels</LabelsCategory>
    <LegendCategory>Legend</LegendCategory>
    <LinearScaleBarCategory>Linear Scale Bar</LinearScaleBarCategory>
    <LinearScaleCategory>Linear Scale</LinearScaleCategory>
    <ListBoxCategory>List Box</ListBoxCategory>
    <MainCategory>Main</MainCategory>
    <MarkerCategory>Marker</MarkerCategory>
    <MathFormulaCategory>Math Formula</MathFormulaCategory>
    <MiscCategory>Misc</MiscCategory>
    <MouseEventsCategory>Mouse Events</MouseEventsCategory>
    <NavigationCategory>Navigation</NavigationCategory>
    <NavigationEventsCategory>Navigation Events</NavigationEventsCategory>
    <Needle>Needle</Needle>
    <NeedleCategory>Needle</NeedleCategory>
    <OnlineMapCategory>Online Map</OnlineMapCategory>
    <OptionsCategory>Options</OptionsCategory>
    <PageAdditionalCategory>Page Additional</PageAdditionalCategory>
    <PageCategory>Page</PageCategory>
    <PageColumnBreakCategory>Page and Column Break</PageColumnBreakCategory>
    <ParametersCategory>Parameters</ParametersCategory>
    <PivotTableCategory>Pivot Table</PivotTableCategory>
    <PositionCategory>Position</PositionCategory>
    <PrimitiveCategory>Primitive</PrimitiveCategory>
    <PrintEventsCategory>Print Events</PrintEventsCategory>
    <ProgressCategory>Progress</ProgressCategory>
    <RadialScaleBarCategory>Radial Scale Bar</RadialScaleBarCategory>
    <RegionMapCategory>Region Map</RegionMapCategory>
    <RenderEventsCategory>Render Events</RenderEventsCategory>
    <SeriesCategory>Series</SeriesCategory>
    <SeriesLabelsCategory>Series Labels</SeriesLabelsCategory>
    <ShapeCategory>Shape</ShapeCategory>
    <Size>Size</Size>
    <SubReportCategory>Sub-Report</SubReportCategory>
    <TableCategory>Table</TableCategory>
    <TableOfContents>Table of Contents</TableOfContents>
    <TextAdditionalCategory>Text Additional</TextAdditionalCategory>
    <TextCategory>Text</TextCategory>
    <TickLabelMajorCategory>Tick Label Major</TickLabelMajorCategory>
    <TickLabelMinorCategory>Tick Label Minor</TickLabelMinorCategory>
    <TickMarkMajorCategory>Tick Mark Major</TickMarkMajorCategory>
    <TickMarkMinorCategory>Tick Mark Minor</TickMarkMinorCategory>
    <TitleCategory>Title</TitleCategory>
    <TreeViewBoxCategory>Tree View Box</TreeViewBoxCategory>
    <TreeViewCategory>Tree View</TreeViewCategory>
    <TrendLineCategory>Trend Line</TrendLineCategory>
    <ValueCategory>Value</ValueCategory>
    <ValueCloseCategory>Value Close</ValueCloseCategory>
    <ValueEndCategory>Value End</ValueEndCategory>
    <ValueEventsCategory>Value Events</ValueEventsCategory>
    <ValueHighCategory>Value High</ValueHighCategory>
    <ValueLowCategory>Value Low</ValueLowCategory>
    <ValueOpenCategory>Value Open</ValueOpenCategory>
    <ViewCategory>View</ViewCategory>
    <WeightCategory>Weight</WeightCategory>
    <WinControlCategory>Win Control</WinControlCategory>
    <ZipCodeCategory>ZIP Code</ZipCodeCategory>
  </PropertyCategory>
  <PropertyColor>
    <AliceBlue>Alice Blue</AliceBlue>
    <AntiqueWhite>Antique White</AntiqueWhite>
    <Aqua>Aqua</Aqua>
    <Aquamarine>Aquamarine</Aquamarine>
    <Azure>Azure</Azure>
    <Beige>Beige</Beige>
    <Bisque>Bisque</Bisque>
    <Black>Black</Black>
    <BlanchedAlmond>Blanched Almond</BlanchedAlmond>
    <Blue>Blue</Blue>
    <BlueViolet>Blue Violet</BlueViolet>
    <Brown>Brown</Brown>
    <BurlyWood>Burly Wood</BurlyWood>
    <CadetBlue>Cadet Blue</CadetBlue>
    <Carmine>Carmine</Carmine>
    <Chartreuse>Chartreuse</Chartreuse>
    <Chocolate>Chocolate</Chocolate>
    <Coral>Coral</Coral>
    <CornflowerBlue>Cornflower Blue</CornflowerBlue>
    <Cornsilk>Cornsilk</Cornsilk>
    <Crimson>Crimson</Crimson>
    <Cyan>Cyan</Cyan>
    <DarkBlue>Dark Blue</DarkBlue>
    <DarkCyan>Dark Cyan</DarkCyan>
    <DarkGoldenrod>Dark Goldenrod</DarkGoldenrod>
    <DarkGray>Dark Gray</DarkGray>
    <DarkGreen>Dark Green</DarkGreen>
    <DarkKhaki>Dark Khaki</DarkKhaki>
    <DarkMagenta>Dark Magenta</DarkMagenta>
    <DarkOliveGreen>Dark Olive Green</DarkOliveGreen>
    <DarkOrange>Dark Orange</DarkOrange>
    <DarkOrchid>Dark Orchid</DarkOrchid>
    <DarkRed>Dark Red</DarkRed>
    <DarkSalmon>Dark Salmon</DarkSalmon>
    <DarkSeaGreen>Dark Sea Green</DarkSeaGreen>
    <DarkSlateBlue>Dark Slate Blue</DarkSlateBlue>
    <DarkSlateGray>Dark Slate Gray</DarkSlateGray>
    <DarkTurquoise>Dark Turquoise</DarkTurquoise>
    <DarkViolet>Dark Violet</DarkViolet>
    <DeepPink>Deep Pink</DeepPink>
    <DeepSkyBlue>Deep Sky Blue</DeepSkyBlue>
    <DimGray>Dim Gray</DimGray>
    <DodgerBlue>Dodger Blue</DodgerBlue>
    <Firebrick>Firebrick</Firebrick>
    <FloralWhite>Floral White</FloralWhite>
    <ForestGreen>Forest Green</ForestGreen>
    <Fuchsia>Fuchsia</Fuchsia>
    <Gainsboro>Gainsboro</Gainsboro>
    <GhostWhite>Ghost White</GhostWhite>
    <Gold>Gold</Gold>
    <Goldenrod>Goldenrod</Goldenrod>
    <Gray>Gray</Gray>
    <Green>Green</Green>
    <GreenYellow>Green Yellow</GreenYellow>
    <Honeydew>Honeydew</Honeydew>
    <HotPink>Hot Pink</HotPink>
    <IndianRed>Indian Red</IndianRed>
    <Indigo>Indigo</Indigo>
    <Ivory>Ivory</Ivory>
    <Khaki>Khaki</Khaki>
    <Lavender>Lavender</Lavender>
    <LavenderBlush>Lavender Blush</LavenderBlush>
    <LawnGreen>Lawn Green</LawnGreen>
    <LemonChiffon>Lemon Chiffon</LemonChiffon>
    <LightBlue>Light Blue</LightBlue>
    <LightCoral>Light Coral</LightCoral>
    <LightCyan>Light Cyan</LightCyan>
    <LightGoldenrodYellow>Light Goldenrod Yellow</LightGoldenrodYellow>
    <LightGray>Light Gray</LightGray>
    <LightGreen>Light Green</LightGreen>
    <LightPink>Light Pink</LightPink>
    <LightSalmon>Light Salmon</LightSalmon>
    <LightSeaGreen>Light Sea Green</LightSeaGreen>
    <LightSkyBlue>Light Sky Blue</LightSkyBlue>
    <LightSlateGray>Light Slate Gray</LightSlateGray>
    <LightSteelBlue>Light Steel Blue</LightSteelBlue>
    <LightYellow>Light Yellow</LightYellow>
    <Lime>Lime</Lime>
    <LimeGreen>Lime Green</LimeGreen>
    <Linen>Linen</Linen>
    <Magenta>Magenta</Magenta>
    <Maroon>Maroon</Maroon>
    <MediumAquamarine>Medium Aquamarine</MediumAquamarine>
    <MediumBlue>Medium Blue</MediumBlue>
    <MediumOrchid>Medium Orchid</MediumOrchid>
    <MediumPurple>Medium Purple</MediumPurple>
    <MediumSeaGreen>Medium Sea Green</MediumSeaGreen>
    <MediumSlateBlue>Medium Slate Blue</MediumSlateBlue>
    <MediumSpringGreen>Medium Spring Green</MediumSpringGreen>
    <MediumTurquoise>Medium Turquoise</MediumTurquoise>
    <MediumVioletRed>Medium Violet Red</MediumVioletRed>
    <MidnightBlue>Midnight Blue</MidnightBlue>
    <MintCream>Mint Cream</MintCream>
    <MistyRose>Misty Rose</MistyRose>
    <Moccasin>Moccasin</Moccasin>
    <NavajoWhite>Navajo White</NavajoWhite>
    <Navy>Navy</Navy>
    <OldLace>Old Lace</OldLace>
    <Olive>Olive</Olive>
    <OliveDrab>Olive Drab</OliveDrab>
    <Orange>Orange</Orange>
    <OrangeRed>Orange Red</OrangeRed>
    <Orchid>Orchid</Orchid>
    <PaleGoldenrod>Pale Goldenrod</PaleGoldenrod>
    <PaleGreen>Pale Green</PaleGreen>
    <PaleTurquoise>Pale Turquoise</PaleTurquoise>
    <PaleVioletRed>Pale Violet Red</PaleVioletRed>
    <PapayaWhip>Papaya Whip</PapayaWhip>
    <PeachPuff>Peach Puff</PeachPuff>
    <Peru>Peru</Peru>
    <Pink>Pink</Pink>
    <Plum>Plum</Plum>
    <PowderBlue>Powder Blue</PowderBlue>
    <Purple>Purple</Purple>
    <Red>Red</Red>
    <RosyBrown>Rosy Brown</RosyBrown>
    <RoyalBlue>Royal Blue</RoyalBlue>
    <SaddleBrown>Saddle Brown</SaddleBrown>
    <Salmon>Salmon</Salmon>
    <SandyBrown>Sandy Brown</SandyBrown>
    <SeaGreen>Sea Green</SeaGreen>
    <SeaShell>Sea Shell</SeaShell>
    <Sienna>Sienna</Sienna>
    <Silver>Silver</Silver>
    <SkyBlue>Sky Blue</SkyBlue>
    <SlateBlue>Slate Blue</SlateBlue>
    <SlateGray>Slate Gray</SlateGray>
    <Snow>Snow</Snow>
    <SpringGreen>Spring Green</SpringGreen>
    <SteelBlue>Steel Blue</SteelBlue>
    <Tan>Tan</Tan>
    <Teal>Teal</Teal>
    <Thistle>Thistle</Thistle>
    <Tomato>Tomato</Tomato>
    <Transparent>Transparent</Transparent>
    <Turquoise>Turquoise</Turquoise>
    <VeryDarkGray>Very Dark Gray</VeryDarkGray>
    <Violet>Violet</Violet>
    <Wheat>Wheat</Wheat>
    <White>White</White>
    <WhiteSmoke>White Smoke</WhiteSmoke>
    <Yellow>Yellow</Yellow>
    <YellowGreen>Yellow Green</YellowGreen>
  </PropertyColor>
  <PropertyEnum>
    <boolFalse>False</boolFalse>
    <boolTrue>True</boolTrue>
    <BorderStyleFixed3D>Fixed 3D</BorderStyleFixed3D>
    <BorderStyleFixedSingle>Fixed Single</BorderStyleFixedSingle>
    <BorderStyleNone>None</BorderStyleNone>
    <ChartAxesTicksAll>All</ChartAxesTicksAll>
    <ChartAxesTicksMajor>Major</ChartAxesTicksMajor>
    <ChartAxesTicksNone>None</ChartAxesTicksNone>
    <ChartGridLinesAll>All</ChartGridLinesAll>
    <ChartGridLinesMajor>Major</ChartGridLinesMajor>
    <ChartGridLinesNone>None</ChartGridLinesNone>
    <ComboBoxStyleDropDown>Drop Down</ComboBoxStyleDropDown>
    <ComboBoxStyleDropDownList>Drop Down List</ComboBoxStyleDropDownList>
    <ComboBoxStyleSimple>Simple</ComboBoxStyleSimple>
    <ContentAlignmentBottomCenter>Bottom Center</ContentAlignmentBottomCenter>
    <ContentAlignmentBottomLeft>Bottom Left</ContentAlignmentBottomLeft>
    <ContentAlignmentBottomRight>Bottom Right</ContentAlignmentBottomRight>
    <ContentAlignmentMiddleCenter>Middle Center</ContentAlignmentMiddleCenter>
    <ContentAlignmentMiddleLeft>Middle Left</ContentAlignmentMiddleLeft>
    <ContentAlignmentMiddleRight>Middle Right</ContentAlignmentMiddleRight>
    <ContentAlignmentTopCenter>Top Center</ContentAlignmentTopCenter>
    <ContentAlignmentTopLeft>Top Left</ContentAlignmentTopLeft>
    <ContentAlignmentTopRight>Top Right</ContentAlignmentTopRight>
    <DataGridLineStyleNone>None</DataGridLineStyleNone>
    <DataGridLineStyleSolid>Solid</DataGridLineStyleSolid>
    <DateTimePickerFormatCustom>Custom</DateTimePickerFormatCustom>
    <DateTimePickerFormatLong>Long</DateTimePickerFormatLong>
    <DateTimePickerFormatShort>Short</DateTimePickerFormatShort>
    <DateTimePickerFormatTime>Time</DateTimePickerFormatTime>
    <DialogResultAbort>Abort</DialogResultAbort>
    <DialogResultCancel>Cancel</DialogResultCancel>
    <DialogResultIgnore>Ignore</DialogResultIgnore>
    <DialogResultNo>No</DialogResultNo>
    <DialogResultNone>None</DialogResultNone>
    <DialogResultOK>OK</DialogResultOK>
    <DialogResultRetry>Retry</DialogResultRetry>
    <DialogResultYes>Yes</DialogResultYes>
    <DuplexDefault>Default</DuplexDefault>
    <DuplexHorizontal>Horizontal</DuplexHorizontal>
    <DuplexSimplex>Simplex</DuplexSimplex>
    <DuplexVertical>Vertical</DuplexVertical>
    <FormStartPositionCenterParent>Center Parent</FormStartPositionCenterParent>
    <FormStartPositionCenterScreen>Center Screen</FormStartPositionCenterScreen>
    <FormStartPositionManual>Manual</FormStartPositionManual>
    <FormStartPositionWindowsDefaultBounds>Windows Default Bounds</FormStartPositionWindowsDefaultBounds>
    <FormStartPositionWindowsDefaultLocation>Windows Default Location</FormStartPositionWindowsDefaultLocation>
    <FormWindowStateMaximized>Maximized</FormWindowStateMaximized>
    <FormWindowStateMinimized>Minimized</FormWindowStateMinimized>
    <FormWindowStateNormal>Normal</FormWindowStateNormal>
    <HorizontalAlignmentCenter>Center</HorizontalAlignmentCenter>
    <HorizontalAlignmentLeft>Left</HorizontalAlignmentLeft>
    <HorizontalAlignmentRight>Right</HorizontalAlignmentRight>
    <HotkeyPrefixHide>Hide</HotkeyPrefixHide>
    <HotkeyPrefixNone>None</HotkeyPrefixNone>
    <HotkeyPrefixShow>Show</HotkeyPrefixShow>
    <LeftRightAlignmentLeft>Left</LeftRightAlignmentLeft>
    <LeftRightAlignmentRight>Right</LeftRightAlignmentRight>
    <PictureBoxSizeModeAutoSize>Auto Size</PictureBoxSizeModeAutoSize>
    <PictureBoxSizeModeCenterImage>Center Image</PictureBoxSizeModeCenterImage>
    <PictureBoxSizeModeNormal>Normal</PictureBoxSizeModeNormal>
    <PictureBoxSizeModeStretchImage>Stretch Image</PictureBoxSizeModeStretchImage>
    <RelationDirectionChildToParent>Child-To-Parent</RelationDirectionChildToParent>
    <RelationDirectionParentToChild>Parent-To-Child</RelationDirectionParentToChild>
    <RightToLeftInherit>Inherit</RightToLeftInherit>
    <RightToLeftNo>No</RightToLeftNo>
    <RightToLeftYes>Yes</RightToLeftYes>
    <SelectionModeMultiExtended>Multi Extended</SelectionModeMultiExtended>
    <SelectionModeMultiSimple>Multi Simple</SelectionModeMultiSimple>
    <SelectionModeNone>None</SelectionModeNone>
    <SelectionModeOne>One</SelectionModeOne>
    <StiAnchorModeBottom>Bottom</StiAnchorModeBottom>
    <StiAnchorModeLeft>Left</StiAnchorModeLeft>
    <StiAnchorModeRight>Right</StiAnchorModeRight>
    <StiAnchorModeTop>Top</StiAnchorModeTop>
    <StiAngleAngle0>0 Degrees</StiAngleAngle0>
    <StiAngleAngle180>180 Degrees</StiAngleAngle180>
    <StiAngleAngle270>270 Degrees</StiAngleAngle270>
    <StiAngleAngle45>45 Degrees</StiAngleAngle45>
    <StiAngleAngle90>90 Degrees</StiAngleAngle90>
    <StiArrowStyleArc>Arc</StiArrowStyleArc>
    <StiArrowStyleArcAndCircle>Arc and Circle</StiArrowStyleArcAndCircle>
    <StiArrowStyleCircle>Circle</StiArrowStyleCircle>
    <StiArrowStyleLines>Lines</StiArrowStyleLines>
    <StiArrowStyleNone>None</StiArrowStyleNone>
    <StiArrowStyleTriangle>Triangle</StiArrowStyleTriangle>
    <StiBorderSidesAll>All</StiBorderSidesAll>
    <StiBorderSidesBottom>Bottom</StiBorderSidesBottom>
    <StiBorderSidesLeft>Left</StiBorderSidesLeft>
    <StiBorderSidesNone>None</StiBorderSidesNone>
    <StiBorderSidesRight>Right</StiBorderSidesRight>
    <StiBorderSidesTop>Top</StiBorderSidesTop>
    <StiBorderStyleBump>Bump</StiBorderStyleBump>
    <StiBorderStyleEtched>Etched</StiBorderStyleEtched>
    <StiBorderStyleFlat>Flat</StiBorderStyleFlat>
    <StiBorderStyleNone>None</StiBorderStyleNone>
    <StiBorderStyleRaised>Raised</StiBorderStyleRaised>
    <StiBorderStyleRaisedInner>Raised Inner</StiBorderStyleRaisedInner>
    <StiBorderStyleRaisedOuter>Raised Outer</StiBorderStyleRaisedOuter>
    <StiBorderStyleSunken>Sunken</StiBorderStyleSunken>
    <StiBorderStyleSunkenInner>Sunken Inner</StiBorderStyleSunkenInner>
    <StiBorderStyleSunkenOuter>Sunken Outer</StiBorderStyleSunkenOuter>
    <StiBrushTypeGlare>Glare Brush</StiBrushTypeGlare>
    <StiBrushTypeGradient0>Gradient Brush, Angle 0</StiBrushTypeGradient0>
    <StiBrushTypeGradient180>Gradient Brush, Angle 180</StiBrushTypeGradient180>
    <StiBrushTypeGradient270>Gradient Brush, Angle 270</StiBrushTypeGradient270>
    <StiBrushTypeGradient45>Gradient Brush, Angle 45</StiBrushTypeGradient45>
    <StiBrushTypeGradient90>Gradient Brush, Angle 90</StiBrushTypeGradient90>
    <StiBrushTypeSolid>Solid Brush</StiBrushTypeSolid>
    <StiButtonSendTypeFDF>FDF</StiButtonSendTypeFDF>
    <StiButtonSendTypeHTML>HTML</StiButtonSendTypeHTML>
    <StiButtonSendTypePDF>PDF</StiButtonSendTypePDF>
    <StiButtonSendTypeXFDF>XFDF</StiButtonSendTypeXFDF>
    <StiCalculationModeCompilation>Compilation</StiCalculationModeCompilation>
    <StiCalculationModeInterpretation>Interpretation</StiCalculationModeInterpretation>
    <StiCapStyleArrow>Arrow</StiCapStyleArrow>
    <StiCapStyleDiamond>Diamond</StiCapStyleDiamond>
    <StiCapStyleNone>None</StiCapStyleNone>
    <StiCapStyleOpen>Open</StiCapStyleOpen>
    <StiCapStyleOval>Oval</StiCapStyleOval>
    <StiCapStyleSquare>Square</StiCapStyleSquare>
    <StiCapStyleStealth>Stealth</StiCapStyleStealth>
    <StiChartLabelsStyleCategory>Category</StiChartLabelsStyleCategory>
    <StiChartLabelsStyleCategoryPercentOfTotal>Category - Percent Of Total</StiChartLabelsStyleCategoryPercentOfTotal>
    <StiChartLabelsStyleCategoryValue>Category - Value</StiChartLabelsStyleCategoryValue>
    <StiChartLabelsStylePercentOfTotal>Percent Of Total</StiChartLabelsStylePercentOfTotal>
    <StiChartLabelsStyleValue>Value</StiChartLabelsStyleValue>
    <StiChartTitleDockBottom>Bottom</StiChartTitleDockBottom>
    <StiChartTitleDockLeft>Left</StiChartTitleDockLeft>
    <StiChartTitleDockRight>Right</StiChartTitleDockRight>
    <StiChartTitleDockTop>Top</StiChartTitleDockTop>
    <StiChartTrendLineTypeExponential>Exponential</StiChartTrendLineTypeExponential>
    <StiChartTrendLineTypeLinear>Linear</StiChartTrendLineTypeLinear>
    <StiChartTrendLineTypeLogarithmic>Logarithmic</StiChartTrendLineTypeLogarithmic>
    <StiChartTrendLineTypeNone>None</StiChartTrendLineTypeNone>
    <StiCheckStateChecked>Checked</StiCheckStateChecked>
    <StiCheckStateIndeterminate>Indeterminate</StiCheckStateIndeterminate>
    <StiCheckStateUnchecked>Unchecked</StiCheckStateUnchecked>
    <StiCheckStyleCheck>Check</StiCheckStyleCheck>
    <StiCheckStyleCheckRectangle>Check Rectangle</StiCheckStyleCheckRectangle>
    <StiCheckStyleCross>Cross</StiCheckStyleCross>
    <StiCheckStyleCrossCircle>Cross Circle</StiCheckStyleCrossCircle>
    <StiCheckStyleCrossRectangle>Cross Rectangle</StiCheckStyleCrossRectangle>
    <StiCheckStyleDotCircle>Dot Circle</StiCheckStyleDotCircle>
    <StiCheckStyleDotRectangle>Dot Rectangle</StiCheckStyleDotRectangle>
    <StiCheckStyleNone>None</StiCheckStyleNone>
    <StiCheckStyleNoneCircle>None Circle</StiCheckStyleNoneCircle>
    <StiCheckStyleNoneRectangle>None Rectangle</StiCheckStyleNoneRectangle>
    <StiCheckSumNo>No</StiCheckSumNo>
    <StiCheckSumYes>Yes</StiCheckSumYes>
    <StiCode11CheckSumAuto>Auto</StiCode11CheckSumAuto>
    <StiCode11CheckSumNone>None</StiCode11CheckSumNone>
    <StiCode11CheckSumOneDigit>One Digit</StiCode11CheckSumOneDigit>
    <StiCode11CheckSumTwoDigits>Two Digits</StiCode11CheckSumTwoDigits>
    <StiColorScaleTypeColor2>2-Color Scale</StiColorScaleTypeColor2>
    <StiColorScaleTypeColor3>3-Color Scale</StiColorScaleTypeColor3>
    <StiColumnDirectionAcrossThenDown>Across Then Down</StiColumnDirectionAcrossThenDown>
    <StiColumnDirectionDownThenAcross>Down Then Across</StiColumnDirectionDownThenAcross>
    <StiColumnShape3DBox>Box</StiColumnShape3DBox>
    <StiColumnShape3DPartialPyramid>Partial Pyramid</StiColumnShape3DPartialPyramid>
    <StiColumnShape3DPyramid>Pyramid</StiColumnShape3DPyramid>
    <StiColumnTypeProcessingCastToColumnType>Cast to Column Type</StiColumnTypeProcessingCastToColumnType>
    <StiColumnTypeProcessingLeaveUnprocessed>Leave Unprocessed</StiColumnTypeProcessingLeaveUnprocessed>
    <StiCompilationAccessAllow>Allow</StiCompilationAccessAllow>
    <StiCompilationAccessAsk>Ask</StiCompilationAccessAsk>
    <StiCompilationAccessDeny>Deny</StiCompilationAccessDeny>
    <StiCompilationAccessForceInterpretation>Force Interpretation</StiCompilationAccessForceInterpretation>
    <StiCrossHorAlignmentCenter>Center</StiCrossHorAlignmentCenter>
    <StiCrossHorAlignmentLeft>Left</StiCrossHorAlignmentLeft>
    <StiCrossHorAlignmentNone>None</StiCrossHorAlignmentNone>
    <StiCrossHorAlignmentRight>Right</StiCrossHorAlignmentRight>
    <StiDashboardContentAlignmentCenter>Center</StiDashboardContentAlignmentCenter>
    <StiDashboardContentAlignmentLeft>Left</StiDashboardContentAlignmentLeft>
    <StiDashboardContentAlignmentRight>Right</StiDashboardContentAlignmentRight>
    <StiDashboardContentAlignmentStretchX>Stretch X</StiDashboardContentAlignmentStretchX>
    <StiDashboardContentAlignmentStretchXY>Stretch XY</StiDashboardContentAlignmentStretchXY>
    <StiDateSelectionModeAutoRange>Auto Range</StiDateSelectionModeAutoRange>
    <StiDateSelectionModeRange>Range</StiDateSelectionModeRange>
    <StiDateSelectionModeSingle>Single</StiDateSelectionModeSingle>
    <StiDateTimeTypeDate>Date</StiDateTimeTypeDate>
    <StiDateTimeTypeDateAndTime>Date and Time</StiDateTimeTypeDateAndTime>
    <StiDateTimeTypeTime>Time</StiDateTimeTypeTime>
    <StiDesignerScaleModeAutomaticScaling>Automatic Scaling</StiDesignerScaleModeAutomaticScaling>
    <StiDesignerScaleModeScaling100>100% Scaling</StiDesignerScaleModeScaling100>
    <StiDesignerSpecificationAuto>Determine Automatically</StiDesignerSpecificationAuto>
    <StiDesignerSpecificationBeginner>I'm Beginner</StiDesignerSpecificationBeginner>
    <StiDesignerSpecificationBICreator>I'm Creator</StiDesignerSpecificationBICreator>
    <StiDesignerSpecificationDeveloper>I'm Developer</StiDesignerSpecificationDeveloper>
    <StiDirectionBottomToTop>Bottom to Top</StiDirectionBottomToTop>
    <StiDirectionLeftToRight>Left to Right</StiDirectionLeftToRight>
    <StiDirectionRightToLeft>Right to Left</StiDirectionRightToLeft>
    <StiDirectionTopToBottom>Top to Bottom</StiDirectionTopToBottom>
    <StiDisplayNameTypeFull>Full</StiDisplayNameTypeFull>
    <StiDisplayNameTypeNone>None</StiDisplayNameTypeNone>
    <StiDisplayNameTypeShort>Short</StiDisplayNameTypeShort>
    <StiDockStyleBottom>Bottom</StiDockStyleBottom>
    <StiDockStyleFill>Fill</StiDockStyleFill>
    <StiDockStyleLeft>Left</StiDockStyleLeft>
    <StiDockStyleNone>None</StiDockStyleNone>
    <StiDockStyleRight>Right</StiDockStyleRight>
    <StiDockStyleTop>Top</StiDockStyleTop>
    <StiDrillDownModeMultiPage>Multi Page</StiDrillDownModeMultiPage>
    <StiDrillDownModeSinglePage>Single Page</StiDrillDownModeSinglePage>
    <StiEanSupplementTypeFiveDigit>FiveDigit</StiEanSupplementTypeFiveDigit>
    <StiEanSupplementTypeNone>None</StiEanSupplementTypeNone>
    <StiEanSupplementTypeTwoDigit>TwoDigit</StiEanSupplementTypeTwoDigit>
    <StiEmptyCellsAsConnectPointsWithLine>Connect Points with Line</StiEmptyCellsAsConnectPointsWithLine>
    <StiEmptyCellsAsGap>Gap</StiEmptyCellsAsGap>
    <StiEmptyCellsAsZero>Zero</StiEmptyCellsAsZero>
    <StiEmptySizeModeAlignFooterToBottom>Align Footer to Bottom</StiEmptySizeModeAlignFooterToBottom>
    <StiEmptySizeModeAlignFooterToTop>Align Footer to Top</StiEmptySizeModeAlignFooterToTop>
    <StiEmptySizeModeDecreaseLastRow>Decrease Last Row</StiEmptySizeModeDecreaseLastRow>
    <StiEmptySizeModeIncreaseLastRow>Increase Last Row</StiEmptySizeModeIncreaseLastRow>
    <StiEnumeratorTypeABC>ABC</StiEnumeratorTypeABC>
    <StiEnumeratorTypeArabic>Arabic</StiEnumeratorTypeArabic>
    <StiEnumeratorTypeNone>None</StiEnumeratorTypeNone>
    <StiEnumeratorTypeRoman>Roman</StiEnumeratorTypeRoman>
    <StiExtendedStyleBoolFalse>False</StiExtendedStyleBoolFalse>
    <StiExtendedStyleBoolFromStyle>From Style</StiExtendedStyleBoolFromStyle>
    <StiExtendedStyleBoolTrue>True</StiExtendedStyleBoolTrue>
    <StiFilterConditionBeginningWith>beginning with</StiFilterConditionBeginningWith>
    <StiFilterConditionBetween>between</StiFilterConditionBetween>
    <StiFilterConditionContaining>containing</StiFilterConditionContaining>
    <StiFilterConditionEndingWith>ending with</StiFilterConditionEndingWith>
    <StiFilterConditionEqualTo>equal to</StiFilterConditionEqualTo>
    <StiFilterConditionEqualToVariableOrVariableIsNull>equal to variable or variable is null</StiFilterConditionEqualToVariableOrVariableIsNull>
    <StiFilterConditionGreaterThan>greater than</StiFilterConditionGreaterThan>
    <StiFilterConditionGreaterThanOrEqualTo>greater than or equal to</StiFilterConditionGreaterThanOrEqualTo>
    <StiFilterConditionIsBlank>is blank</StiFilterConditionIsBlank>
    <StiFilterConditionIsNotBlank>is not blank</StiFilterConditionIsNotBlank>
    <StiFilterConditionIsNotNull>is not null</StiFilterConditionIsNotNull>
    <StiFilterConditionIsNull>is null</StiFilterConditionIsNull>
    <StiFilterConditionLessThan>less than</StiFilterConditionLessThan>
    <StiFilterConditionLessThanOrEqualTo>less than or equal to</StiFilterConditionLessThanOrEqualTo>
    <StiFilterConditionNotBetween>not between</StiFilterConditionNotBetween>
    <StiFilterConditionNotContaining>not containing</StiFilterConditionNotContaining>
    <StiFilterConditionNotEqualTo>not equal to</StiFilterConditionNotEqualTo>
    <StiFilterDataTypeBoolean>Boolean</StiFilterDataTypeBoolean>
    <StiFilterDataTypeDateTime>DateTime</StiFilterDataTypeDateTime>
    <StiFilterDataTypeExpression>Expression</StiFilterDataTypeExpression>
    <StiFilterDataTypeNumeric>Numeric</StiFilterDataTypeNumeric>
    <StiFilterDataTypeString>String</StiFilterDataTypeString>
    <StiFilterEngineReportEngine>Report Engine</StiFilterEngineReportEngine>
    <StiFilterEngineSQLQuery>SQL Query</StiFilterEngineSQLQuery>
    <StiFilterItemArgument>Argument</StiFilterItemArgument>
    <StiFilterItemExpression>Expression</StiFilterItemExpression>
    <StiFilterItemValue>Value</StiFilterItemValue>
    <StiFilterItemValueClose>Value Close</StiFilterItemValueClose>
    <StiFilterItemValueEnd>Value End</StiFilterItemValueEnd>
    <StiFilterItemValueHigh>Value High</StiFilterItemValueHigh>
    <StiFilterItemValueLow>Value Low</StiFilterItemValueLow>
    <StiFilterItemValueOpen>Value Open</StiFilterItemValueOpen>
    <StiFilterModeAnd>And</StiFilterModeAnd>
    <StiFilterModeOr>Or</StiFilterModeOr>
    <StiFontIconGroupAccessibilityIcons>Accessibility</StiFontIconGroupAccessibilityIcons>
    <StiFontIconGroupBrandIcons>Brand</StiFontIconGroupBrandIcons>
    <StiFontIconGroupDirectionalIcons>Directional</StiFontIconGroupDirectionalIcons>
    <StiFontIconGroupGenderIcons>Gender</StiFontIconGroupGenderIcons>
    <StiFontIconGroupMedicalIcons>Medical</StiFontIconGroupMedicalIcons>
    <StiFontIconGroupPaymentIcons>Payment</StiFontIconGroupPaymentIcons>
    <StiFontIconGroupSpinnerIcons>Spinner</StiFontIconGroupSpinnerIcons>
    <StiFontIconGroupTransportationIcons>Transportation</StiFontIconGroupTransportationIcons>
    <StiFontIconGroupVideoPlayerIcons>Video</StiFontIconGroupVideoPlayerIcons>
    <StiFontIconGroupWebApplicationIcons>Application</StiFontIconGroupWebApplicationIcons>
    <StiFontSizeModeAuto>Auto</StiFontSizeModeAuto>
    <StiFontSizeModeTarget>Target</StiFontSizeModeTarget>
    <StiFontSizeModeValue>Value</StiFontSizeModeValue>
    <StiFormStartModeOnEnd>On End</StiFormStartModeOnEnd>
    <StiFormStartModeOnPreview>On Preview</StiFormStartModeOnPreview>
    <StiFormStartModeOnStart>On Start</StiFormStartModeOnStart>
    <StiGaugeCalculationModeAuto>Auto</StiGaugeCalculationModeAuto>
    <StiGaugeCalculationModeCustom>Custom</StiGaugeCalculationModeCustom>
    <StiGaugeRangeModePercentage>Percentage</StiGaugeRangeModePercentage>
    <StiGaugeRangeModeValue>Value</StiGaugeRangeModeValue>
    <StiGaugeRangeTypeColor>Color</StiGaugeRangeTypeColor>
    <StiGaugeRangeTypeNone>None</StiGaugeRangeTypeNone>
    <StiGaugeTypeBullet>Bullet</StiGaugeTypeBullet>
    <StiGaugeTypeFullCircular>Full Circular</StiGaugeTypeFullCircular>
    <StiGaugeTypeHalfCircular>Half-Circular</StiGaugeTypeHalfCircular>
    <StiGaugeTypeHorizontalLinear>Horizontal Linear</StiGaugeTypeHorizontalLinear>
    <StiGaugeTypeLinear>Vertical Linear</StiGaugeTypeLinear>
    <StiGroupSortDirectionAscending>Ascending</StiGroupSortDirectionAscending>
    <StiGroupSortDirectionDescending>Descending</StiGroupSortDirectionDescending>
    <StiGroupSortDirectionNone>None</StiGroupSortDirectionNone>
    <StiHorAlignmentCenter>Center</StiHorAlignmentCenter>
    <StiHorAlignmentLeft>Left</StiHorAlignmentLeft>
    <StiHorAlignmentRight>Right</StiHorAlignmentRight>
    <StiIconAlignmentBottom>Bottom</StiIconAlignmentBottom>
    <StiIconAlignmentLeft>Left</StiIconAlignmentLeft>
    <StiIconAlignmentNone>None</StiIconAlignmentNone>
    <StiIconAlignmentRight>Right</StiIconAlignmentRight>
    <StiIconAlignmentTop>Top</StiIconAlignmentTop>
    <StiImageProcessingDuplicatesTypeGlobalHide>Global Hide</StiImageProcessingDuplicatesTypeGlobalHide>
    <StiImageProcessingDuplicatesTypeGlobalMerge>Global Merge</StiImageProcessingDuplicatesTypeGlobalMerge>
    <StiImageProcessingDuplicatesTypeGlobalRemoveImage>Global Remove Image</StiImageProcessingDuplicatesTypeGlobalRemoveImage>
    <StiImageProcessingDuplicatesTypeHide>Hide</StiImageProcessingDuplicatesTypeHide>
    <StiImageProcessingDuplicatesTypeMerge>Merge</StiImageProcessingDuplicatesTypeMerge>
    <StiImageProcessingDuplicatesTypeNone>None</StiImageProcessingDuplicatesTypeNone>
    <StiImageProcessingDuplicatesTypeRemoveImage>Remove Image</StiImageProcessingDuplicatesTypeRemoveImage>
    <StiImageRotationFlipHorizontal>Flip Horizontal</StiImageRotationFlipHorizontal>
    <StiImageRotationFlipVertical>Flip Vertical</StiImageRotationFlipVertical>
    <StiImageRotationNone>None</StiImageRotationNone>
    <StiImageRotationRotate180>Rotate 180°</StiImageRotationRotate180>
    <StiImageRotationRotate90CCW>Rotate 90° CCW</StiImageRotationRotate90CCW>
    <StiImageRotationRotate90CW>Rotate 90° CW</StiImageRotationRotate90CW>
    <StiInteractionOnClick>None</StiInteractionOnClick>
    <StiInteractionOnClickApplyFilter>Apply Filter</StiInteractionOnClickApplyFilter>
    <StiInteractionOnClickDrillDown>Drill-Down</StiInteractionOnClickDrillDown>
    <StiInteractionOnClickOpenHyperlink>Open Hyperlink</StiInteractionOnClickOpenHyperlink>
    <StiInteractionOnClickShowDashboard>Show Dashboard</StiInteractionOnClickShowDashboard>
    <StiInteractionOnHoverNone>None</StiInteractionOnHoverNone>
    <StiInteractionOnHoverShowHyperlink>Show Hyperlink</StiInteractionOnHoverShowHyperlink>
    <StiInteractionOnHoverShowToolTip>Show Tool Tip</StiInteractionOnHoverShowToolTip>
    <StiInteractionOpenHyperlinkDestinationCurrentTab>Current Tab</StiInteractionOpenHyperlinkDestinationCurrentTab>
    <StiInteractionOpenHyperlinkDestinationNewTab>New Tab</StiInteractionOpenHyperlinkDestinationNewTab>
    <StiItemFilterModeFullPath>Full Path</StiItemFilterModeFullPath>
    <StiItemFilterModeSelectedOnly>Selected Only</StiItemFilterModeSelectedOnly>
    <StiItemOrientationHorizontal>Horizontal</StiItemOrientationHorizontal>
    <StiItemOrientationVertical>Vertical</StiItemOrientationVertical>
    <StiItemSelectionModeMulti>Multi</StiItemSelectionModeMulti>
    <StiItemSelectionModeOne>One</StiItemSelectionModeOne>
    <StiKeepDetailsKeepDetailsTogether>Keep Details Together</StiKeepDetailsKeepDetailsTogether>
    <StiKeepDetailsKeepFirstDetailTogether>Keep First Detail Together</StiKeepDetailsKeepFirstDetailTogether>
    <StiKeepDetailsKeepFirstRowTogether>Keep First Row Together</StiKeepDetailsKeepFirstRowTogether>
    <StiKeepDetailsNone>None</StiKeepDetailsNone>
    <StiLabelPlacementInside>Inside</StiLabelPlacementInside>
    <StiLabelPlacementOutside>Outside</StiLabelPlacementOutside>
    <StiLabelsPlacementAutoRotation>Auto Rotation</StiLabelsPlacementAutoRotation>
    <StiLabelsPlacementNone>None</StiLabelsPlacementNone>
    <StiLabelsPlacementOneLine>One Line</StiLabelsPlacementOneLine>
    <StiLabelsPlacementTwoLines>Two Lines</StiLabelsPlacementTwoLines>
    <StiLegendDirectionBottomToTop>Bottom to Top</StiLegendDirectionBottomToTop>
    <StiLegendDirectionLeftToRight>Left to Right</StiLegendDirectionLeftToRight>
    <StiLegendDirectionRightToLeft>Right to Left</StiLegendDirectionRightToLeft>
    <StiLegendDirectionTopToBottom>Top to Bottom</StiLegendDirectionTopToBottom>
    <StiLegendHorAlignmentCenter>Center</StiLegendHorAlignmentCenter>
    <StiLegendHorAlignmentLeft>Left</StiLegendHorAlignmentLeft>
    <StiLegendHorAlignmentLeftOutside>Left Outside</StiLegendHorAlignmentLeftOutside>
    <StiLegendHorAlignmentRight>Right</StiLegendHorAlignmentRight>
    <StiLegendHorAlignmentRightOutside>Right Outside</StiLegendHorAlignmentRightOutside>
    <StiLegendVertAlignmentBottom>Bottom</StiLegendVertAlignmentBottom>
    <StiLegendVertAlignmentBottomOutside>Bottom Outside</StiLegendVertAlignmentBottomOutside>
    <StiLegendVertAlignmentCenter>Center</StiLegendVertAlignmentCenter>
    <StiLegendVertAlignmentTop>Top</StiLegendVertAlignmentTop>
    <StiLegendVertAlignmentTopOutside>Top Outside</StiLegendVertAlignmentTopOutside>
    <StiLineWeightNormal>Normal</StiLineWeightNormal>
    <StiLineWeightThick>Thick</StiLineWeightThick>
    <StiLineWeightThin>Thin</StiLineWeightThin>
    <StiMapModeChoropleth>Choropleth</StiMapModeChoropleth>
    <StiMapModeOnline>Online</StiMapModeOnline>
    <StiMapTypeGroup>Group</StiMapTypeGroup>
    <StiMapTypeHeatmap>Heatmap</StiMapTypeHeatmap>
    <StiMapTypeHeatmapWithGroup>Heatmap With Group</StiMapTypeHeatmapWithGroup>
    <StiMapTypeIndividual>Individual</StiMapTypeIndividual>
    <StiMapTypeNone>None</StiMapTypeNone>
    <StiMapTypePoints>Points</StiMapTypePoints>
    <StiMarkerAlignmentCenter>Center</StiMarkerAlignmentCenter>
    <StiMarkerAlignmentLeft>Left</StiMarkerAlignmentLeft>
    <StiMarkerAlignmentRight>Right</StiMarkerAlignmentRight>
    <StiMarkerTypeCircle>Circle</StiMarkerTypeCircle>
    <StiMarkerTypeHalfCircle>Half Circle</StiMarkerTypeHalfCircle>
    <StiMarkerTypeHexagon>Hexagon</StiMarkerTypeHexagon>
    <StiMarkerTypeRectangle>Rectangle</StiMarkerTypeRectangle>
    <StiMarkerTypeStar5>Star 5</StiMarkerTypeStar5>
    <StiMarkerTypeStar6>Star 6</StiMarkerTypeStar6>
    <StiMarkerTypeStar7>Star 7</StiMarkerTypeStar7>
    <StiMarkerTypeStar8>Star 8</StiMarkerTypeStar8>
    <StiMarkerTypeTriangle>Triangle</StiMarkerTypeTriangle>
    <StiNestedFactorHigh>High</StiNestedFactorHigh>
    <StiNestedFactorLow>Low</StiNestedFactorLow>
    <StiNestedFactorNormal>Normal</StiNestedFactorNormal>
    <StiNumberOfPassDoublePass>Double Pass</StiNumberOfPassDoublePass>
    <StiNumberOfPassSinglePass>Single Pass</StiNumberOfPassSinglePass>
    <StiOnlineMapHeatmapColorGradientTypeAddRange>Add Range</StiOnlineMapHeatmapColorGradientTypeAddRange>
    <StiOnlineMapHeatmapColorGradientTypeBlackAquaWhite>Black Aqua White</StiOnlineMapHeatmapColorGradientTypeBlackAquaWhite>
    <StiOnlineMapHeatmapColorGradientTypeBlueRed>Blue Red</StiOnlineMapHeatmapColorGradientTypeBlueRed>
    <StiOnlineMapHeatmapColorGradientTypeColorSpectrum>Color Spectrum</StiOnlineMapHeatmapColorGradientTypeColorSpectrum>
    <StiOnlineMapHeatmapColorGradientTypeDeepSea>Deep Sea</StiOnlineMapHeatmapColorGradientTypeDeepSea>
    <StiOnlineMapHeatmapColorGradientTypeHeatedMetal>Heated Metal</StiOnlineMapHeatmapColorGradientTypeHeatedMetal>
    <StiOnlineMapHeatmapColorGradientTypeIncandescent>Incandescent</StiOnlineMapHeatmapColorGradientTypeIncandescent>
    <StiOnlineMapHeatmapColorGradientTypeSteppedColors>Stepped Colors</StiOnlineMapHeatmapColorGradientTypeSteppedColors>
    <StiOnlineMapHeatmapColorGradientTypeSunrise>Sunrise</StiOnlineMapHeatmapColorGradientTypeSunrise>
    <StiOnlineMapHeatmapColorGradientTypeVisibleSpectrum>Visible Spectrum</StiOnlineMapHeatmapColorGradientTypeVisibleSpectrum>
    <StiOnlineMapLocationTypeAdminDivision1>AdminDivision1</StiOnlineMapLocationTypeAdminDivision1>
    <StiOnlineMapLocationTypeAdminDivision2>AdminDivision2</StiOnlineMapLocationTypeAdminDivision2>
    <StiOnlineMapLocationTypeAuto>Auto</StiOnlineMapLocationTypeAuto>
    <StiOnlineMapLocationTypeCountryRegion>CountryRegion</StiOnlineMapLocationTypeCountryRegion>
    <StiOnlineMapLocationTypeNeighborhood>Neighborhood</StiOnlineMapLocationTypeNeighborhood>
    <StiOnlineMapLocationTypePopulatedPlace>PopulatedPlace</StiOnlineMapLocationTypePopulatedPlace>
    <StiOnlineMapLocationTypePostcode1>Postcode1</StiOnlineMapLocationTypePostcode1>
    <StiOnlineMapLocationTypePostcode2>Postcode2</StiOnlineMapLocationTypePostcode2>
    <StiOnlineMapLocationTypePostcode3>Postcode3</StiOnlineMapLocationTypePostcode3>
    <StiOnlineMapLocationTypePostcode4>Postcode4</StiOnlineMapLocationTypePostcode4>
    <StiOrientationHorizontal>Horizontal</StiOrientationHorizontal>
    <StiOrientationHorizontalRight>Horizontal Right</StiOrientationHorizontalRight>
    <StiOrientationVertical>Vertical</StiOrientationVertical>
    <StiPageOrientationLandscape>Landscape</StiPageOrientationLandscape>
    <StiPageOrientationPortrait>Portrait</StiPageOrientationPortrait>
    <StiPdfDigitalSignatureAppearanceTypeDraw>Draw</StiPdfDigitalSignatureAppearanceTypeDraw>
    <StiPdfDigitalSignatureAppearanceTypeImage>Image</StiPdfDigitalSignatureAppearanceTypeImage>
    <StiPdfDigitalSignatureAppearanceTypeNone>None</StiPdfDigitalSignatureAppearanceTypeNone>
    <StiPdfDigitalSignatureAppearanceTypeText>Text</StiPdfDigitalSignatureAppearanceTypeText>
    <StiPenStyleDash>Dash</StiPenStyleDash>
    <StiPenStyleDashDot>Dash Dot</StiPenStyleDashDot>
    <StiPenStyleDashDotDot>Dash Dot Dot</StiPenStyleDashDotDot>
    <StiPenStyleDot>Dot</StiPenStyleDot>
    <StiPenStyleDouble>Double</StiPenStyleDouble>
    <StiPenStyleNone>None</StiPenStyleNone>
    <StiPenStyleSolid>Solid</StiPenStyleSolid>
    <StiPlacementInside>Inside</StiPlacementInside>
    <StiPlacementOutside>Outside</StiPlacementOutside>
    <StiPlacementOverlay>Overlay</StiPlacementOverlay>
    <StiPlesseyCheckSumModulo10>Modulo10</StiPlesseyCheckSumModulo10>
    <StiPlesseyCheckSumModulo11>Modulo11</StiPlesseyCheckSumModulo11>
    <StiPlesseyCheckSumNone>None</StiPlesseyCheckSumNone>
    <StiPreviewModeDotMatrix>Dot-Matrix</StiPreviewModeDotMatrix>
    <StiPreviewModeStandard>Standard</StiPreviewModeStandard>
    <StiPreviewModeStandardAndDotMatrix>Standard and Dot-Matrix</StiPreviewModeStandardAndDotMatrix>
    <StiPrintOnEvenOddPagesTypeIgnore>Ignore</StiPrintOnEvenOddPagesTypeIgnore>
    <StiPrintOnEvenOddPagesTypePrintOnEvenPages>Print on Even Pages</StiPrintOnEvenOddPagesTypePrintOnEvenPages>
    <StiPrintOnEvenOddPagesTypePrintOnOddPages>Print on Odd Pages</StiPrintOnEvenOddPagesTypePrintOnOddPages>
    <StiPrintOnTypeAllPages>All Pages</StiPrintOnTypeAllPages>
    <StiPrintOnTypeExceptFirstAndLastPage>Except First and Last Page</StiPrintOnTypeExceptFirstAndLastPage>
    <StiPrintOnTypeExceptFirstPage>Except First Page</StiPrintOnTypeExceptFirstPage>
    <StiPrintOnTypeExceptLastPage>Except Last Page</StiPrintOnTypeExceptLastPage>
    <StiPrintOnTypeOnlyFirstAndLastPage>Only First and Last Page</StiPrintOnTypeOnlyFirstAndLastPage>
    <StiPrintOnTypeOnlyFirstPage>Only First Page</StiPrintOnTypeOnlyFirstPage>
    <StiPrintOnTypeOnlyLastPage>Only Last Page</StiPrintOnTypeOnlyLastPage>
    <StiProcessAtEndOfPage>End of Page</StiProcessAtEndOfPage>
    <StiProcessAtEndOfReport>End of Report</StiProcessAtEndOfReport>
    <StiProcessAtNone>None</StiProcessAtNone>
    <StiProcessingDuplicatesTypeBasedOnTagHide>Hide based on Tag</StiProcessingDuplicatesTypeBasedOnTagHide>
    <StiProcessingDuplicatesTypeBasedOnTagMerge>Merge based on Tag</StiProcessingDuplicatesTypeBasedOnTagMerge>
    <StiProcessingDuplicatesTypeBasedOnTagRemoveText>Remove Text based on Tag</StiProcessingDuplicatesTypeBasedOnTagRemoveText>
    <StiProcessingDuplicatesTypeBasedOnValueAndTagHide>Hide based on Value and Tag</StiProcessingDuplicatesTypeBasedOnValueAndTagHide>
    <StiProcessingDuplicatesTypeBasedOnValueAndTagMerge>Merge based on Value and Tag</StiProcessingDuplicatesTypeBasedOnValueAndTagMerge>
    <StiProcessingDuplicatesTypeBasedOnValueRemoveText>Remove based on Value Text</StiProcessingDuplicatesTypeBasedOnValueRemoveText>
    <StiProcessingDuplicatesTypeGlobalBasedOnValueAndTagHide>Global Hide based on Value and Tag</StiProcessingDuplicatesTypeGlobalBasedOnValueAndTagHide>
    <StiProcessingDuplicatesTypeGlobalBasedOnValueAndTagMerge>Global Merge based on Value and Tag</StiProcessingDuplicatesTypeGlobalBasedOnValueAndTagMerge>
    <StiProcessingDuplicatesTypeGlobalBasedOnValueRemoveText>Global Remove based on Value Text</StiProcessingDuplicatesTypeGlobalBasedOnValueRemoveText>
    <StiProcessingDuplicatesTypeGlobalHide>Global Hide</StiProcessingDuplicatesTypeGlobalHide>
    <StiProcessingDuplicatesTypeGlobalMerge>Global Merge</StiProcessingDuplicatesTypeGlobalMerge>
    <StiProcessingDuplicatesTypeGlobalRemoveText>Global Remove Text</StiProcessingDuplicatesTypeGlobalRemoveText>
    <StiProcessingDuplicatesTypeHide>Hide</StiProcessingDuplicatesTypeHide>
    <StiProcessingDuplicatesTypeMerge>Merge</StiProcessingDuplicatesTypeMerge>
    <StiProcessingDuplicatesTypeNone>None</StiProcessingDuplicatesTypeNone>
    <StiProcessingDuplicatesTypeRemoveText>Remove Text</StiProcessingDuplicatesTypeRemoveText>
    <StiProgressElementModeCircle>Circle</StiProgressElementModeCircle>
    <StiProgressElementModeDataBars>DataBars</StiProgressElementModeDataBars>
    <StiProgressElementModePie>Pie</StiProgressElementModePie>
    <StiQRCodeBodyShapeTypeCircle>Circle</StiQRCodeBodyShapeTypeCircle>
    <StiQRCodeBodyShapeTypeCircular>Circular</StiQRCodeBodyShapeTypeCircular>
    <StiQRCodeBodyShapeTypeDiamond>Diamond</StiQRCodeBodyShapeTypeDiamond>
    <StiQRCodeBodyShapeTypeDockedDiamonds>Docked Diamonds</StiQRCodeBodyShapeTypeDockedDiamonds>
    <StiQRCodeBodyShapeTypeDot>Dot</StiQRCodeBodyShapeTypeDot>
    <StiQRCodeBodyShapeTypeRoundedSquare>Rounded Square</StiQRCodeBodyShapeTypeRoundedSquare>
    <StiQRCodeBodyShapeTypeSquare>Square</StiQRCodeBodyShapeTypeSquare>
    <StiQRCodeBodyShapeTypeStar>Star</StiQRCodeBodyShapeTypeStar>
    <StiQRCodeBodyShapeTypeZebraCross>Zebra Cross</StiQRCodeBodyShapeTypeZebraCross>
    <StiQRCodeBodyShapeTypeZebraHorizontal>Zebra Horizontal</StiQRCodeBodyShapeTypeZebraHorizontal>
    <StiQRCodeBodyShapeTypeZebraVertical>Zebra Vertical</StiQRCodeBodyShapeTypeZebraVertical>
    <StiQRCodeEyeBallShapeTypeCircle>Circle</StiQRCodeEyeBallShapeTypeCircle>
    <StiQRCodeEyeBallShapeTypeDots>Dots</StiQRCodeEyeBallShapeTypeDots>
    <StiQRCodeEyeBallShapeTypeRound>Round</StiQRCodeEyeBallShapeTypeRound>
    <StiQRCodeEyeBallShapeTypeSquare>Square</StiQRCodeEyeBallShapeTypeSquare>
    <StiQRCodeEyeBallShapeTypeStar>Star</StiQRCodeEyeBallShapeTypeStar>
    <StiQRCodeEyeBallShapeTypeZebraHorizontal>Zebra Horizontal</StiQRCodeEyeBallShapeTypeZebraHorizontal>
    <StiQRCodeEyeBallShapeTypeZebraVertical>Zebra Vertical</StiQRCodeEyeBallShapeTypeZebraVertical>
    <StiRadarStyleXFCircle>Circle</StiRadarStyleXFCircle>
    <StiRadarStyleXFPolygon>Polygon</StiRadarStyleXFPolygon>
    <StiReportCacheModeAuto>Auto</StiReportCacheModeAuto>
    <StiReportCacheModeOff>Off</StiReportCacheModeOff>
    <StiReportCacheModeOn>On</StiReportCacheModeOn>
    <StiReportUnitTypeCentimeters>Centimeters</StiReportUnitTypeCentimeters>
    <StiReportUnitTypeHundredthsOfInch>Hundredths of Inch</StiReportUnitTypeHundredthsOfInch>
    <StiReportUnitTypeInches>Inches</StiReportUnitTypeInches>
    <StiReportUnitTypeMillimeters>Millimeters</StiReportUnitTypeMillimeters>
    <StiReportUnitTypePixels>Pixels</StiReportUnitTypePixels>
    <StiReportUnitTypePoints>Points</StiReportUnitTypePoints>
    <StiRestrictionsAll>All</StiRestrictionsAll>
    <StiRestrictionsAllowChange>Allow Change</StiRestrictionsAllowChange>
    <StiRestrictionsAllowDelete>Allow Delete</StiRestrictionsAllowDelete>
    <StiRestrictionsAllowMove>Allow Move</StiRestrictionsAllowMove>
    <StiRestrictionsAllowResize>Allow Resize</StiRestrictionsAllowResize>
    <StiRestrictionsAllowSelect>Allow Select</StiRestrictionsAllowSelect>
    <StiRestrictionsNone>None</StiRestrictionsNone>
    <StiSelectionModeFirst>First</StiSelectionModeFirst>
    <StiSelectionModeFromVariable>From Variable</StiSelectionModeFromVariable>
    <StiSelectionModeNothing>Nothing</StiSelectionModeNothing>
    <StiSeriesLabelsValueTypeArgument>Argument</StiSeriesLabelsValueTypeArgument>
    <StiSeriesLabelsValueTypeArgumentValue>Argument - Value</StiSeriesLabelsValueTypeArgumentValue>
    <StiSeriesLabelsValueTypeSeriesTitle>Series Title</StiSeriesLabelsValueTypeSeriesTitle>
    <StiSeriesLabelsValueTypeSeriesTitleArgument>Series Title - Argument</StiSeriesLabelsValueTypeSeriesTitleArgument>
    <StiSeriesLabelsValueTypeSeriesTitleValue>Series Title - Value</StiSeriesLabelsValueTypeSeriesTitleValue>
    <StiSeriesLabelsValueTypeTag>Tag</StiSeriesLabelsValueTypeTag>
    <StiSeriesLabelsValueTypeValue>Value</StiSeriesLabelsValueTypeValue>
    <StiSeriesLabelsValueTypeValueArgument>Value - Argument</StiSeriesLabelsValueTypeValueArgument>
    <StiSeriesLabelsValueTypeWeight>Weight</StiSeriesLabelsValueTypeWeight>
    <StiSeriesSortDirectionAscending>Ascending</StiSeriesSortDirectionAscending>
    <StiSeriesSortDirectionDescending>Descending</StiSeriesSortDirectionDescending>
    <StiSeriesSortTypeArgument>Argument</StiSeriesSortTypeArgument>
    <StiSeriesSortTypeNone>None</StiSeriesSortTypeNone>
    <StiSeriesSortTypeValue>Value</StiSeriesSortTypeValue>
    <StiSeriesXAxisBottomXAxis>Bottom X Axis</StiSeriesXAxisBottomXAxis>
    <StiSeriesXAxisTopXAxis>Top X Axis</StiSeriesXAxisTopXAxis>
    <StiSeriesYAxisLeftYAxis>Left Y Axis</StiSeriesYAxisLeftYAxis>
    <StiSeriesYAxisRightYAxis>Right Y Axis</StiSeriesYAxisRightYAxis>
    <StiShapeDirectionDown>Down</StiShapeDirectionDown>
    <StiShapeDirectionLeft>Left</StiShapeDirectionLeft>
    <StiShapeDirectionRight>Right</StiShapeDirectionRight>
    <StiShapeDirectionUp>Up</StiShapeDirectionUp>
    <StiShiftModeDecreasingSize>Decreasing Size</StiShiftModeDecreasingSize>
    <StiShiftModeIncreasingSize>Increasing Size</StiShiftModeIncreasingSize>
    <StiShiftModeNone>None</StiShiftModeNone>
    <StiShiftModeOnlyInWidthOfComponent>Only in Width of Component</StiShiftModeOnlyInWidthOfComponent>
    <StiShowEmptyCellsAsConnectPointsWithLine>Connect Points with Line</StiShowEmptyCellsAsConnectPointsWithLine>
    <StiShowEmptyCellsAsGap>Gap</StiShowEmptyCellsAsGap>
    <StiShowEmptyCellsAsZero>Zero</StiShowEmptyCellsAsZero>
    <StiShowSeriesLabelsFromChart>From Chart</StiShowSeriesLabelsFromChart>
    <StiShowSeriesLabelsFromSeries>From Series</StiShowSeriesLabelsFromSeries>
    <StiShowSeriesLabelsNone>None</StiShowSeriesLabelsNone>
    <StiShowXAxisBoth>Both</StiShowXAxisBoth>
    <StiShowXAxisBottom>Bottom</StiShowXAxisBottom>
    <StiShowXAxisCenter>Center</StiShowXAxisCenter>
    <StiShowYAxisBoth>Both</StiShowYAxisBoth>
    <StiShowYAxisCenter>Center</StiShowYAxisCenter>
    <StiShowYAxisLeft>Left</StiShowYAxisLeft>
    <StiSignatureModeDraw>Draw</StiSignatureModeDraw>
    <StiSignatureModeType>Type</StiSignatureModeType>
    <StiSignatureTypeDigitalSignature>Digital Signature</StiSignatureTypeDigitalSignature>
    <StiSignatureTypeDraw>Draw</StiSignatureTypeDraw>
    <StiSignatureTypeType>Type</StiSignatureTypeType>
    <StiSizeModeAutoSize>Auto Size</StiSizeModeAutoSize>
    <StiSizeModeFit>Fit</StiSizeModeFit>
    <StiSortDirectionAsc>Ascending</StiSortDirectionAsc>
    <StiSortDirectionDesc>Descending</StiSortDirectionDesc>
    <StiSortDirectionNone>None</StiSortDirectionNone>
    <StiSortTypeByDisplayValue>by Display Value</StiSortTypeByDisplayValue>
    <StiSortTypeByValue>by Value</StiSortTypeByValue>
    <StiSqlSourceTypeStoredProcedure>Stored Procedure</StiSqlSourceTypeStoredProcedure>
    <StiSqlSourceTypeTable>Query</StiSqlSourceTypeTable>
    <StiStyleComponentTypeChart>Chart</StiStyleComponentTypeChart>
    <StiStyleComponentTypeCheckBox>Check Box</StiStyleComponentTypeCheckBox>
    <StiStyleComponentTypeCrossTab>Cross-Tab</StiStyleComponentTypeCrossTab>
    <StiStyleComponentTypeImage>Image</StiStyleComponentTypeImage>
    <StiStyleComponentTypePrimitive>Primitive</StiStyleComponentTypePrimitive>
    <StiStyleComponentTypeText>Text</StiStyleComponentTypeText>
    <StiStyleConditionTypeComponentName>Component Name</StiStyleConditionTypeComponentName>
    <StiStyleConditionTypeComponentType>Component Type</StiStyleConditionTypeComponentType>
    <StiStyleConditionTypeLocation>Location</StiStyleConditionTypeLocation>
    <StiStyleConditionTypePlacement>Placement</StiStyleConditionTypePlacement>
    <StiSummaryValuesAllValues>All Values</StiSummaryValuesAllValues>
    <StiSummaryValuesSkipNulls>Skip Nulls</StiSummaryValuesSkipNulls>
    <StiSummaryValuesSkipZerosAndNulls>Skip Zeros and Nulls</StiSummaryValuesSkipZerosAndNulls>
    <StiTablceCellTypeCheckBox>CheckBox</StiTablceCellTypeCheckBox>
    <StiTablceCellTypeImage>Image</StiTablceCellTypeImage>
    <StiTablceCellTypeRichText>RichText</StiTablceCellTypeRichText>
    <StiTablceCellTypeText>Text</StiTablceCellTypeText>
    <StiTableAutoWidthNone>None</StiTableAutoWidthNone>
    <StiTableAutoWidthPage>Page</StiTableAutoWidthPage>
    <StiTableAutoWidthTable>Table</StiTableAutoWidthTable>
    <StiTableAutoWidthTypeFullTable>Full Table</StiTableAutoWidthTypeFullTable>
    <StiTableAutoWidthTypeLastColumns>Last Columns</StiTableAutoWidthTypeLastColumns>
    <StiTableAutoWidthTypeNone>None</StiTableAutoWidthTypeNone>
    <StiTableSizeModeAutoSize>Auto Size</StiTableSizeModeAutoSize>
    <StiTableSizeModeFit>Fit</StiTableSizeModeFit>
    <StiTargetModePercentage>Percentage</StiTargetModePercentage>
    <StiTargetModeVariation>Variation</StiTargetModeVariation>
    <StiTextHorAlignmentCenter>Center</StiTextHorAlignmentCenter>
    <StiTextHorAlignmentLeft>Left</StiTextHorAlignmentLeft>
    <StiTextHorAlignmentRight>Right</StiTextHorAlignmentRight>
    <StiTextHorAlignmentWidth>Width</StiTextHorAlignmentWidth>
    <StiTextPositionCenterBottom>Center Bottom</StiTextPositionCenterBottom>
    <StiTextPositionCenterTop>Center Top</StiTextPositionCenterTop>
    <StiTextPositionLeftBottom>Left Bottom</StiTextPositionLeftBottom>
    <StiTextPositionLeftTop>Left Top</StiTextPositionLeftTop>
    <StiTextPositionRightBottom>Right Bottom</StiTextPositionRightBottom>
    <StiTextPositionRightTop>Right Top</StiTextPositionRightTop>
    <StiTextQualityStandard>Standard</StiTextQualityStandard>
    <StiTextQualityTypographic>Typographic</StiTextQualityTypographic>
    <StiTextQualityWysiwyg>Wysiwyg</StiTextQualityWysiwyg>
    <StiTextSizeModeFit>Fit</StiTextSizeModeFit>
    <StiTextSizeModeFitAndWordWrap>Fit and Word Wrap</StiTextSizeModeFitAndWordWrap>
    <StiTextSizeModeTrimming>Trimming</StiTextSizeModeTrimming>
    <StiTextSizeModeWordWrap>Word Wrap</StiTextSizeModeWordWrap>
    <StiThemeAppearanceAuto>Auto</StiThemeAppearanceAuto>
    <StiThemeAppearanceDark>Dark</StiThemeAppearanceDark>
    <StiThemeAppearanceLight>Light</StiThemeAppearanceLight>
    <StiThemeAppearanceLightGray>Light Gray</StiThemeAppearanceLightGray>
    <StiTimeDateStepDay>Day</StiTimeDateStepDay>
    <StiTimeDateStepHour>Hour</StiTimeDateStepHour>
    <StiTimeDateStepMinute>Minute</StiTimeDateStepMinute>
    <StiTimeDateStepMonth>Month</StiTimeDateStepMonth>
    <StiTimeDateStepNone>None</StiTimeDateStepNone>
    <StiTimeDateStepSecond>Second</StiTimeDateStepSecond>
    <StiTimeDateStepYear>Year</StiTimeDateStepYear>
    <StiTitlePositionInside>Inside</StiTitlePositionInside>
    <StiTitlePositionOutside>Outside</StiTitlePositionOutside>
    <StiTopNModeBottom>Bottom</StiTopNModeBottom>
    <StiTopNModeNone>None</StiTopNModeNone>
    <StiTopNModeTop>Top</StiTopNModeTop>
    <StiTypeModeList>List</StiTypeModeList>
    <StiTypeModeNullableValue>Nullable Value</StiTypeModeNullableValue>
    <StiTypeModeRange>Range</StiTypeModeRange>
    <StiTypeModeValue>Value</StiTypeModeValue>
    <StiUnitAlignmentPrefix>Prefix</StiUnitAlignmentPrefix>
    <StiUnitAlignmentPrefixInside>Prefix Inside</StiUnitAlignmentPrefixInside>
    <StiUnitAlignmentPrefixInsideWithSpace>Prefix Inside with Space</StiUnitAlignmentPrefixInsideWithSpace>
    <StiUnitAlignmentSuffix>Suffix</StiUnitAlignmentSuffix>
    <StiUnitAlignmentSuffixInside>Suffix Inside</StiUnitAlignmentSuffixInside>
    <StiUnitAlignmentSuffixInsideWithSpace>Suffix Inside with Space</StiUnitAlignmentSuffixInsideWithSpace>
    <StiVertAlignmentBottom>Bottom</StiVertAlignmentBottom>
    <StiVertAlignmentCenter>Center</StiVertAlignmentCenter>
    <StiVertAlignmentTop>Top</StiVertAlignmentTop>
    <StiViewModeNormal>Normal</StiViewModeNormal>
    <StiViewModePageBreakPreview>Page Break Preview</StiViewModePageBreakPreview>
    <StiXmlTypeAdoNetXml>ADO.NET XML</StiXmlTypeAdoNetXml>
    <StiXmlTypeXml>XML</StiXmlTypeXml>
    <StringAlignmentCenter>Center</StringAlignmentCenter>
    <StringAlignmentFar>Far</StringAlignmentFar>
    <StringAlignmentNear>Near</StringAlignmentNear>
    <StringTrimmingCharacter>Character</StringTrimmingCharacter>
    <StringTrimmingEllipsisCharacter>Ellipsis Character</StringTrimmingEllipsisCharacter>
    <StringTrimmingEllipsisPath>Ellipsis Path</StringTrimmingEllipsisPath>
    <StringTrimmingEllipsisWord>Ellipsis Word</StringTrimmingEllipsisWord>
    <StringTrimmingNone>None</StringTrimmingNone>
    <StringTrimmingWord>Word</StringTrimmingWord>
  </PropertyEnum>
  <PropertyEvents>
    <AfterPrintEvent>After Print</AfterPrintEvent>
    <AfterSelectEvent>After Select</AfterSelectEvent>
    <BeforePrintEvent>Before Print</BeforePrintEvent>
    <BeginRenderEvent>Begin Render</BeginRenderEvent>
    <CheckedChangedEvent>Checked Changed</CheckedChangedEvent>
    <ClickEvent>Click</ClickEvent>
    <ClosedFormEvent>Closed Form</ClosedFormEvent>
    <ClosingFormEvent>Closing Form</ClosingFormEvent>
    <ColumnBeginRenderEvent>Column Begin Render</ColumnBeginRenderEvent>
    <ColumnEndRenderEvent>Column End Render</ColumnEndRenderEvent>
    <ConnectedEvent>ConnectedEvent</ConnectedEvent>
    <ConnectingEvent>ConnectingEvent</ConnectingEvent>
    <DisconnectedEvent>Disconnected</DisconnectedEvent>
    <DisconnectingEvent>Disconnecting</DisconnectingEvent>
    <DoubleClickEvent>Double Click</DoubleClickEvent>
    <EndRenderEvent>End Render</EndRenderEvent>
    <EnterEvent>Enter</EnterEvent>
    <ExportedEvent>Exported</ExportedEvent>
    <ExportingEvent>Exporting</ExportingEvent>
    <FillParametersEvent>Fill Parameters</FillParametersEvent>
    <GetArgumentEvent>Get Argument</GetArgumentEvent>
    <GetBarCodeEvent>Get BarCode</GetBarCodeEvent>
    <GetBookmarkEvent>Get Bookmark</GetBookmarkEvent>
    <GetCheckedEvent>Get Checked</GetCheckedEvent>
    <GetCollapsedEvent>Get Collapsed</GetCollapsedEvent>
    <GetCrossValueEvent>Get Cross Value</GetCrossValueEvent>
    <GetCutPieListEvent>Get Cut Pie List</GetCutPieListEvent>
    <GetDataUrlEvent>Get DataUrl</GetDataUrlEvent>
    <GetDisplayCrossValueEvent>Get Display Cross Value</GetDisplayCrossValueEvent>
    <GetDrillDownReportEvent>Get Drill-Down Report</GetDrillDownReportEvent>
    <GetExcelSheetEvent>Get Excel Sheet</GetExcelSheetEvent>
    <GetExcelValueEvent>Get Excel Value</GetExcelValueEvent>
    <GetHyperlinkEvent>Get Hyperlink</GetHyperlinkEvent>
    <GetImageDataEvent>Get Image Data</GetImageDataEvent>
    <GetImageURLEvent>Get Image URL</GetImageURLEvent>
    <GetListOfArgumentsEvent>Get List of Arguments</GetListOfArgumentsEvent>
    <GetListOfHyperlinksEvent>Get List of Hyperlinks</GetListOfHyperlinksEvent>
    <GetListOfTagsEvent>Get List of Tags</GetListOfTagsEvent>
    <GetListOfToolTipsEvent>Get List of Tool Tips</GetListOfToolTipsEvent>
    <GetListOfValuesEndEvent>Get List of Values End</GetListOfValuesEndEvent>
    <GetListOfValuesEvent>Get List of Values</GetListOfValuesEvent>
    <GetListOfWeights>Get List of Weights</GetListOfWeights>
    <GetListOfWeightsEvent>Get List of Weights</GetListOfWeightsEvent>
    <GetPointerEvent>Get Pointer</GetPointerEvent>
    <GetSummaryExpressionEvent>Get Summary Expression</GetSummaryExpressionEvent>
    <GetTagEvent>Get Tag</GetTagEvent>
    <GetTitleEvent>Get Title</GetTitleEvent>
    <GetToolTipEvent>Get Tool Tip</GetToolTipEvent>
    <GetValueEndEvent>Get Value End</GetValueEndEvent>
    <GetValueEvent>Get Value</GetValueEvent>
    <GetWeightEvent>Get Weight</GetWeightEvent>
    <LeaveEvent>Leave</LeaveEvent>
    <LoadFormEvent>Load Form</LoadFormEvent>
    <MouseDownEvent>Mouse Down</MouseDownEvent>
    <MouseEnterEvent>Mouse Enter</MouseEnterEvent>
    <MouseLeaveEvent>Mouse Leave</MouseLeaveEvent>
    <MouseMoveEvent>Mouse Move</MouseMoveEvent>
    <MouseUpEvent>Mouse Up</MouseUpEvent>
    <NewAutoSeriesEvent>New Auto Series</NewAutoSeriesEvent>
    <PositionChangedEvent>Position Changed</PositionChangedEvent>
    <PrintedEvent>Printed</PrintedEvent>
    <PrintingEvent>Printing</PrintingEvent>
    <ProcessCellEvent>Process Cell</ProcessCellEvent>
    <ProcessChartEvent>Process Chart</ProcessChartEvent>
    <RefreshingEvent>Refreshing</RefreshingEvent>
    <RenderingEvent>Rendering</RenderingEvent>
    <ReportCacheProcessingEvent>Report Cache Processing</ReportCacheProcessingEvent>
    <SelectedIndexChangedEvent>Selected Index Changed</SelectedIndexChangedEvent>
    <StateRestoreEvent>State Restore</StateRestoreEvent>
    <StateSaveEvent>State Save</StateSaveEvent>
    <ValueChangedEvent>Value Changed</ValueChangedEvent>
  </PropertyEvents>
  <PropertyHatchStyle>
    <BackwardDiagonal>Backward Diagonal</BackwardDiagonal>
    <Cross>Cross</Cross>
    <DarkDownwardDiagonal>Dark Downward Diagonal</DarkDownwardDiagonal>
    <DarkHorizontal>Dark Horizontal</DarkHorizontal>
    <DarkUpwardDiagonal>Dark Upward Diagonal</DarkUpwardDiagonal>
    <DarkVertical>Dark Vertical</DarkVertical>
    <DashedDownwardDiagonal>Dashed Downward Diagonal</DashedDownwardDiagonal>
    <DashedHorizontal>Dashed Horizontal</DashedHorizontal>
    <DashedUpwardDiagonal>Dashed Upward Diagonal</DashedUpwardDiagonal>
    <DashedVertical>Dashed Vertical</DashedVertical>
    <DiagonalBrick>Diagonal Brick</DiagonalBrick>
    <DiagonalCross>Diagonal Cross</DiagonalCross>
    <Divot>Divot</Divot>
    <DottedDiamond>Dotted Diamond</DottedDiamond>
    <DottedGrid>Dotted Grid</DottedGrid>
    <ForwardDiagonal>Forward Diagonal</ForwardDiagonal>
    <Horizontal>Horizontal</Horizontal>
    <HorizontalBrick>Horizontal Brick</HorizontalBrick>
    <LargeCheckerBoard>Large Checker Board</LargeCheckerBoard>
    <LargeConfetti>Large Confetti</LargeConfetti>
    <LargeGrid>Large Grid</LargeGrid>
    <LightDownwardDiagonal>Light Downward Diagonal</LightDownwardDiagonal>
    <LightHorizontal>Light Horizontal</LightHorizontal>
    <LightUpwardDiagonal>Light Upward Diagonal</LightUpwardDiagonal>
    <LightVertical>Light Vertical</LightVertical>
    <NarrowHorizontal>Narrow Horizontal</NarrowHorizontal>
    <NarrowVertical>Narrow Vertical</NarrowVertical>
    <OutlinedDiamond>Outlined Diamond</OutlinedDiamond>
    <Percent05>Percent05</Percent05>
    <Percent10>Percent10</Percent10>
    <Percent20>Percent20</Percent20>
    <Percent25>Percent25</Percent25>
    <Percent30>Percent30</Percent30>
    <Percent40>Percent40</Percent40>
    <Percent50>Percent50</Percent50>
    <Percent60>Percent60</Percent60>
    <Percent70>Percent70</Percent70>
    <Percent75>Percent75</Percent75>
    <Percent80>Percent80</Percent80>
    <Percent90>Percent90</Percent90>
    <Plaid>Plaid</Plaid>
    <Shingle>Shingle</Shingle>
    <SmallCheckerBoard>Small Checker Board</SmallCheckerBoard>
    <SmallConfetti>Small Confetti</SmallConfetti>
    <SmallGrid>Small Grid</SmallGrid>
    <SolidDiamond>Solid Diamond</SolidDiamond>
    <Sphere>Sphere</Sphere>
    <Trellis>Trellis</Trellis>
    <Vertical>Vertical</Vertical>
    <Wave>Wave</Wave>
    <Weave>Weave</Weave>
    <WideDownwardDiagonal>Wide Downward Diagonal</WideDownwardDiagonal>
    <WideUpwardDiagonal>Wide Upward Diagonal</WideUpwardDiagonal>
    <ZigZag>Zig Zag</ZigZag>
  </PropertyHatchStyle>
  <PropertyMain>
    <AcceptsReturn>Accepts Return</AcceptsReturn>
    <AcceptsTab>Accepts Tab</AcceptsTab>
    <Actual>Actual</Actual>
    <AddClearZone>Add Clear Zone</AddClearZone>
    <Advanced>Advanced</Advanced>
    <AggregateFunction>Aggregate Function</AggregateFunction>
    <AggregateFunctions>Aggregate Functions</AggregateFunctions>
    <Alias>Alias</Alias>
    <Alignment>Alignment</Alignment>
    <AllowApplyBorderColor>Allow Apply Border Color</AllowApplyBorderColor>
    <AllowApplyBrush>Allow Apply Brush</AllowApplyBrush>
    <AllowApplyBrushNegative>Allow Apply Brush Negative</AllowApplyBrushNegative>
    <AllowApplyColorNegative>Allow Apply Color Negative</AllowApplyColorNegative>
    <AllowApplyLineColor>Allow Apply Line Color</AllowApplyLineColor>
    <AllowApplyStyle>Allow Apply Style</AllowApplyStyle>
    <AllowCleanSignature>Allow Clean Signature</AllowCleanSignature>
    <AllowExpressions>Allow Expressions</AllowExpressions>
    <AllowHtmlTags>Allow HTML Tags</AllowHtmlTags>
    <AllowNull>Allow Null</AllowNull>
    <AllowSeries>Allow Series</AllowSeries>
    <AllowSeriesElements>Allow Series Elements</AllowSeriesElements>
    <AllowSorting>Allow Sorting</AllowSorting>
    <AllowUseBackColor>Allow Use Back Color</AllowUseBackColor>
    <AllowUseBorder>Allow Use Border</AllowUseBorder>
    <AllowUseBorderFormatting>Allow Use Border Formatting</AllowUseBorderFormatting>
    <AllowUseBorderSides>Allow Use Border Sides</AllowUseBorderSides>
    <AllowUseBorderSidesFromLocation>Allow Use Border Sides from Location</AllowUseBorderSidesFromLocation>
    <AllowUseBrush>Allow Use Brush</AllowUseBrush>
    <AllowUseFont>Allow Use Font</AllowUseFont>
    <AllowUseForeColor>Allow Use Fore Color</AllowUseForeColor>
    <AllowUseHorAlignment>Allow Use Hor Alignment</AllowUseHorAlignment>
    <AllowUseImage>Allow Use Image</AllowUseImage>
    <AllowUseNegativeTextBrush>Allow Use Negative Text Brush</AllowUseNegativeTextBrush>
    <AllowUserValues>Allow User Values</AllowUserValues>
    <AllowUseTextBrush>Allow Use Text Brush</AllowUseTextBrush>
    <AllowUseTextFormat>Allow Use Text Format</AllowUseTextFormat>
    <AllowUseTextOptions>Allow Use Text Options</AllowUseTextOptions>
    <AllowUseTitle>Allow Use Title</AllowUseTitle>
    <AllowUseVertAlignment>Allow Use Vert Alignment</AllowUseVertAlignment>
    <AllowUsingAsSqlParameter>Allow using as SQL parameter</AllowUsingAsSqlParameter>
    <AlternatingBackColor>Alternating Back Color</AlternatingBackColor>
    <AlternatingCellBackColor>Alternating Cell Back Color</AlternatingCellBackColor>
    <AlternatingCellForeColor>Alternating Cell Fore Color</AlternatingCellForeColor>
    <AlternatingDataColor>Alternating Data Color</AlternatingDataColor>
    <AlternatingDataForeground>Alternating Data Foreground</AlternatingDataForeground>
    <Always>Always</Always>
    <Anchor>Anchor</Anchor>
    <Angle>Angle</Angle>
    <Antialiasing>Antialiasing</Antialiasing>
    <Appearance>Appearance</Appearance>
    <Apply>Apply</Apply>
    <Area>Area</Area>
    <Argument>Argument</Argument>
    <ArgumentDataColumn>Argument Data Column</ArgumentDataColumn>
    <ArgumentFormat>Argument Format</ArgumentFormat>
    <Arguments>Arguments</Arguments>
    <ArrowHeight>Arrow Height</ArrowHeight>
    <ArrowStyle>Arrow Style</ArrowStyle>
    <ArrowWidth>Arrow Width</ArrowWidth>
    <AspectRatio>Aspect Ratio</AspectRatio>
    <Author>Author</Author>
    <Auto>Auto</Auto>
    <AutoCalculateCenterPoint>Auto Calculate Center Point</AutoCalculateCenterPoint>
    <AutoDataColumns>Auto Data Columns</AutoDataColumns>
    <AutoDataRows>Auto Data Rows</AutoDataRows>
    <AutoLocalizeReportOnRun>Auto Localize Report on Run</AutoLocalizeReportOnRun>
    <AutoRefresh>Auto Refresh</AutoRefresh>
    <AutoRotate>Auto Rotate</AutoRotate>
    <AutoScale>Auto Scale</AutoScale>
    <AutoSeriesColorDataColumn>Auto Series Color Data Column</AutoSeriesColorDataColumn>
    <AutoSeriesKeyDataColumn>Auto Series Key Data Column</AutoSeriesKeyDataColumn>
    <AutoSeriesTitleDataColumn>Auto Series Title Data Column</AutoSeriesTitleDataColumn>
    <AutoWidth>Auto Width</AutoWidth>
    <AutoWidthType>Auto Width Type</AutoWidthType>
    <AvailableInTheViewer>Available in the Viewer</AvailableInTheViewer>
    <AxisLabelsColor>Axis Labels Color</AxisLabelsColor>
    <AxisLineColor>Axis Line Color</AxisLineColor>
    <AxisTitleColor>Axis Title Color</AxisTitleColor>
    <AxisValue>Axis Value</AxisValue>
    <BackColor>Back Color</BackColor>
    <Background>Background</Background>
    <BackgroundColor>Background Color</BackgroundColor>
    <BandColor>Band Color</BandColor>
    <BarCodeType>Bar Code Type</BarCodeType>
    <BasicStyleColor>Basic Style Color</BasicStyleColor>
    <Blend>Blend</Blend>
    <Blocks>Blocks</Blocks>
    <BodyBrush>Body Brush</BodyBrush>
    <BodyShape>Body Shape</BodyShape>
    <Bold>Bold</Bold>
    <Bookmark>Bookmark</Bookmark>
    <Border>Border</Border>
    <BorderBrush>Border Brush</BorderBrush>
    <BorderColor>Border Color</BorderColor>
    <BorderColorNegative>Border Color Negative</BorderColorNegative>
    <Borders>Borders</Borders>
    <BorderSize>Border Size</BorderSize>
    <BorderStyle>Border Style</BorderStyle>
    <BorderThickness>Border Thickness</BorderThickness>
    <BorderWidth>Border Width</BorderWidth>
    <Bottom>Bottom</Bottom>
    <BottomLeft>Bottom-Left</BottomLeft>
    <BottomRight>Bottom-Right</BottomRight>
    <BottomSide>Bottom Side</BottomSide>
    <BreakIfLessThan>Break if Less Than</BreakIfLessThan>
    <Brush>Brush</Brush>
    <BrushNegative>Brush Negative</BrushNegative>
    <BrushType>Brush Type</BrushType>
    <BubbleBackColor>Bubble Back Color</BubbleBackColor>
    <BubbleBorderColor>Bubble Border Color</BubbleBorderColor>
    <BubbleScale>Bubble Scale</BubbleScale>
    <BusinessObject>Business Object</BusinessObject>
    <CacheAllData>Cache All Data</CacheAllData>
    <CacheTotals>Cache Totals</CacheTotals>
    <CalcInvisible>Calc Invisible</CalcInvisible>
    <CalculatedDataColumn>Calculated Data Column</CalculatedDataColumn>
    <CalculationMode>Calculation Mode</CalculationMode>
    <CanBreak>Can Break</CanBreak>
    <Cancel>Cancel</Cancel>
    <CanGrow>Can Grow</CanGrow>
    <CanShrink>Can Shrink</CanShrink>
    <CastToColumnType>Cast to Column Type</CastToColumnType>
    <Categories>Categories</Categories>
    <Category>Category</Category>
    <CategoryConnections>Connections</CategoryConnections>
    <CellAlignment>Cell Alignment</CellAlignment>
    <CellBackColor>Cell Back Color</CellBackColor>
    <CellDockStyle>Cell Dock Style</CellDockStyle>
    <CellForeColor>Cell Fore Color</CellForeColor>
    <CellHeight>Cell Height</CellHeight>
    <Cells>Cells</Cells>
    <CellType>Cell Type</CellType>
    <CellWidth>Cell Width</CellWidth>
    <Center>Center</Center>
    <CenterPoint>Center Point</CenterPoint>
    <ChartAreaBorderColor>Chart Area Border Color</ChartAreaBorderColor>
    <ChartAreaBorderThickness>Chart Area Border Thickness</ChartAreaBorderThickness>
    <ChartAreaBrush>Chart Area Brush</ChartAreaBrush>
    <ChartAreaShowShadow>Chart Area Show Shadow</ChartAreaShowShadow>
    <ChartType>Chart Type</ChartType>
    <CheckAlignment>Check Alignment</CheckAlignment>
    <CheckColor>Check Color</CheckColor>
    <Checked>Checked</Checked>
    <CheckedIcon>Checked Icon</CheckedIcon>
    <CheckOnClick>Check on Click</CheckOnClick>
    <CheckStyle>Check Style</CheckStyle>
    <CheckStyleForFalse>Check Style for False</CheckStyleForFalse>
    <CheckStyleForTrue>Check Style for True</CheckStyleForTrue>
    <Checksum>Checksum</Checksum>
    <CheckSum>CheckSum</CheckSum>
    <CheckSum1>CheckSum1</CheckSum1>
    <CheckSum2>CheckSum2</CheckSum2>
    <Child>Child</Child>
    <ChildColumns>Child Columns</ChildColumns>
    <ChildSource>Child Source</ChildSource>
    <City>City</City>
    <ClearFormat>Clear Format</ClearFormat>
    <CloneContainer>Clone Container</CloneContainer>
    <CloseValues>Close Values</CloseValues>
    <Code>Code</Code>
    <CodePage>Code Page</CodePage>
    <Collapsed>Collapsed</Collapsed>
    <CollapseGroupFooter>Collapse Group Footer</CollapseGroupFooter>
    <CollapsingEnabled>Collapsing Enabled</CollapsingEnabled>
    <Collate>Collate</Collate>
    <CollectionName>Collection Name</CollectionName>
    <Color>Color</Color>
    <ColorDataColumn>Color Data Column</ColorDataColumn>
    <ColorEach>Color Each</ColorEach>
    <ColorMeter>Color Meter</ColorMeter>
    <Colors>Colors</Colors>
    <ColorScaleCondition>Color Scale Condition</ColorScaleCondition>
    <ColorScaleType>Color Scale Type</ColorScaleType>
    <Column>Column</Column>
    <ColumnCount>Column Count</ColumnCount>
    <ColumnDirection>Column Direction</ColumnDirection>
    <ColumnGaps>Column Gaps</ColumnGaps>
    <ColumnHeaderBackColor>Column Header Back Color</ColumnHeaderBackColor>
    <ColumnHeaderForeColor>Column Header Fore Color</ColumnHeaderForeColor>
    <ColumnHeadersVisible>Column Headers Visible</ColumnHeadersVisible>
    <Columns>Columns</Columns>
    <ColumnShape>Column Shape</ColumnShape>
    <ColumnWidth>Column Width</ColumnWidth>
    <CommandTimeout>Command Timeout</CommandTimeout>
    <CompanyPrefix>Company Prefix</CompanyPrefix>
    <ComponentStyle>Component Style</ComponentStyle>
    <Condition>Condition</Condition>
    <ConditionOptions>Condition Options</ConditionOptions>
    <Conditions>Conditions</Conditions>
    <ConnectionString>Connection String</ConnectionString>
    <ConnectOnStart>Connect on Start</ConnectOnStart>
    <ConstantLines>Constant Lines</ConstantLines>
    <Container>Container</Container>
    <ContentAlignment>Content Alignment</ContentAlignment>
    <ContinuousText>Continuous Text</ContinuousText>
    <ContourColor>Contour Color</ContourColor>
    <Converting>Converting</Converting>
    <ConvertNulls>Convert Nulls</ConvertNulls>
    <Copies>Copies</Copies>
    <CornerRadius>Corner Radius</CornerRadius>
    <Count>Count</Count>
    <CountData>Count Data</CountData>
    <Country>Country</Country>
    <Create>Create</Create>
    <CreateFieldOnDoubleClick>Create Field on Double Click</CreateFieldOnDoubleClick>
    <CreateLabel>Create Label</CreateLabel>
    <CrossFiltering>Cross-Filtering</CrossFiltering>
    <Culture>Culture</Culture>
    <CustomFonts>Custom Fonts</CustomFonts>
    <CustomFormat>Custom Format</CustomFormat>
    <CutPieList>Cut Pie List</CutPieList>
    <Data>Data</Data>
    <DataAdapter>Data Adapter</DataAdapter>
    <DataAdapters>Data Adapters</DataAdapters>
    <DataBarCondition>Data Bar Condition</DataBarCondition>
    <DataBarsNegative>Data Bars Negative</DataBarsNegative>
    <DataBarsOverlapped>Data Bars Overlapped</DataBarsOverlapped>
    <DataBarsPositive>Data Bars Positive</DataBarsPositive>
    <DataBindings>Data Bindings</DataBindings>
    <DataCells>Data Cells</DataCells>
    <DataColor>Data Color</DataColor>
    <DataColumn>Data Column</DataColumn>
    <DataColumns>Data Columns</DataColumns>
    <DataField>Data Field</DataField>
    <DataForeground>Data Foreground</DataForeground>
    <DataRelation>Data Relation</DataRelation>
    <DataRows>Data Rows</DataRows>
    <DataSource>Data Source</DataSource>
    <DataSources>Data Sources</DataSources>
    <DataTextField>Data Text Field</DataTextField>
    <DataTransformation>Data Transformation</DataTransformation>
    <DataType>Data Type</DataType>
    <DataUrl>Data URL</DataUrl>
    <DateInfo>Date Info</DateInfo>
    <DateTimeMode>DateTime Mode</DateTimeMode>
    <DateTimeStep>Date Time Step</DateTimeStep>
    <DecimalDigits>Decimal Digits</DecimalDigits>
    <Default>Default</Default>
    <DefaultColor>Default Color</DefaultColor>
    <DefaultHeightCell>Default Height of Cell</DefaultHeightCell>
    <DefaultNamespace>Default Namespace</DefaultNamespace>
    <DefaultSettings>Default Settings</DefaultSettings>
    <DependentColumn>Dependent Column</DependentColumn>
    <DependentValue>Dependent Value</DependentValue>
    <Description>Description</Description>
    <Destination>Destination</Destination>
    <DetectUrls>Detect URLs</DetectUrls>
    <DeviceWidth>Device Width</DeviceWidth>
    <DialogResult>Dialog Result</DialogResult>
    <Diameter>Diameter</Diameter>
    <Direction>Direction</Direction>
    <Disabled>Disabled</Disabled>
    <DisplayNameType>Display Name Type</DisplayNameType>
    <DisplayValue>Display Value</DisplayValue>
    <Distance>Distance</Distance>
    <DistanceBetweenTabs>Distance Between Tabs</DistanceBetweenTabs>
    <DistinguishedName>Distinguished Name</DistinguishedName>
    <Dock>Dock</Dock>
    <DockableTable>Dockable Table</DockableTable>
    <DockStyle>Dock Style</DockStyle>
    <DrawBorder>Draw Border</DrawBorder>
    <DrawHatch>Draw Hatch</DrawHatch>
    <DrawLine>Draw Line</DrawLine>
    <DrillDown>Drill-Down</DrillDown>
    <DrillDownEnabled>Drill-Down Enabled</DrillDownEnabled>
    <DrillDownMode>Drill-Down Mode</DrillDownMode>
    <DrillDownPage>Drill-Down Page</DrillDownPage>
    <DrillDownParameter1>Drill-Down Parameter 1</DrillDownParameter1>
    <DrillDownParameter2>Drill-Down Parameter 2</DrillDownParameter2>
    <DrillDownParameter3>Drill-Down Parameter 3</DrillDownParameter3>
    <DrillDownParameter4>Drill-Down Parameter 4</DrillDownParameter4>
    <DrillDownParameter5>Drill-Down Parameter 5</DrillDownParameter5>
    <DrillDownParameters>Drill-Down Parameters</DrillDownParameters>
    <DrillDownReport>Drill-Down Report</DrillDownReport>
    <DropDownAlign>Drop Down Align</DropDownAlign>
    <DropDownStyle>Drop Down Style</DropDownStyle>
    <DropDownWidth>Drop Down Width</DropDownWidth>
    <DropShadow>Drop Shadow</DropShadow>
    <Duplex>Duplex</Duplex>
    <Editable>Editable</Editable>
    <Effects>Effects</Effects>
    <EmptyBorderBrush>Empty Border Brush</EmptyBorderBrush>
    <EmptyBorderWidth>Empty Border Width</EmptyBorderWidth>
    <EmptyBrush>Empty Brush</EmptyBrush>
    <EmptyValue>Empty Value</EmptyValue>
    <Enabled>Enabled</Enabled>
    <EnableLog>Enabled Log</EnableLog>
    <EncodingMode>Encoding Mode</EncodingMode>
    <EncodingType>Encoding Type</EncodingType>
    <EndCap>End Cap</EndCap>
    <EndColor>End Color</EndColor>
    <EndValue>End Value</EndValue>
    <EndValues>End Values</EndValues>
    <EndWidth>End Width</EndWidth>
    <EngineVersion>Engine Version</EngineVersion>
    <EnumeratorSeparator>Enumerator Separator</EnumeratorSeparator>
    <EnumeratorType>Enumerator Type</EnumeratorType>
    <ErrorCorrectionLevel>Error Correction Level</ErrorCorrectionLevel>
    <ErrorsCorrectionLevel>Errors Correction Level</ErrorsCorrectionLevel>
    <EvenStyle>Even Style</EvenStyle>
    <ExceedMargins>Exceed Margins</ExceedMargins>
    <ExcelSheet>Excel Sheet</ExcelSheet>
    <ExcelValue>Excel Value</ExcelValue>
    <Expand>Expand</Expand>
    <Exponential>Exponential</Exponential>
    <ExportAsImage>Export as Image</ExportAsImage>
    <ExportSettings>Export Settings</ExportSettings>
    <Expression>Expression</Expression>
    <ExtensionDigit>Extension Digit</ExtensionDigit>
    <EyeBallBrush>Eye Ball Brush</EyeBallBrush>
    <EyeBallShape>Eye Ball Shape</EyeBallShape>
    <EyeFrameBrush>Eye Frame Brush</EyeFrameBrush>
    <EyeFrameShape>Eye Frame Shape</EyeFrameShape>
    <FaqPage>FAQ Page</FaqPage>
    <Field>Field</Field>
    <FieldIs>Field Is</FieldIs>
    <Fifth>Fifth</Fifth>
    <File>File</File>
    <Fill>Fill</Fill>
    <FillColor>Fill Color</FillColor>
    <Filter>Filter</Filter>
    <FilterElements>Filter Elements</FilterElements>
    <FilterEngine>Filter Engine</FilterEngine>
    <FilterMode>Filter Mode</FilterMode>
    <FilterOn>Filter On</FilterOn>
    <Filters>Filters</Filters>
    <First>First</First>
    <FirstTabOffset>First Tab Offset</FirstTabOffset>
    <FixedHeight>Fixed Height</FixedHeight>
    <FixedWidth>Fixed Width</FixedWidth>
    <Flat>Flat</Flat>
    <FlatMode>Flat Mode</FlatMode>
    <Focus>Focus</Focus>
    <Font>Font</Font>
    <FontBold>Font Bold</FontBold>
    <FontItalic>Font Italic</FontItalic>
    <FontName>Font Name</FontName>
    <FontSize>Font Size</FontSize>
    <FontSizeMode>Font Size Mode</FontSizeMode>
    <FontStrikeout>Font Strikeout</FontStrikeout>
    <FontSubscript>Subscript</FontSubscript>
    <FontSuperscript>Superscript</FontSuperscript>
    <FontUnderline>Font Underline</FontUnderline>
    <FontUnit>Font Unit</FontUnit>
    <FooterCanBreak>Footer Can Break</FooterCanBreak>
    <FooterCanGrow>Footer Can Grow</FooterCanGrow>
    <FooterCanShrink>Footer Can Shrink</FooterCanShrink>
    <FooterColor>Footer Color</FooterColor>
    <FooterFont>Footer Font</FooterFont>
    <FooterForeColor>Footer Fore Color</FooterForeColor>
    <FooterForeground>Footer Foreground</FooterForeground>
    <FooterPrintAtBottom>Footer Print At Bottom</FooterPrintAtBottom>
    <FooterPrintIfEmpty>Footer Print If Empty</FooterPrintIfEmpty>
    <FooterPrintOn>Footer Print On</FooterPrintOn>
    <FooterPrintOnAllPages>Footer Print On All Pages</FooterPrintOnAllPages>
    <FooterPrintOnEvenOddPages>Footer Print On Even Odd Pages</FooterPrintOnEvenOddPages>
    <FooterRowsCount>Footer Rows Count</FooterRowsCount>
    <Footers>Footers</Footers>
    <ForeColor>Fore Color</ForeColor>
    <Foreground>Foreground</Foreground>
    <Format>Format</Format>
    <FormName>Form Name</FormName>
    <FormSettings>Form Settings</FormSettings>
    <Fourth>Fourth</Fourth>
    <From>From</From>
    <FrozenColumns>Frozen Columns</FrozenColumns>
    <FullConvertExpression>Full Convert Expression</FullConvertExpression>
    <FullName>Full Name</FullName>
    <Function>Function</Function>
    <Functions>Functions</Functions>
    <GapAfterLastColumn>Gap After Last Column</GapAfterLastColumn>
    <GisSettings>Gis Settings</GisSettings>
    <GlobalizationStrings>Globalization Strings</GlobalizationStrings>
    <GlobalizedName>Globalized Name</GlobalizedName>
    <GlyphColor>Glyph Color</GlyphColor>
    <GridColor>Grid Color</GridColor>
    <GridColumns>Grid Columns</GridColumns>
    <GridLineColor>Grid Line Color</GridLineColor>
    <GridLinesHor>Grid Lines Horizontal</GridLinesHor>
    <GridLinesHorColor>Grid Lines Horizontal Color</GridLinesHorColor>
    <GridLinesHorRight>Grid Lines Horizontal Right</GridLinesHorRight>
    <GridLineStyle>Grid Line Style</GridLineStyle>
    <GridLinesVert>Grid Lines Vertical</GridLinesVert>
    <GridLinesVertColor>Grid Lines Vertical Color</GridLinesVertColor>
    <GridOutline>Grid Outline</GridOutline>
    <GridVerticalStep>Grid Vertical Step</GridVerticalStep>
    <Group>Group</Group>
    <GroupDataColumn>Group Data Column</GroupDataColumn>
    <GroupMeter>Group Meter</GroupMeter>
    <GrowToHeight>Grow to Height</GrowToHeight>
    <Header>Header</Header>
    <HeaderAlignment>Header Alignment</HeaderAlignment>
    <HeaderBackColor>Header Back Color</HeaderBackColor>
    <HeaderCanBreak>Header Can Break</HeaderCanBreak>
    <HeaderCanGrow>Header Can Grow</HeaderCanGrow>
    <HeaderCanShrink>Header Can Shrink</HeaderCanShrink>
    <HeaderColor>Header Color</HeaderColor>
    <HeaderFont>Header Font</HeaderFont>
    <HeaderForeColor>Header Fore Color</HeaderForeColor>
    <HeaderForeground>Header Foreground</HeaderForeground>
    <HeaderPrintAtBottom>Header Print At Bottom</HeaderPrintAtBottom>
    <HeaderPrintIfEmpty>Header Print If Empty</HeaderPrintIfEmpty>
    <HeaderPrintOn>Header Print On</HeaderPrintOn>
    <HeaderPrintOnAllPages>Header Print On All Pages</HeaderPrintOnAllPages>
    <HeaderPrintOnEvenOddPages>Header Print On Even Odd Pages</HeaderPrintOnEvenOddPages>
    <HeaderRowsCount>Header Rows Count</HeaderRowsCount>
    <Headers>Headers</Headers>
    <HeaderText>Header Text</HeaderText>
    <Heading>Heading</Heading>
    <Heatmap>Heatmap</Heatmap>
    <HeatmapColors>Heatmap Colors</HeatmapColors>
    <HeatmapWithGroup>Heatmap With Group</HeatmapWithGroup>
    <Height>Height</Height>
    <Hidden>Hidden</Hidden>
    <HideSeriesWithEmptyTitle>Hide Series with Empty Title</HideSeriesWithEmptyTitle>
    <HideZeros>Hide Zeros</HideZeros>
    <High>High</High>
    <HighlightCondition>Highlight Condition</HighlightCondition>
    <HighValues>High Values</HighValues>
    <HorAlignment>Horizontal Alignment</HorAlignment>
    <HorSpacing>Horizontal Spacing</HorSpacing>
    <HotBackColor>Hot Back Color</HotBackColor>
    <HotColumnHeaderBackColor>Hot Column Header Back Color</HotColumnHeaderBackColor>
    <HotForeColor>Hot Fore Color</HotForeColor>
    <HotGlyphColor>Hot Glyph Color</HotGlyphColor>
    <HotHeaderBackColor>Hot Header Back Color</HotHeaderBackColor>
    <HotHeaderColor>Hot Header Color</HotHeaderColor>
    <HotkeyPrefix>Hotkey Prefix</HotkeyPrefix>
    <HotRowHeaderBackColor>Hot Row Header Back Color</HotRowHeaderBackColor>
    <HotSelectedBackColor>Hot Selected Back Color</HotSelectedBackColor>
    <HotSelectedForeColor>Hot Selected Fore Color</HotSelectedForeColor>
    <HotSelectedGlyphColor>Hot Selected Glyph Color</HotSelectedGlyphColor>
    <HtmlPreviewMode>HTML Preview Mode</HtmlPreviewMode>
    <HtmlTags>HTML Tags</HtmlTags>
    <Hyperlink>Hyperlink</Hyperlink>
    <HyperlinkDataColumn>Hyperlink Data Column</HyperlinkDataColumn>
    <HyperlinkPattern>Hyperlink Pattern</HyperlinkPattern>
    <Icon>Icon</Icon>
    <IconAlignment>Icon Alignment</IconAlignment>
    <IconBrush>Icon Brush</IconBrush>
    <IconColor>Icon Color</IconColor>
    <IconSet>Icon Set</IconSet>
    <IconSetCondition>Icon Set Condition</IconSetCondition>
    <Idents>Indents</Idents>
    <Image>Image</Image>
    <ImageAlign>Image Align</ImageAlign>
    <ImageAlignment>Image Alignment</ImageAlignment>
    <ImageAspectRatio>Image Aspect Ratio</ImageAspectRatio>
    <ImageData>Image Data</ImageData>
    <ImageEnabled>Image Enabled</ImageEnabled>
    <ImageHorAlignment>Image Horizontal Alignment</ImageHorAlignment>
    <ImageMultipleFactor>Image Multiple Factor</ImageMultipleFactor>
    <ImageRotation>Image Rotation</ImageRotation>
    <ImageShowBehind>Image Show Behind</ImageShowBehind>
    <ImageStretch>Image Stretch</ImageStretch>
    <ImageTiling>Image Tiling</ImageTiling>
    <ImageTransparency>Image Transparency</ImageTransparency>
    <ImageURL>Image URL</ImageURL>
    <ImageVertAlignment>Image Vertical Alignment</ImageVertAlignment>
    <ImportRelations>Import Relations</ImportRelations>
    <Increment>Increment</Increment>
    <Indent>Indent</Indent>
    <Indicator>Indicator</Indicator>
    <IndicatorNegative>Indicator Negative</IndicatorNegative>
    <IndicatorNeutral>Indicator Neutral</IndicatorNeutral>
    <IndicatorPositive>Indicator Positive</IndicatorPositive>
    <IndicatorValue>Indicator Value</IndicatorValue>
    <IndividualColor>Individual Color</IndividualColor>
    <InitBy>Init by</InitBy>
    <Initials>Initials</Initials>
    <InitialSelection>Initial Selection</InitialSelection>
    <InitialSelectionSource>Initial Selection Source</InitialSelectionSource>
    <InitialValue>Initial Value</InitialValue>
    <InnerText>Inner Text</InnerText>
    <Input>Input</Input>
    <Insert>Insert</Insert>
    <Interaction>Interaction</Interaction>
    <InterlacedBrush>Interlaced Brush</InterlacedBrush>
    <InterlacingHor>Interlacing Horizontal</InterlacingHor>
    <InterlacingHorBrush>Interlacing Horizontal Brush</InterlacingHorBrush>
    <InterlacingVert>Interlacing Vertical</InterlacingVert>
    <InterlacingVertBrush>Interlacing Vertical Brush</InterlacingVertBrush>
    <Interpolation>Interpolation</Interpolation>
    <IsReversed>Is Reversed</IsReversed>
    <Italic>Italic</Italic>
    <Item>Item</Item>
    <ItemHeight>Item Height</ItemHeight>
    <Items>Items</Items>
    <KeepChildTogether>Keep Child Together</KeepChildTogether>
    <KeepCrossTabTogether>Keep Cross-Tab Together</KeepCrossTabTogether>
    <KeepDetails>Keep Details</KeepDetails>
    <KeepDetailsTogether>Keep Details Together</KeepDetailsTogether>
    <KeepFooterTogether>Keep Footer Together</KeepFooterTogether>
    <KeepGroupFooterTogether>Keep Group Footer Together</KeepGroupFooterTogether>
    <KeepGroupHeaderTogether>Keep Group Header Together</KeepGroupHeaderTogether>
    <KeepGroupTogether>Keep Group Together</KeepGroupTogether>
    <KeepHeaderTogether>Keep Header Together</KeepHeaderTogether>
    <KeepMergedCellsTogether>Keep Merged Cells Together</KeepMergedCellsTogether>
    <KeepReportSummaryTogether>Keep Report Summary Together</KeepReportSummaryTogether>
    <KeepSubReportTogether>Keep Sub-Report Together</KeepSubReportTogether>
    <Key>Key</Key>
    <KeyDataColumn>Key Data Column</KeyDataColumn>
    <KeyMeter>Key Meter</KeyMeter>
    <KeyMeters>Key Meters</KeyMeters>
    <Keys>Keys</Keys>
    <Label>Label</Label>
    <LabelColor>Label Color</LabelColor>
    <LabelForeground>Label Foreground</LabelForeground>
    <LabelRotationMode>Label Rotation Mode</LabelRotationMode>
    <Labels>Labels</Labels>
    <LabelsColor>Labels Color</LabelsColor>
    <LabelShadowForeground>Label Shadow Foreground</LabelShadowForeground>
    <LabelsOffset>Labels Offset</LabelsOffset>
    <Language>Language</Language>
    <LargeHeight>Large Height</LargeHeight>
    <LargeHeightFactor>Large Height Factor</LargeHeightFactor>
    <LaTexExpression>LaTex Expression</LaTexExpression>
    <Latitude>Latitude</Latitude>
    <Layout>Layout</Layout>
    <Left>Left</Left>
    <LeftSide>Left Side</LeftSide>
    <Legend>Legend</Legend>
    <LegendBorderColor>Legend Border Color</LegendBorderColor>
    <LegendBrush>Legend Brush</LegendBrush>
    <LegendLabelsColor>Legend Labels Color</LegendLabelsColor>
    <LegendTitleColor>Legend Title Color</LegendTitleColor>
    <LegendValueType>Legend Value Type</LegendValueType>
    <Length>Length</Length>
    <LengthUnderLabels>Length under Labels</LengthUnderLabels>
    <Lighting>Lighting</Lighting>
    <LimitRows>Limit Rows</LimitRows>
    <Linear>Linear</Linear>
    <LinearBarBorderBrush>Linear Bar Border Brush</LinearBarBorderBrush>
    <LinearBarBrush>Linear Bar Brush</LinearBarBrush>
    <LinearBarEmptyBorderBrush>Linear Bar Empty Border Brush</LinearBarEmptyBorderBrush>
    <LinearBarEmptyBrush>Linear Bar Empty Brush</LinearBarEmptyBrush>
    <LinearScaleBrush>Linear Scale Brush</LinearScaleBrush>
    <LineColor>Line Color</LineColor>
    <LineColorNegative>Line Color Negative</LineColorNegative>
    <LineLength>Line Length</LineLength>
    <LineLimit>Line Limit</LineLimit>
    <LineMarker>Line Marker</LineMarker>
    <LinesOfUnderline>Lines of Underline</LinesOfUnderline>
    <LineSpacing>Line Spacing</LineSpacing>
    <LineStyle>Line Style</LineStyle>
    <LineWidth>Line Width</LineWidth>
    <Linked>Linked</Linked>
    <ListOfArguments>List of Arguments</ListOfArguments>
    <ListOfHyperlinks>List of Hyperlinks</ListOfHyperlinks>
    <ListOfTags>List of Tags</ListOfTags>
    <ListOfToolTips>List of Tooltips</ListOfToolTips>
    <ListOfValues>List of Values</ListOfValues>
    <ListOfValuesClose>List of Values Close</ListOfValuesClose>
    <ListOfValuesEnd>List of Values End</ListOfValuesEnd>
    <ListOfValuesHigh>List of Values High</ListOfValuesHigh>
    <ListOfValuesLow>List of Values Low</ListOfValuesLow>
    <ListOfValuesOpen>List of Values Open</ListOfValuesOpen>
    <ListOfWeights>List of Weights</ListOfWeights>
    <ListOfXValues>List of Values X</ListOfXValues>
    <ListOfYValues>List of Values Y</ListOfYValues>
    <Localizable>Localizable</Localizable>
    <Location>Location</Location>
    <Locked>Locked</Locked>
    <Logarithmic>Logarithmic</Logarithmic>
    <LogarithmicScale>Logarithmic Scale</LogarithmicScale>
    <Logo>Logo</Logo>
    <Longitude>Longitude</Longitude>
    <Low>Low</Low>
    <LowValues>Low Values</LowValues>
    <MajorInterval>Major Interval</MajorInterval>
    <MapAfrica>Africa</MapAfrica>
    <MapAsia>Asia</MapAsia>
    <MapCustom>Custom</MapCustom>
    <MapEurope>Europe</MapEurope>
    <MapID>Map ID</MapID>
    <MapNorthAmerica>North America</MapNorthAmerica>
    <MapOceania>Oceania</MapOceania>
    <MapPopularMaps>Popular maps</MapPopularMaps>
    <Maps>Maps</Maps>
    <MapSouthAmerica>South America</MapSouthAmerica>
    <MapStyle>Map Style</MapStyle>
    <MapType>Map Type</MapType>
    <Margin>Margin</Margin>
    <Margins>Margins</Margins>
    <Marker>Marker</Marker>
    <MarkerAlignment>Marker Alignment</MarkerAlignment>
    <MarkerAngle>Marker Angle</MarkerAngle>
    <MarkerBorder>Marker Border</MarkerBorder>
    <MarkerBrush>Marker Brush</MarkerBrush>
    <MarkerColor>Marker Color</MarkerColor>
    <MarkerSize>Marker Size</MarkerSize>
    <MarkerType>Marker Type</MarkerType>
    <MarkerVisible>Marker Visible</MarkerVisible>
    <MasterComponent>Master Component</MasterComponent>
    <MasterKeyDataColumn>Master Key Data Column</MasterKeyDataColumn>
    <MatrixSize>Matrix Size</MatrixSize>
    <MaxDate>Max Date</MaxDate>
    <MaxDropDownItems>Max Drop Down Items</MaxDropDownItems>
    <MaxHeight>Max Height</MaxHeight>
    <Maximum>Maximum</Maximum>
    <MaximumValue>Maximum Value</MaximumValue>
    <MaxLength>Max Length</MaxLength>
    <MaxNumberOfLines>Max Number of Lines</MaxNumberOfLines>
    <MaxSize>Max Size</MaxSize>
    <MaxValue>Max Value</MaxValue>
    <MaxWidth>Max Width</MaxWidth>
    <MergeDuplicates>Merge Duplicates</MergeDuplicates>
    <MergeHeaders>Merge Headers</MergeHeaders>
    <Mid>Mid</Mid>
    <MinDate>Min Date</MinDate>
    <MinHeight>Min Height</MinHeight>
    <Minimum>Minimum</Minimum>
    <MinimumFontSize>Minimum Font Size</MinimumFontSize>
    <MinimumValue>Minimum Value</MinimumValue>
    <MinorColor>Minor Color</MinorColor>
    <MinorCount>Minor Count</MinorCount>
    <MinorInterval>Minor Interval</MinorInterval>
    <MinorLength>Minor Length</MinorLength>
    <MinorStyle>Minor Style</MinorStyle>
    <MinorVisible>Minor Visible</MinorVisible>
    <MinRowsInColumn>Min Rows in Column</MinRowsInColumn>
    <MinSize>Min Size</MinSize>
    <MinValue>Min Value</MinValue>
    <MinWidth>Min Width</MinWidth>
    <MirrorMargins>Mirror Margins</MirrorMargins>
    <Mode>Mode</Mode>
    <Module>Module</Module>
    <Move>Move</Move>
    <Multiline>Multiline</Multiline>
    <MultipleFactor>Multiple Factor</MultipleFactor>
    <MultipleInitialization>Multiple Initialization</MultipleInitialization>
    <Name>Name</Name>
    <NameDataColumn>Name Data Column</NameDataColumn>
    <NameInSource>Name in Source</NameInSource>
    <NameMeter>Name Meter</NameMeter>
    <NameParent>Parent Name</NameParent>
    <Namespaces>Namespaces</Namespaces>
    <NeedleBorderBrush>Needle Border Brush</NeedleBorderBrush>
    <NeedleBorderWidth>Needle Border Width</NeedleBorderWidth>
    <NeedleBrush>Needle Brush</NeedleBrush>
    <NeedleCapBorderBrush>Needle Cap Border Brush</NeedleCapBorderBrush>
    <NeedleCapBrush>Needle Cap Brush</NeedleCapBrush>
    <Negative>Negative</Negative>
    <NegativeColor>Negative Color</NegativeColor>
    <NegativeSeriesColors>Negative Series Colors</NegativeSeriesColors>
    <NegativeTextBrush>Negative Text Brush</NegativeTextBrush>
    <NestedLevel>Nested Level</NestedLevel>
    <NewColumnAfter>New Column After</NewColumnAfter>
    <NewColumnBefore>New Column Before</NewColumnBefore>
    <NewPageAfter>New Page After</NewPageAfter>
    <NewPageBefore>New Page Before</NewPageBefore>
    <NextPage>Next Page</NextPage>
    <NoElements>No Elements</NoElements>
    <NoIcon>No Icon</NoIcon>
    <NullText>Null Text</NullText>
    <NumberOfColumns>Number of Columns</NumberOfColumns>
    <NumberOfCopies>Number of Copies</NumberOfCopies>
    <NumberOfPass>Number of Pass</NumberOfPass>
    <NumberOfValues>Number of Values</NumberOfValues>
    <OddStyle>Odd Style</OddStyle>
    <Offset>Offset</Offset>
    <OffsetAngle>Offset Angle</OffsetAngle>
    <OnClick>On Click</OnClick>
    <OnDataManipulation>On Data Manipulation</OnDataManipulation>
    <OnePointZoom>One Point Zoom</OnePointZoom>
    <OnHover>On Hover</OnHover>
    <OnlyText>Only Text</OnlyText>
    <OnSelect>On Select</OnSelect>
    <Opacity>Opacity</Opacity>
    <OpenValues>Open Values</OpenValues>
    <Operation>Operation</Operation>
    <Options>Options</Options>
    <Options3D>Options 3D</Options3D>
    <Orientation>Orientation</Orientation>
    <OthersText>Others Text</OthersText>
    <OverlappedColor>Overlapped Color</OverlappedColor>
    <Padding>Padding</Padding>
    <PageHeight>Page Height</PageHeight>
    <PageNumbers>Page Numbers</PageNumbers>
    <PageTurnTime>Page Turn Time</PageTurnTime>
    <PageWidth>Page Width</PageWidth>
    <Paper>Paper</Paper>
    <PaperSize>Paper Size</PaperSize>
    <PaperSourceOfFirstPage>Paper Source of First Page</PaperSourceOfFirstPage>
    <PaperSourceOfOtherPages>Paper Source of Other Pages</PaperSourceOfOtherPages>
    <Parameter>Parameter</Parameter>
    <ParameterAlignment>Parameter Alignment</ParameterAlignment>
    <Parameters>Parameters</Parameters>
    <ParametersDateFormat>Parameters Date Format</ParametersDateFormat>
    <ParametersOrientation>Parameters Orientation</ParametersOrientation>
    <ParameterWidth>Parameter Width</ParameterWidth>
    <ParentColumns>Parent Columns</ParentColumns>
    <ParentSource>Parent Data Source</ParentSource>
    <ParentValue>Parent Value</ParentValue>
    <ParetoSeriesColors>Pareto Series Colors</ParetoSeriesColors>
    <PasswordChar>Password Char</PasswordChar>
    <Path>Path</Path>
    <PathData>Path Data</PathData>
    <PathSchema>Path Schema</PathSchema>
    <Pattern>Pattern</Pattern>
    <PDFPreview>PDF Preview</PDFPreview>
    <Placeholder>Placeholder</Placeholder>
    <Placement>Placement</Placement>
    <PlaceOnToolbox>Place on Toolbox</PlaceOnToolbox>
    <PointAtCenter>Point at Center</PointAtCenter>
    <Position>Position</Position>
    <Positive>Positive</Positive>
    <PositiveColor>Positive Color</PositiveColor>
    <PreferredColumnWidth>Preferred Column Width</PreferredColumnWidth>
    <PreferredRowHeight>Preferred Row Height</PreferredRowHeight>
    <PreventIntersection>Prevent Intersection</PreventIntersection>
    <PreviewMode>Preview Mode</PreviewMode>
    <PreviewSettings>Preview Settings</PreviewSettings>
    <Printable>Printable</Printable>
    <PrintAtBottom>Print at Bottom</PrintAtBottom>
    <PrinterName>Printer Name</PrinterName>
    <PrinterSettings>Printer Settings</PrinterSettings>
    <PrintHeadersFootersFromPreviousPage>Print Headers and Footers from Previous Page</PrintHeadersFootersFromPreviousPage>
    <PrintIfDetailEmpty>Print if Detail Empty</PrintIfDetailEmpty>
    <PrintIfEmpty>Print if Empty</PrintIfEmpty>
    <PrintIfParentDisabled>Print if Parent Disabled</PrintIfParentDisabled>
    <PrintOn>Print on</PrintOn>
    <PrintOnAllPages>Print on All Pages</PrintOnAllPages>
    <PrintOnEvenOddPages>Print on Even Odd Pages</PrintOnEvenOddPages>
    <PrintOnPreviousPage>Print on Previous Page</PrintOnPreviousPage>
    <PrintTitleOnAllPages>Print Title On All Pages</PrintTitleOnAllPages>
    <PrintVerticalBars>Print Vertical Bars</PrintVerticalBars>
    <ProcessAt>Process at</ProcessAt>
    <ProcessAtEnd>Process at End</ProcessAtEnd>
    <ProcessingDuplicates>Processing Duplicates</ProcessingDuplicates>
    <ProcessTilde>Process Tilde</ProcessTilde>
    <ProductHomePage>Product Home Page</ProductHomePage>
    <Property>Property</Property>
    <Provider>Provider</Provider>
    <RadarStyle>Radar Style</RadarStyle>
    <RadialBarBorderBrush>Radial Bar Border Brush</RadialBarBorderBrush>
    <RadialBarBrush>Radial Bar Brush</RadialBarBrush>
    <RadialBarEmptyBorderBrush>Radial Bar Empty Border Brush</RadialBarEmptyBorderBrush>
    <RadialBarEmptyBrush>Radial Bar Empty Brush</RadialBarEmptyBrush>
    <Radius>Radius</Radius>
    <RadiusMode>Radius Mode</RadiusMode>
    <Range>Range</Range>
    <RangeColorMode>Range Color Mode</RangeColorMode>
    <RangeFrom>From</RangeFrom>
    <RangeMode>Range Mode</RangeMode>
    <RangeScrollEnabled>Range Scroll Enabled</RangeScrollEnabled>
    <RangeTo>To</RangeTo>
    <RangeType>Range Type</RangeType>
    <Ratio>Ratio</Ratio>
    <RatioY>Ratio Y</RatioY>
    <ReadOnly>Read Only</ReadOnly>
    <Reason>Reason</Reason>
    <RecentFonts>Recent Fonts</RecentFonts>
    <ReconnectOnEachRow>Reconnect on Each Row</ReconnectOnEachRow>
    <ReferencedAssemblies>Referenced Assemblies</ReferencedAssemblies>
    <Refresh>Refresh</Refresh>
    <RefreshTime>Refresh Time</RefreshTime>
    <Regular>Regular</Regular>
    <Relation>Relation</Relation>
    <RelationName>Relation Name</RelationName>
    <Relations>Relations</Relations>
    <RelativeHeight>Relative Height</RelativeHeight>
    <RelativeWidth>Relative Width</RelativeWidth>
    <RememberSelection>Remember Selection</RememberSelection>
    <RemoveUnusedDataBeforeStart>Remove Unused Data Before Start</RemoveUnusedDataBeforeStart>
    <RenderTo>Render to</RenderTo>
    <ReportAlias>Report Alias</ReportAlias>
    <ReportAuthor>Report Author</ReportAuthor>
    <ReportCacheMode>Report Cache Mode</ReportCacheMode>
    <ReportDescription>Report Description</ReportDescription>
    <ReportIcon>Report Icon</ReportIcon>
    <ReportImage>Report Image</ReportImage>
    <ReportName>Report Name</ReportName>
    <ReportUnit>Report Unit</ReportUnit>
    <RequestFromUser>Request from User</RequestFromUser>
    <RequestParameters>Request Parameters</RequestParameters>
    <Required>Required</Required>
    <ResetAllFilters>Reset All Filters</ResetAllFilters>
    <ResetDataSource>Reset Data Source</ResetDataSource>
    <ResetPageNumber>Reset Page Number</ResetPageNumber>
    <Resize>Resize</Resize>
    <Resource>Resource</Resource>
    <Resources>Resources</Resources>
    <Restrictions>Restrictions</Restrictions>
    <RetrieveOnlyUsedData>Retrieve Only Used Data</RetrieveOnlyUsedData>
    <ReturnValue>Return Value</ReturnValue>
    <ReverseHor>Reverse Horizontal</ReverseHor>
    <ReverseVert>Reverse Vertical</ReverseVert>
    <Right>Right</Right>
    <RightSide>Right Side</RightSide>
    <RightToLeft>Right to Left</RightToLeft>
    <Rotation>Rotation</Rotation>
    <RotationLabels>Rotation Labels</RotationLabels>
    <RotationMode>Rotation Mode</RotationMode>
    <Round>Round</Round>
    <RoundValues>Round Values</RoundValues>
    <RowCount>Row Count</RowCount>
    <RowHeaderBackColor>Row Header Back Color</RowHeaderBackColor>
    <RowHeaderForeColor>Row Header Fore Color</RowHeaderForeColor>
    <RowHeadersVisible>Row Headers Visible</RowHeadersVisible>
    <RowHeaderWidth>Row Header Width</RowHeaderWidth>
    <Rows>Rows</Rows>
    <RowsPerPage>Rows per Page</RowsPerPage>
    <Scale>Scale</Scale>
    <ScaleHor>Scale Hor</ScaleHor>
    <ScriptLanguage>Script Language</ScriptLanguage>
    <ScrollTime>Scroll Time</ScrollTime>
    <Second>Second</Second>
    <SegmentPerHeight>Segment per Height</SegmentPerHeight>
    <SegmentPerWidth>Segment per Width</SegmentPerWidth>
    <SelectedBackColor>Selected Back Color</SelectedBackColor>
    <SelectedCellBackColor>Selected Cell Back Color</SelectedCellBackColor>
    <SelectedCellForeColor>Selected Cell Fore Color</SelectedCellForeColor>
    <SelectedDataColor>Selected Data Color</SelectedDataColor>
    <SelectedDataForeground>Selected Data Foreground</SelectedDataForeground>
    <SelectedForeColor>Selected Fore Color</SelectedForeColor>
    <SelectedGlyphColor>Selected Glyph Color</SelectedGlyphColor>
    <SelectedIndex>Selected Index</SelectedIndex>
    <SelectedItem>Selected Item</SelectedItem>
    <SelectedKey>Selected Key</SelectedKey>
    <SelectedValue>Selected Value</SelectedValue>
    <Selection>Selection</Selection>
    <SelectionBackColor>Selection Back Color</SelectionBackColor>
    <SelectionEnabled>Selection Enabled</SelectionEnabled>
    <SelectionForeColor>Selection Fore Color</SelectionForeColor>
    <SelectionMode>Selection Mode</SelectionMode>
    <SelectionType>Selection Type</SelectionType>
    <SelectStyle>Select Style</SelectStyle>
    <SendType>Send Type</SendType>
    <SeparatorColor>Separator Color</SeparatorColor>
    <SerialNumber>Serial Number</SerialNumber>
    <Series>Series</Series>
    <SeriesBorderColor>Series Border Color</SeriesBorderColor>
    <SeriesBorderThickness>Series Border Thickness</SeriesBorderThickness>
    <SeriesColors>Series Colors</SeriesColors>
    <SeriesCornerRadius>Series Corner Radius</SeriesCornerRadius>
    <SeriesLabels>Series Labels</SeriesLabels>
    <SeriesLabelsBorderColor>Series Labels Border Color</SeriesLabelsBorderColor>
    <SeriesLabelsBrush>Series Labels Brush</SeriesLabelsBrush>
    <SeriesLabelsColor>Series Labels Color</SeriesLabelsColor>
    <SeriesLabelsLineColor>Series Labels Line Color</SeriesLabelsLineColor>
    <SeriesLighting>Series Lighting</SeriesLighting>
    <SeriesShowBorder>Series Show Border</SeriesShowBorder>
    <SeriesShowShadow>Series Show Shadow</SeriesShowShadow>
    <SeriesTitle>Series Title</SeriesTitle>
    <Shadow>Shadow</Shadow>
    <ShadowBrush>Shadow Brush</ShadowBrush>
    <ShadowColor>Shadow Color</ShadowColor>
    <ShadowSize>Shadow Size</ShadowSize>
    <ShapeType>Shape Type</ShapeType>
    <Shift>Shift</Shift>
    <ShiftMode>Shift Mode</ShiftMode>
    <ShortName>Short Name</ShortName>
    <ShortValue>Short Value</ShortValue>
    <Show3D>Show 3D</Show3D>
    <ShowAllValue>Show All Value</ShowAllValue>
    <ShowBehind>Show Behind</ShowBehind>
    <ShowBlanks>Show Blanks</ShowBlanks>
    <ShowBubble>Show Bubble</ShowBubble>
    <ShowDialog>Show Dialog</ShowDialog>
    <ShowEdgeValues>Show Edge Values</ShowEdgeValues>
    <ShowHyperlink>Show Hyperlink</ShowHyperlink>
    <ShowImageBehind>Show Image Behind</ShowImageBehind>
    <ShowInLegend>Show in Legend</ShowInLegend>
    <ShowInnerPoints>Show Inner Points</ShowInnerPoints>
    <ShowInPercent>Show in Percent</ShowInPercent>
    <ShowLabel>Show Label</ShowLabel>
    <ShowLabels>Show Labels</ShowLabels>
    <ShowLabelText>Show Label Text</ShowLabelText>
    <ShowLegend>Show Legend</ShowLegend>
    <ShowMarker>Show Marker</ShowMarker>
    <ShowMeanMarkers>Show Mean Markers</ShowMeanMarkers>
    <ShowNulls>Show Nulls</ShowNulls>
    <ShowOthers>Show Others</ShowOthers>
    <ShowPercents>Show Percents</ShowPercents>
    <ShowPlacemark>Show Placemark</ShowPlacemark>
    <ShowQuietZoneIndicator>Show Quiet Zone Indicator</ShowQuietZoneIndicator>
    <ShowQuietZones>Show Quiet Zones</ShowQuietZones>
    <ShowScrollBar>Show Scroll Bar</ShowScrollBar>
    <ShowSelectAll>Show Select All</ShowSelectAll>
    <ShowSeriesLabels>Show Series Labels</ShowSeriesLabels>
    <ShowShadow>Show Shadow</ShowShadow>
    <ShowTotal>Show Total</ShowTotal>
    <ShowTotalSummary>Show Total Summary</ShowTotalSummary>
    <ShowUpDown>Show Up Down</ShowUpDown>
    <ShowValue>Show Value</ShowValue>
    <ShowXAxis>Show X Axis</ShowXAxis>
    <ShowYAxis>Show Y Axis</ShowYAxis>
    <ShowZeros>Show Zeros</ShowZeros>
    <ShrinkFontToFit>Shrink Font to Fit</ShrinkFontToFit>
    <ShrinkFontToFitMinimumSize>Shrink Font to Fit Minimum Size</ShrinkFontToFitMinimumSize>
    <ShrinkToFit>Shrink to Fit</ShrinkToFit>
    <Side>Side</Side>
    <SideBySide>Side-by-Side</SideBySide>
    <Sides>Sides</Sides>
    <SignatureType>Type</SignatureType>
    <Simple>Simple</Simple>
    <Size>Size</Size>
    <SizeMode>Size Mode</SizeMode>
    <Skin>Skin</Skin>
    <SkipFirst>Skip First</SkipFirst>
    <SkipIndices>Skip Indices</SkipIndices>
    <SkipIndicesObj>Skip Indices</SkipIndicesObj>
    <SkipMajorValues>Skip Major Values</SkipMajorValues>
    <SkipValues>Skip Values</SkipValues>
    <SkipValuesObj>Skip Values</SkipValuesObj>
    <Smoothing>Smoothing</Smoothing>
    <Sort>Sort</Sort>
    <SortBy>Sort by</SortBy>
    <SortDirection>Sort Direction</SortDirection>
    <Sorted>Sorted</Sorted>
    <SortingColumn>Sorting Column</SortingColumn>
    <SortingEnabled>Sorting Enabled</SortingEnabled>
    <SortLetters>A-Z</SortLetters>
    <SortType>Sort Type</SortType>
    <Space>Space</Space>
    <SpaceRatio>Space Ratio</SpaceRatio>
    <Spacing>Spacing</Spacing>
    <Sparkline>Sparkline</Sparkline>
    <SqlCommand>Sql Command</SqlCommand>
    <StartAngle>Start Angle</StartAngle>
    <StartCap>Start Cap</StartCap>
    <StartColor>Start Color</StartColor>
    <StartFromZero>Start From Zero</StartFromZero>
    <StartMode>Start Mode</StartMode>
    <StartNewPage>Start New Page</StartNewPage>
    <StartNewPageIfLessThan>Start New Page if Less Than</StartNewPageIfLessThan>
    <StartPosition>Start Position</StartPosition>
    <StartValue>Start Value</StartValue>
    <StartWidth>Start Width</StartWidth>
    <State>State</State>
    <Step>Step</Step>
    <Stop>Stop</Stop>
    <StopBeforePage>Stop Before Page</StopBeforePage>
    <StopBeforePrint>Stop Before Print</StopBeforePrint>
    <StoreImagesInResources>Store Images in Resources</StoreImagesInResources>
    <StreetAddress>Street Address</StreetAddress>
    <Stretch>Stretch</Stretch>
    <StretchToPrintArea>Stretch to Print Area</StretchToPrintArea>
    <Strikeout>Strikeout</Strikeout>
    <StripBrush>Strip Brush</StripBrush>
    <Strips>Strips</Strips>
    <Stroke>Stroke</Stroke>
    <StructuredAppendPosition>Structured Append Position</StructuredAppendPosition>
    <StructuredAppendTotal>Structured Append Total</StructuredAppendTotal>
    <Style>Style</Style>
    <StyleColors>Style Colors</StyleColors>
    <Styles>Styles</Styles>
    <SubmissionMessage>Submission Message</SubmissionMessage>
    <SubReportPage>Sub Report</SubReportPage>
    <Summaries>Summaries</Summaries>
    <Summary>Summary</Summary>
    <SummaryAlignment>Summary Alignment</SummaryAlignment>
    <SummaryExpression>Summary Expression</SummaryExpression>
    <SummarySortDirection>Summary Sort Direction</SummarySortDirection>
    <SummaryType>Summary Type</SummaryType>
    <SummaryValues>Summary Values</SummaryValues>
    <SupplementCode>Supplement Code</SupplementCode>
    <SupplementType>Supplement Type</SupplementType>
    <SweepAngle>Sweep Angle</SweepAngle>
    <SystemFonts>System Fonts</SystemFonts>
    <SystemVariable>System Variable</SystemVariable>
    <SystemVariables>System Variables</SystemVariables>
    <Table>Table</Table>
    <Tag>Tag</Tag>
    <TagDataColumn>Tag Data Column</TagDataColumn>
    <TagValue>Tag Value</TagValue>
    <Target>Target</Target>
    <TargetColor>Target Color</TargetColor>
    <TargetFormat>Target Format</TargetFormat>
    <TargetIcon>Target Icon</TargetIcon>
    <TargetMode>Target Mode</TargetMode>
    <TargetSettings>Target</TargetSettings>
    <Tension>Tension</Tension>
    <Text>Text</Text>
    <TextAfter>Text After</TextAfter>
    <TextAlign>Text Align</TextAlign>
    <TextAlignment>Text Alignment</TextAlignment>
    <TextAngle>Text Angle</TextAngle>
    <TextBefore>Text Before</TextBefore>
    <TextBrush>Text Brush</TextBrush>
    <TextColor>Text Color</TextColor>
    <TextEnabled>Text Enabled</TextEnabled>
    <TextFont>Text Font</TextFont>
    <TextFormat>Text Format</TextFormat>
    <TextOnly>Text Only</TextOnly>
    <TextOptions>Text Options</TextOptions>
    <TextPlacement>Text Placement</TextPlacement>
    <TextQuality>Text Quality</TextQuality>
    <TextRightToLeft>Text Right to Left</TextRightToLeft>
    <TextShowBehind>Text Show Behind</TextShowBehind>
    <Third>Third</Third>
    <TickLabelMajorFont>Tick Label Major Font</TickLabelMajorFont>
    <TickLabelMajorTextBrush>Tick Label Major Text Brush</TickLabelMajorTextBrush>
    <TickLabelMinorFont>Tick Label Minor Font</TickLabelMinorFont>
    <TickLabelMinorTextBrush>Tick Label Minor Text Brush</TickLabelMinorTextBrush>
    <TickMarkMajorBorder>Tick Mark Major Border</TickMarkMajorBorder>
    <TickMarkMajorBorderWidth>Tick Mark Major Border Width</TickMarkMajorBorderWidth>
    <TickMarkMajorBrush>Tick Mark Major Brush</TickMarkMajorBrush>
    <TickMarkMinorBorder>Tick Mark Minor Border</TickMarkMinorBorder>
    <TickMarkMinorBorderWidth>Tick Mark Minor Border Width</TickMarkMinorBorderWidth>
    <TickMarkMinorBrush>Tick Mark Minor Brush</TickMarkMinorBrush>
    <Ticks>Ticks</Ticks>
    <Title>Title</Title>
    <TitleBackColor>Title Back Color</TitleBackColor>
    <TitleBeforeHeader>Title Before Header</TitleBeforeHeader>
    <TitleColor>Title Color</TitleColor>
    <TitleDirection>TitleDirection</TitleDirection>
    <TitleFont>Title Font</TitleFont>
    <TitleForeColor>Title Fore Color</TitleForeColor>
    <TitleVisible>Title Visible</TitleVisible>
    <To>To</To>
    <Today>Today</Today>
    <ToolTip>Tool Tip</ToolTip>
    <ToolTipBorder>Tool Tip Border</ToolTipBorder>
    <ToolTipBrush>Tool Tip Brush</ToolTipBrush>
    <ToolTipCornerRadius>Tool Tip Corner Radius</ToolTipCornerRadius>
    <ToolTipDataColumn>Tool Tip Data Column</ToolTipDataColumn>
    <ToolTipTextBrush>Tool Tip Text Brush</ToolTipTextBrush>
    <Top>Top</Top>
    <TopLeft>Top-Left</TopLeft>
    <Topmost>Topmost</Topmost>
    <TopmostLine>Topmost Line</TopmostLine>
    <TopN>Top N</TopN>
    <TopRight>Top-Right</TopRight>
    <TopSide>Top Side</TopSide>
    <Total>Total</Total>
    <TotalCellColumnBackColor>Total Cell Column Back Color</TotalCellColumnBackColor>
    <TotalCellColumnForeColor>Total Cell Column Fore Color</TotalCellColumnForeColor>
    <TotalCellRowBackColor>Total Cell Row Back Color</TotalCellRowBackColor>
    <TotalCellRowForeColor>Total Cell Row Fore Color</TotalCellRowForeColor>
    <TotalLabel>Total Label</TotalLabel>
    <Totals>Totals</Totals>
    <TrackColor>Track Color</TrackColor>
    <TransparentColor>Transparent Color</TransparentColor>
    <TrendLine>Trend Line</TrendLine>
    <TrendLineColor>Trend Line Color</TrendLineColor>
    <TrendLines>Trend Lines</TrendLines>
    <TrendLineShowShadow>Trend Line Show Shadow</TrendLineShowShadow>
    <TrimExcessData>Trim Excess Data</TrimExcessData>
    <Trimming>Trimming</Trimming>
    <Type>Type</Type>
    <TypeName>Type Name</TypeName>
    <Types>Types</Types>
    <UncheckedIcon>Unchecked Icon</UncheckedIcon>
    <Underline>Underline</Underline>
    <UndoLimit>Undo Limit</UndoLimit>
    <Unit>Unit</Unit>
    <UnitAlignment>Unit Alignment</UnitAlignment>
    <UnlimitedBreakable>Unlimited Breakable</UnlimitedBreakable>
    <UnlimitedHeight>Unlimited Height</UnlimitedHeight>
    <UnlimitedWidth>Unlimited Width</UnlimitedWidth>
    <UpperMarks>Upper Marks</UpperMarks>
    <URL>URL</URL>
    <UseAliases>Use Aliases</UseAliases>
    <UseExternalReport>Use External Report</UseExternalReport>
    <UseParentStyles>Use Parent Styles</UseParentStyles>
    <UseRangeColor>Use Range Color</UseRangeColor>
    <UserBingKey>User Bing Key</UserBingKey>
    <UseRectangularSymbols>Use Rectangular Symbols</UseRectangularSymbols>
    <UseSeriesColor>Use Series Color</UseSeriesColor>
    <UseStyleOfSummaryInColumnTotal>Use Style of Summary in Column Total</UseStyleOfSummaryInColumnTotal>
    <UseStyleOfSummaryInRowTotal>Use Style of Summary in Row Total</UseStyleOfSummaryInRowTotal>
    <UseUserPicture>Use User Picture</UseUserPicture>
    <UseValuesFromTheSpecifiedRange>Use Values From The Specified Range</UseValuesFromTheSpecifiedRange>
    <Value>Value</Value>
    <ValueClose>Value Close</ValueClose>
    <ValueDataColumn>Value Data Column</ValueDataColumn>
    <ValueDataColumnClose>Value Data Column Close</ValueDataColumnClose>
    <ValueDataColumnEnd>Value Data Column End</ValueDataColumnEnd>
    <ValueDataColumnHigh>Value Data Column High</ValueDataColumnHigh>
    <ValueDataColumnLow>Value Data Column Low</ValueDataColumnLow>
    <ValueDataColumnOpen>Value Data Column Open</ValueDataColumnOpen>
    <ValueEnd>Value End</ValueEnd>
    <ValueFormat>Value Format</ValueFormat>
    <ValueHigh>Value High</ValueHigh>
    <ValueLow>Value Low</ValueLow>
    <ValueMeter>Value Meter</ValueMeter>
    <ValueOpen>Value Open</ValueOpen>
    <Values>Values</Values>
    <ValueType>Value Type</ValueType>
    <ValueTypeSeparator>Value Type Separator</ValueTypeSeparator>
    <Variable>Variable</Variable>
    <Variables>Variables</Variables>
    <Variation>Variation</Variation>
    <Version>Version</Version>
    <VertAlignment>Vertical Alignment</VertAlignment>
    <VertSpacing>Vertical Spacing</VertSpacing>
    <ViewMode>View Mode</ViewMode>
    <Visibility>Visibility</Visibility>
    <Visible>Visible</Visible>
    <VisualStates>Visual States</VisualStates>
    <Watermark>Watermark</Watermark>
    <WatermarkStyle>Watermark Style</WatermarkStyle>
    <WeaveAngle>Weave Angle</WeaveAngle>
    <WeaveDistance>Weave Distance</WeaveDistance>
    <WeaveEnabled>Weave Enabled</WeaveEnabled>
    <WeaveMajorColor>Weave Major Color</WeaveMajorColor>
    <WeaveMajorIcon>Weave Major Icon</WeaveMajorIcon>
    <WeaveMajorSize>Weave Major Size</WeaveMajorSize>
    <WeaveMinorColor>Weave Minor Color</WeaveMinorColor>
    <WeaveMinorIcon>Weave Minor Icon</WeaveMinorIcon>
    <WeaveMinorSize>Weave Minor Size</WeaveMinorSize>
    <Weight>Weight</Weight>
    <WeightDataColumn>Weight Data Column</WeightDataColumn>
    <Weights>Weights</Weights>
    <Width>Width</Width>
    <WindowState>Window State</WindowState>
    <WinLossNegative>Win Loss Negative</WinLossNegative>
    <WinLossPositive>Win Loss Positive</WinLossPositive>
    <WordWrap>Word Wrap</WordWrap>
    <Wrap>Wrap</Wrap>
    <WrapGap>WrapGap</WrapGap>
    <WrapLine>Wrap Line</WrapLine>
    <XAxis>X Axis</XAxis>
    <XTopAxis>X Top Axis</XTopAxis>
    <XValue>Value X</XValue>
    <XValuesDataColumn>Value Data Column X</XValuesDataColumn>
    <YAxis>Y Axis</YAxis>
    <YRightAxis>Y Right Axis</YRightAxis>
    <YValue>Value Y</YValue>
    <YValuesDataColumn>Value Data Column Y</YValuesDataColumn>
    <ZeroColor>Zero Color</ZeroColor>
    <Zoom>Zoom</Zoom>
  </PropertyMain>
  <PropertySystemColors>
    <ActiveBorder>Active Border</ActiveBorder>
    <ActiveCaption>Active Caption</ActiveCaption>
    <ActiveCaptionText>Active Caption Text</ActiveCaptionText>
    <AppWorkspace>App Workspace</AppWorkspace>
    <Control>Control</Control>
    <ControlDark>Control Dark</ControlDark>
    <ControlDarkDark>Control Dark Dark</ControlDarkDark>
    <ControlLight>Control Light</ControlLight>
    <ControlLightLight>Control Light Light</ControlLightLight>
    <ControlText>Control Text</ControlText>
    <Desktop>Desktop</Desktop>
    <GrayText>Gray Text</GrayText>
    <Highlight>Highlight</Highlight>
    <HighlightText>Highlight Text</HighlightText>
    <HotTrack>Hot Track</HotTrack>
    <InactiveBorder>Inactive Border</InactiveBorder>
    <InactiveCaption>Inactive Caption</InactiveCaption>
    <InactiveCaptionText>Inactive Caption Text</InactiveCaptionText>
    <Info>Info</Info>
    <InfoText>Info Text</InfoText>
    <Menu>Menu</Menu>
    <MenuText>Menu Text</MenuText>
    <ScrollBar>Scroll Bar</ScrollBar>
    <Window>Window</Window>
    <WindowFrame>Window Frame</WindowFrame>
    <WindowText>Window Text</WindowText>
  </PropertySystemColors>
  <QueryBuilder>
    <AddObject>Add Object</AddObject>
    <AddSubQuery>Add Derived Table</AddSubQuery>
    <AllObjects>(All objects)</AllObjects>
    <BadFromObjectExpression>Invalid FROM object expression!</BadFromObjectExpression>
    <BadObjectName>Invalid object name!</BadObjectName>
    <BadSelectStatement>Invalid SELECT statement!</BadSelectStatement>
    <Collections>Collections</Collections>
    <CreateLinksFromForeignKeys>Create Links from Foreign Keys</CreateLinksFromForeignKeys>
    <CriteriaAlias>Alias</CriteriaAlias>
    <CriteriaCriteria>Criteria</CriteriaCriteria>
    <CriteriaExpression>Expression</CriteriaExpression>
    <CriteriaGroupBy>Group By</CriteriaGroupBy>
    <CriteriaOr>Or...</CriteriaOr>
    <CriteriaOutput>Output</CriteriaOutput>
    <CriteriaSortOrder>Sort Order</CriteriaSortOrder>
    <CriteriaSortType>Sort Type</CriteriaSortType>
    <Database>Database</Database>
    <DataSourceProperties>Data Source Properties</DataSourceProperties>
    <DialectDontSupportDatabases>The server does not support queries with objects from different databases.</DialectDontSupportDatabases>
    <DialectDontSupportSchemas>The server does not support schemas.</DialectDontSupportSchemas>
    <DialectDontSupportUnions>This server does not support unions.</DialectDontSupportUnions>
    <DialectDontSupportUnionsBrackets>This server does not support brackets in unions.</DialectDontSupportUnionsBrackets>
    <DialectDontSupportUnionsBracketsInSubQuery>This server doesn't support brackets in unions in subqueries.</DialectDontSupportUnionsBracketsInSubQuery>
    <DialectDontSupportUnionsInSubQueries>This server does not support unions in subqueries.</DialectDontSupportUnionsInSubQueries>
    <Edit>Edit</Edit>
    <EncloseWithBrackets>Enclose with brackets</EncloseWithBrackets>
    <Expressions>Expressions</Expressions>
    <InsertEmptyItem>Insert Empty Item</InsertEmptyItem>
    <JoinExpression>Join Expression</JoinExpression>
    <LabelAlias>Alias:</LabelAlias>
    <LabelFilterObjectsBySchemaName>Filter Objects by Schema Name:</LabelFilterObjectsBySchemaName>
    <LabelJoinExpression>Join Expression:</LabelJoinExpression>
    <LabelLeftColumn>Left Column:</LabelLeftColumn>
    <LabelLeftObject>Left Object:</LabelLeftObject>
    <LabelObject>Object:</LabelObject>
    <LabelRightColumn>Right Column:</LabelRightColumn>
    <LabelRightObject>Right Object:</LabelRightObject>
    <LinkProperties>Link Properties</LinkProperties>
    <MetadataProviderCantExecSQL>Used metadata provider cannot execute SQL queries.</MetadataProviderCantExecSQL>
    <MetaProviderCantLoadMetadata>Used metadata provider cannot automatically load metadata.</MetaProviderCantLoadMetadata>
    <MetaProviderCantLoadMetadataForDatabase>Used metadata provider cannot automatically load metadata for database: {0}</MetaProviderCantLoadMetadataForDatabase>
    <MoveDown>Move Down</MoveDown>
    <MoveUp>Move Up</MoveUp>
    <NewUnionSubQuery>New union sub-query</NewUnionSubQuery>
    <NoConnectionObject>No connection object (property {0} not assigned).</NoConnectionObject>
    <NoTransactionObject>No transaction object (property {0} not assigned).</NoTransactionObject>
    <Objects>Objects</Objects>
    <ProcedureParameters>Procedure Parameters</ProcedureParameters>
    <Procedures>Procedures</Procedures>
    <qnSaveChanges>Do you want to save changes of query?</qnSaveChanges>
    <Query>Query</Query>
    <QueryBuilder>Query Builder</QueryBuilder>
    <QueryParameters>Query Parameters</QueryParameters>
    <QueryProperties>Query Properties</QueryProperties>
    <Remove>Remove</Remove>
    <RemoveBrackets>Remove brackets</RemoveBrackets>
    <RunQueryBuilder>Run Query Builder</RunQueryBuilder>
    <SelectAllFromLeft>Select All from Left</SelectAllFromLeft>
    <SelectAllFromRight>Select All from Right</SelectAllFromRight>
    <SwitchToDerivedTable>Switch to Derived Table</SwitchToDerivedTable>
    <Tables>Tables</Tables>
    <UnexpectedTokenAt>Unexpected token "{0}" at line {1}, pos {2}!</UnexpectedTokenAt>
    <Unions>Unions</Unions>
    <UnionSubMenu>Union</UnionSubMenu>
    <ViewQuery>View Query</ViewQuery>
    <Views>Views</Views>
  </QueryBuilder>
  <Questions>
    <qnConfiguration>Please choose the type of configuration for the properties panel. The type of the selected configuration depends on the number of visible properties and their complexity for the developer of reports. You can always change the configuration type from the context menu of the properties panel.</qnConfiguration>
    <qnCopyCategory>Do you want to copy all variables of this category?</qnCopyCategory>
    <qnCovertColumnType>You changed the column type, do you want to enable column data transformation for this data source?</qnCovertColumnType>
    <qnDictionaryNew>Do you want to create new Dictionary?</qnDictionaryNew>
    <qnLanguageNew>You have changed the language of the report. This will lead to the new report code generation. Are you certain you want to save the new language?</qnLanguageNew>
    <qnPageDelete>Do you want to delete page?</qnPageDelete>
    <qnRemove>Do you want to remove?</qnRemove>
    <qnRemoveService>Do you want to remove Service?</qnRemoveService>
    <qnRemoveServiceCategory>Do you want to remove Category?</qnRemoveServiceCategory>
    <qnRemoveUnused>Do you want to remove Unused?</qnRemoveUnused>
    <qnReplace>Do you want to replace the existing item {0}?</qnReplace>
    <qnRestoreDefault>Restore defaults?</qnRestoreDefault>
    <qnSaveChanges>Save changes in {0}?</qnSaveChanges>
    <qnSaveChangesToPreviewPage>Do you want to save page changes?</qnSaveChangesToPreviewPage>
    <qnSynchronize>Synchronized contents of the Data Store and contents of the Dictionary?</qnSynchronize>
    <qnSynchronizeServices>Synchronized services?</qnSynchronizeServices>
  </Questions>
  <RecentFiles>
    <AddAPlace>Add a Place</AddAPlace>
    <AddAPlaceDesc>You can add locations to make it easier to save Reports to the cloud.</AddAPlaceDesc>
    <ChangeFileType>Change file type</ChangeFileType>
    <ChooseALocation>Choose a Location</ChooseALocation>
    <ClearUnpinnedItems>Clear unpinned items</ClearUnpinnedItems>
    <CopyPathToClipboard>Copy path to clipboard</CopyPathToClipboard>
    <DateModified>Date modified</DateModified>
    <Documents>Documents</Documents>
    <DoYouWantTo>Do you want to:</DoYouWantTo>
    <LastWeek>Last Week</LastWeek>
    <LocalDisk>Local Disk ({0})</LocalDisk>
    <MessageInvalidFolderName>Invalid folder name.</MessageInvalidFolderName>
    <MessageThatNameAlreadyExists>That name already exists. Please use a different name.</MessageThatNameAlreadyExists>
    <MoreLocations>More Locations →</MoreLocations>
    <Older>Older</Older>
    <OtherLocations>Other locations</OtherLocations>
    <Personal>Personal</Personal>
    <Pinned>Pinned</Pinned>
    <PinToList>Pin to list</PinToList>
    <PinToRecentList>Pin to Recent list</PinToRecentList>
    <RemoveFromList>Remove from list</RemoveFromList>
    <ReplaceExistingFile>Replace existing file.</ReplaceExistingFile>
    <SaveChangesWithADifferentName>Save changes with a different name.</SaveChangesWithADifferentName>
    <SaveThisFile>Save this file</SaveThisFile>
    <SelectToNavigateUpOneLevel>Select to navigate up one level</SelectToNavigateUpOneLevel>
    <SetAsDefaultLocation>Set as Default Location</SetAsDefaultLocation>
    <ThisPC>This PC</ThisPC>
    <UnpinFromList>Unpin from list</UnpinFromList>
  </RecentFiles>
  <Report>
    <ActiveRelation>Active Relation</ActiveRelation>
    <Address>Address</Address>
    <Alphabetical>Alphabetical</Alphabetical>
    <Bands>Bands</Bands>
    <Basic>Basic</Basic>
    <BasicConfiguration>Minimal number of object properties, which are necessary for rendering the basic report types.</BasicConfiguration>
    <BusinessObjects>Business Objects</BusinessObjects>
    <Categorized>Categorized</Categorized>
    <Charts>Charts</Charts>
    <Checking>Checking...</Checking>
    <ClickForMoreDetails>Click for More Details</ClickForMoreDetails>
    <CollapseAll>Collapse All</CollapseAll>
    <Collection>Collection</Collection>
    <CompilationAccess>Compilation Access</CompilationAccess>
    <CompilingReport>Compiling Report</CompilingReport>
    <Complete>Complete</Complete>
    <Components>Components</Components>
    <ConnectingToData>Connecting to Data</ConnectingToData>
    <CopyOf>Copy</CopyOf>
    <CreateNewReportPageForm>Create a new report, add a page, add a form</CreateNewReportPageForm>
    <CreatingReport>Creating Report</CreatingReport>
    <CrossBands>Cross</CrossBands>
    <Dialogs>Dialogs</Dialogs>
    <EditStyles>Edit Styles</EditStyles>
    <Enhancements>Enhancements</Enhancements>
    <EnterDataManually>Enter data manually</EnterDataManually>
    <Errors>Errors</Errors>
    <EventsAccess>Events Access</EventsAccess>
    <EventsTab>Events Tab</EventsTab>
    <ExpandAll>Expand All</ExpandAll>
    <FilterAnd>And</FilterAnd>
    <FilterOr>Or</FilterOr>
    <FinishingReport>Finishing Report</FinishingReport>
    <FirstPass>First Pass</FirstPass>
    <FixedBugs>Fixed Bugs</FixedBugs>
    <FromCurrentState>From Current State</FromCurrentState>
    <FromDefaultState>From Default State</FromDefaultState>
    <Gallery>Gallery</Gallery>
    <GenerateNewCode>Generate New Code</GenerateNewCode>
    <HideUnlistedExportsInTheViewer>Hide Unlisted Exports in The Viewer</HideUnlistedExportsInTheViewer>
    <History>History</History>
    <IfEmpty>If {0} Is Empty</IfEmpty>
    <Infographics>Infographics</Infographics>
    <InfoMessage>{0} - {1} found.</InfoMessage>
    <InformationMessages>Information Messages</InformationMessages>
    <LabelAlias>Alias:</LabelAlias>
    <LabelAuthor>Author:</LabelAuthor>
    <LabelBackground>Background:</LabelBackground>
    <LabelCategory>Category:</LabelCategory>
    <LabelCentimeters>Centimeters:</LabelCentimeters>
    <LabelCollectionName>Collection Name:</LabelCollectionName>
    <LabelColor>Color:</LabelColor>
    <LabelCountData>Count Data:</LabelCountData>
    <LabelDataBand>DataBand:</LabelDataBand>
    <LabelDataColumn>Data Column:</LabelDataColumn>
    <LabelDefaultValue>Default Value:</LabelDefaultValue>
    <LabelExpression>Expression:</LabelExpression>
    <LabelFactorLevel>Nested Factor:</LabelFactorLevel>
    <LabelFontName>Font Name:</LabelFontName>
    <LabelFunction>Function:</LabelFunction>
    <LabelHundredthsOfInch>Hundredths of Inch:</LabelHundredthsOfInch>
    <LabelInches>Inches:</LabelInches>
    <LabelMillimeters>Millimeters:</LabelMillimeters>
    <LabelName>Name:</LabelName>
    <LabelNameInSource>Name in Source:</LabelNameInSource>
    <LabelNestedLevel>Nested Level:</LabelNestedLevel>
    <LabelPassword>Password:</LabelPassword>
    <LabelPixels>Pixels:</LabelPixels>
    <LabelQueryTimeout>Query Timeout:</LabelQueryTimeout>
    <LabelSystemVariable>System Variable:</LabelSystemVariable>
    <LabelTableOfContentsHint>Please, check the components which should form the table of contents. One component is equal to one heading level.</LabelTableOfContentsHint>
    <LabelTotals>Totals</LabelTotals>
    <LabelType>Type:</LabelType>
    <LabelUserName>User Name:</LabelUserName>
    <LabelValue>Value:</LabelValue>
    <LoadingReport>Loading Report</LoadingReport>
    <nameAssembly>Assembly</nameAssembly>
    <NewFeatures>New Features</NewFeatures>
    <No>No</No>
    <NoFixes>There are no fixes or updates for this version!</NoFixes>
    <NoIssues>No Issues</NoIssues>
    <NoNewVersions>There are no new versions available!</NoNewVersions>
    <NotAssigned>Not Assigned</NotAssigned>
    <Null>Null</Null>
    <Obsoleted>Obsoleted</Obsoleted>
    <Office2010Back>Back</Office2010Back>
    <PageNofM>Page {0} of {1}</PageNofM>
    <PreparingReport>Preparing Report</PreparingReport>
    <Professional>Professional</Professional>
    <ProfessionalConfiguration>All object properties.</ProfessionalConfiguration>
    <PropertiesTab>Properties Tab</PropertiesTab>
    <RangeAll>All</RangeAll>
    <RangeCurrentPage>Current Page</RangeCurrentPage>
    <RangeInfo>Enter page number and/or pages ranges separated by commas. For example: 1, 3, 5-12</RangeInfo>
    <RangePage>Page Range</RangePage>
    <RangePages>Pages:</RangePages>
    <ReportChecker>Checker</ReportChecker>
    <ReportRenderingMessages>Report Rendering Messages</ReportRenderingMessages>
    <RestartDesigner>You need to restart the report designer</RestartDesigner>
    <SaveReportPagesOrFormsFromReport>Save the report, pages or forms</SaveReportPagesOrFormsFromReport>
    <SavingReport>Saving Report</SavingReport>
    <SecondPass>Second Pass</SecondPass>
    <Shapes>Shapes</Shapes>
    <ShowOnParametersPanel>Show on Parameters Panel</ShowOnParametersPanel>
    <Standard>Standard</Standard>
    <StandardConfiguration>Main object properties except rarely used ones.</StandardConfiguration>
    <StiEmptyBrush>Empty</StiEmptyBrush>
    <StiGlareBrush>Glare</StiGlareBrush>
    <StiGlassBrush>Glass</StiGlassBrush>
    <StiGradientBrush>Gradient</StiGradientBrush>
    <StiHatchBrush>Hatch</StiHatchBrush>
    <StiSolidBrush>Solid</StiSolidBrush>
    <StoreUserFirstLastNameInReportAuthor>Store User's First and Last Name in 'Report Author'</StoreUserFirstLastNameInReportAuthor>
    <StoreUserNameInReportAuthor>Store 'User Name' in 'Report Author'</StoreUserNameInReportAuthor>
    <StyleBad>Bad</StyleBad>
    <StyleGood>Good</StyleGood>
    <StyleNeutral>Neutral</StyleNeutral>
    <StyleNormal>Normal</StyleNormal>
    <StyleNote>Note</StyleNote>
    <StyleWarning>Warning</StyleWarning>
    <UseDataFields>Use Data Fields</UseDataFields>
    <VisualChecked>Checked</VisualChecked>
    <VisualDefault>Default</VisualDefault>
    <VisualDisabled>Disabled</VisualDisabled>
    <VisualFocused>Focused</VisualFocused>
    <VisualHover>Hover</VisualHover>
    <VisualPressed>Pressed</VisualPressed>
    <Warnings>Warnings</Warnings>
    <WhatsNewInVersion>What's New in {0}</WhatsNewInVersion>
    <When>when {0} {1}</When>
    <WhenAnd>when {0} {1} and</WhenAnd>
    <WhenValueIs>when value is</WhenValueIs>
  </Report>
  <ReportInfo>
    <CheckIssuesAdditionalDescription>Check for errors and find inaccuracies.</CheckIssuesAdditionalDescription>
    <EncryptWithPassword>Encrypt with Password</EncryptWithPassword>
    <EncryptWithPasswordAdditionalDescription>Set the password to prevent unauthorized working with the report.</EncryptWithPasswordAdditionalDescription>
    <EncryptWithPasswordDescription>Protect Report</EncryptWithPasswordDescription>
    <Info>Info</Info>
    <ReportOptions>Report Options</ReportOptions>
    <ReportOptionsAdditionalDescription>Setup the basic information and parameters in the report.</ReportOptionsAdditionalDescription>
  </ReportInfo>
  <ReportOpen>
    <Browse>Browse</Browse>
    <Import>Import</Import>
  </ReportOpen>
  <Scheduler>
    <ActionRunReport>Run Report</ActionRunReport>
    <AdvancedSettings>Advanced Settings</AdvancedSettings>
    <CheckBoxOverwriteIfExists>Overwrite If Exists</CheckBoxOverwriteIfExists>
    <DayAll>All</DayAll>
    <DayDayX>Day {0}</DayDayX>
    <DayLast>Last</DayLast>
    <DayNone>None</DayNone>
    <DaysOfWeekAll>All</DaysOfWeekAll>
    <DaysOfWeekNone>None</DaysOfWeekNone>
    <DelayTaskForUpToRandomDelay>Delay task for up to (random delay)</DelayTaskForUpToRandomDelay>
    <EditScheduler>Edit Scheduler</EditScheduler>
    <EnableSSL>Enable SSL</EnableSSL>
    <Expire>Expire</Expire>
    <ForADurationOf>for a duration of</ForADurationOf>
    <GetFolder>Get Folder</GetFolder>
    <GroupBoxFiles>Files</GroupBoxFiles>
    <Host>Host</Host>
    <Info>Windows Task Scheduler is used to run schedulers.</Info>
    <LabelDaysOfWeek>Days of Week</LabelDaysOfWeek>
    <LabelMessage>Message</LabelMessage>
    <LabelResultName>Result Name</LabelResultName>
    <LabelRunAtDay>Run at Day</LabelRunAtDay>
    <LabelRunAtMonth>Run at Month</LabelRunAtMonth>
    <LabelRunAtTime>Run at Time</LabelRunAtTime>
    <LabelRunEvery>Run Every</LabelRunEvery>
    <LabelSubject>Subject</LabelSubject>
    <LabelTo>To</LabelTo>
    <Logs>Logs</Logs>
    <MessageInvalidName>Task names may not include any characters which are invalid for file names.</MessageInvalidName>
    <MessageInvalidTaskName>Task names ending with a period followed by three or fewer characters cannot be retrieved due to a bug in the native library.</MessageInvalidTaskName>
    <MessagePropertyNotSet>'{0}' not set!</MessagePropertyNotSet>
    <MonthAll>All</MonthAll>
    <MonthNone>None</MonthNone>
    <NewAction>New Action</NewAction>
    <NewScheduler>New Scheduler</NewScheduler>
    <NoLogs>No Logs</NoLogs>
    <NoSchedulers>No Schedulers</NoSchedulers>
    <NumberOfDayAll>All</NumberOfDayAll>
    <NumberOfDayFirst>First</NumberOfDayFirst>
    <NumberOfDayFourth>Fourth</NumberOfDayFourth>
    <NumberOfDayLast>Last</NumberOfDayLast>
    <NumberOfDayNone>None</NumberOfDayNone>
    <NumberOfDaySecond>Second</NumberOfDaySecond>
    <NumberOfDayThird>Third</NumberOfDayThird>
    <OpenWindowsTaskScheduler>Open Windows Task Scheduler</OpenWindowsTaskScheduler>
    <PleaseSelectSchedulerType>Please select a scheduler type</PleaseSelectSchedulerType>
    <RadioButtonDay>Day</RadioButtonDay>
    <RadioButtonOn>On</RadioButtonOn>
    <RepeatTaskEvery>Repeat task every</RepeatTaskEvery>
    <Schedule>Schedule</Schedule>
    <Scheduler>Scheduler</Scheduler>
    <Schedulers>Schedulers</Schedulers>
    <SenderEmail>Sender Email</SenderEmail>
    <SmtpSettings>Smtp Settings</SmtpSettings>
    <StopAllRunningTasksAtEndOfRepetitionDuration>Stop all running tasks at end of repetition duration</StopAllRunningTasksAtEndOfRepetitionDuration>
    <StopTasksIfItRunsLongerThan>Stop tasks if it runs longer than</StopTasksIfItRunsLongerThan>
    <TextDragDropDestinationHere>Drag &amp; Drop the Destination Folder Here</TextDragDropDestinationHere>
    <TextDragDropReportHere>Drag &amp; Drop the Report Here</TextDragDropReportHere>
    <TextPressPlusIconAddNewAction>Click the icon plus to add a new action!</TextPressPlusIconAddNewAction>
    <ToolTipSetParameters>Defining Report Parameters</ToolTipSetParameters>
    <WizardDaily>Daily</WizardDaily>
    <WizardDailyDescription>Once a day, at a specified time.</WizardDailyDescription>
    <WizardHourly>Hourly</WizardHourly>
    <WizardHourlyDescription>Once an hour.</WizardHourlyDescription>
    <WizardMonthly>Monthly</WizardMonthly>
    <WizardMonthlyDescription>Monthly, on selected days of the week or days of the month, at a specified time.</WizardMonthlyDescription>
    <WizardOnce>Once</WizardOnce>
    <WizardOnceDescription>Run the scheduler once at any time.</WizardOnceDescription>
    <WizardWeekly>Weekly</WizardWeekly>
    <WizardWeeklyDescription>On the selected days of the week, at a specified time.</WizardWeeklyDescription>
  </Scheduler>
  <Services>
    <categoryContextTools>Context Tools</categoryContextTools>
    <categoryDesigner>Designer</categoryDesigner>
    <categoryDictionary>Dictionary</categoryDictionary>
    <categoryExport>Exports</categoryExport>
    <categoryLanguages>Languages</categoryLanguages>
    <categoryPanels>Panels</categoryPanels>
    <categoryRender>Render</categoryRender>
    <categoryShapes>Shapes</categoryShapes>
    <categorySL>Save / Load</categorySL>
    <categorySystem>System</categorySystem>
    <categoryTextFormat>Text Format</categoryTextFormat>
  </Services>
  <Shapes>
    <Arrow>Arrow</Arrow>
    <BasicShapes>Basic Shapes</BasicShapes>
    <BentArrow>Bent Arrow</BentArrow>
    <BlockArrows>Block Arrows</BlockArrows>
    <Chevron>Chevron</Chevron>
    <ComplexArrow>Complex Arrow</ComplexArrow>
    <DiagonalDownLine>Diagonal Line Down</DiagonalDownLine>
    <DiagonalUpLine>Diagonal Line Up</DiagonalUpLine>
    <Division>Division</Division>
    <Equal>Equal</Equal>
    <EquationShapes>Equation Shapes</EquationShapes>
    <Flowchart>Flowchart</Flowchart>
    <FlowchartCard>Flowchart: Card</FlowchartCard>
    <FlowchartCollate>Flowchart: Collate</FlowchartCollate>
    <FlowchartDecision>Flowchart: Decision</FlowchartDecision>
    <FlowchartManualInput>Flowchart: Manual Input</FlowchartManualInput>
    <FlowchartOffPageConnector>Flowchart: Off Page Connector</FlowchartOffPageConnector>
    <FlowchartPreparation>Flowchart: Preparation</FlowchartPreparation>
    <FlowchartSort>Flowchart: Sort</FlowchartSort>
    <Frame>Frame</Frame>
    <HorizontalLine>Horizontal Line</HorizontalLine>
    <InsertShapes>Insert Shapes</InsertShapes>
    <LeftAndRightLine>Left and Right Line</LeftAndRightLine>
    <Lines>Lines</Lines>
    <Minus>Minus</Minus>
    <Multiply>Multiply</Multiply>
    <Octagon>Octagon</Octagon>
    <Oval>Oval</Oval>
    <Parallelogram>Parallelogram</Parallelogram>
    <Plus>Plus</Plus>
    <Rectangle>Rectangle</Rectangle>
    <Rectangles>Rectangles</Rectangles>
    <RegularPentagon>Regular: Pentagon</RegularPentagon>
    <RoundedRectangle>Rounded Rectangle</RoundedRectangle>
    <ServiceCategory>Shapes</ServiceCategory>
    <ShapeStyles>Shape Styles</ShapeStyles>
    <SnipDiagonalSideCornerRectangle>Snip Diagonal Side Corner Rectangle</SnipDiagonalSideCornerRectangle>
    <SnipSameSideCornerRectangle>Snip Same Side Corner Rectangle</SnipSameSideCornerRectangle>
    <TopAndBottomLine>Top and Bottom Line</TopAndBottomLine>
    <Trapezoid>Trapezoid</Trapezoid>
    <Triangle>Triangle</Triangle>
    <VerticalLine>Vertical Line</VerticalLine>
  </Shapes>
  <Signature>
    <ClearSignature>Clear Signature</ClearSignature>
    <DigitalSignatureEmptyWatermark>Place for digital signature in PDF Viewer</DigitalSignatureEmptyWatermark>
    <InsertImage>Insert Image</InsertImage>
    <InsertText>Insert Text</InsertText>
    <LoadSignature>Load signature</LoadSignature>
    <NoSavedItems>No saved items</NoSavedItems>
    <PdfDigitalSignatureTitle>Select the objects that you want to display in the signature on the PDF file.</PdfDigitalSignatureTitle>
    <SaveSignature>Save signature</SaveSignature>
    <SignatureSavedSuccessfully>Signature saved successfully.</SignatureSavedSuccessfully>
    <UseBrush>Use Brush</UseBrush>
  </Signature>
  <StimulAI>
    <ChangedWithAI>Changed with Stimul AI</ChangedWithAI>
    <CheckExpressionSuccess>No issues were found in the specified expression.</CheckExpressionSuccess>
    <CheckQuery>Check Query</CheckQuery>
    <CheckWithStimulAI>Check with Stimul AI</CheckWithStimulAI>
    <ConnectionStringExampleWithAI>Connection string sample with Stimul AI</ConnectionStringExampleWithAI>
    <ConnectionStringTesting>Connection string testing</ConnectionStringTesting>
    <ExplainQuery>Explain Query</ExplainQuery>
    <FixWithAI>Fix with Stimul AI</FixWithAI>
    <GenerateNewQuery>Generate New Query</GenerateNewQuery>
    <OptimizeQuery>Optimize Query</OptimizeQuery>
    <QueryWithStimulAI>Query with Stimul AI</QueryWithStimulAI>
    <Translate>Translate using Stimul AI</Translate>
    <Try>Try with Stimul AI</Try>
    <TryConvertScriptToBlockly>Try converting the previous {0} script into a {1} script.</TryConvertScriptToBlockly>
  </StimulAI>
  <SystemVariables>
    <Column>Returns the current column number (starts from 1).</Column>
    <GroupLine>Returns the current group line number (starts from 1).</GroupLine>
    <IsFirstPage>Returns true, if, in the current moment, the first page of a report is printed.</IsFirstPage>
    <IsFirstPageThrough>Returns true, if, in the current moment, the first report page is printed. When calculating the IsFirstPageThrough, all ResetPageNumber properties are ignored and numbering starts from the beginning of report. For correct calculation of a variable it is required to execute two passes.</IsFirstPageThrough>
    <IsLastPage>Returns true, if, in the current moment, the last page of a report is printed. For correct calculation of a variable it is required to execute two passes.</IsLastPage>
    <IsLastPageThrough>Returns true, if, in the current moment, the last page of a report is printed. When calculating the IsLastPageThrough, all properties ResetPageNumber are ignored and numbering starts from the beginning of report. For correct calculation of a variable it is required to execute two passes.</IsLastPageThrough>
    <Line>Returns the current line number (starts from 1).</Line>
    <LineABC>Returns the alphabetical analogue of the current line number.</LineABC>
    <LineRoman>Returns the current line number in Roman numerals.</LineRoman>
    <LineThrough>Returns the current line number (starts from 1). When calculating the number, all groupings are ignored and numbering starts from the beginning of printing.</LineThrough>
    <PageCopyNumber>Return a number of a current copy of a page (starts from 1).</PageCopyNumber>
    <PageNofM>Returns a localized string, showing "Page N of M" where N is the current page number and M is the TotalPageCount of a report.</PageNofM>
    <PageNofMThrough>Returns a localized string, showing "Page N of M" where N is the current page number and M is the TotalPageCount of a report. When calculating the PageNofMThrough, all properties ResetPageNumber are ignored and numbering starts from the beginning of a report.</PageNofMThrough>
    <PageNumber>Returns the current page number (starts from 1).</PageNumber>
    <PageNumberThrough>Returns the current page number (starts from 1). When calculating the PageNumberThrough, all properties ResetPageNumber are ignored and numbering starts from the beginning of a report.</PageNumberThrough>
    <ReportAlias>Returns the report alias. You can change the ReportAlias with help of the ReportAlias property of a report.</ReportAlias>
    <ReportAuthor>Returns the report author. You can change ReportAuthor with help of the ReportAuthor property of a report.</ReportAuthor>
    <ReportChanged>The Date when a report was changed.</ReportChanged>
    <ReportCreated>The Date when a report was created.</ReportCreated>
    <ReportDescription>Returns the report description. You can change the ReportName with help of the ReportDescription property of a report.</ReportDescription>
    <ReportName>Returns the report name. You can change the ReportName with help of the ReportName property of a report.</ReportName>
    <Time>Returns the current time.</Time>
    <Today>Returns the current date.</Today>
    <TotalPageCount>Returns the number of pages in a report.</TotalPageCount>
    <TotalPageCountThrough>Returns the number of pages in a report. When calculating the TotalPageCountThrough, all properties ResetPageNumber are ignored and numbering starts from the beginning of report.</TotalPageCountThrough>
  </SystemVariables>
  <TableRibbon>
    <BuiltIn>Built-In</BuiltIn>
    <Delete>Delete</Delete>
    <DeleteColumns>Delete Columns</DeleteColumns>
    <DeleteRows>Delete Rows</DeleteRows>
    <DeleteTable>Delete Table</DeleteTable>
    <DistributeColumns>Distribute Columns</DistributeColumns>
    <DistributeRows>Distribute Rows</DistributeRows>
    <InsertAbove>Insert Above</InsertAbove>
    <InsertBelow>Insert Below</InsertBelow>
    <InsertLeft>Insert Left</InsertLeft>
    <InsertRight>Insert Right</InsertRight>
    <PlainTables>Plain Tables</PlainTables>
    <ribbonBarRowsColumns>Rows and Columns</ribbonBarRowsColumns>
    <ribbonBarTable>Table</ribbonBarTable>
    <ribbonBarTableStyles>Table Styles</ribbonBarTableStyles>
    <Select>Select</Select>
    <SelectColumn>Select Column</SelectColumn>
    <SelectRow>Select Row</SelectRow>
    <SelectTable>Select Table</SelectTable>
  </TableRibbon>
  <Toolbars>
    <Align>Align</Align>
    <AlignBottom>Align Bottom</AlignBottom>
    <AlignCenter>Align Center</AlignCenter>
    <AlignLeft>Align Left</AlignLeft>
    <AlignMiddle>Align Middle</AlignMiddle>
    <AlignRight>Align Right</AlignRight>
    <AlignToGrid>Align to Grid</AlignToGrid>
    <AlignTop>Align Top</AlignTop>
    <AlignWidth>Justify</AlignWidth>
    <BringToFront>Bring to Front</BringToFront>
    <CenterHorizontally>Center Horizontally</CenterHorizontally>
    <CenterVertically>Center Vertically</CenterVertically>
    <Conditions>Conditions</Conditions>
    <FontGrow>Grow Font</FontGrow>
    <FontName>Font Name</FontName>
    <FontShrink>Shrink Font</FontShrink>
    <FontSize>Font Size</FontSize>
    <FontStyleBold>Font Style Bold</FontStyleBold>
    <FontStyleItalic>Font Style Italic</FontStyleItalic>
    <FontStyleUnderline>Font Style Underline</FontStyleUnderline>
    <Link>Link</Link>
    <Lock>Lock</Lock>
    <MakeHorizontalSpacingEqual>Make Horizontal Spacing Equal</MakeHorizontalSpacingEqual>
    <MakeSameHeight>Make Same Height as {0}</MakeSameHeight>
    <MakeSameSize>Make Same Size as {0}</MakeSameSize>
    <MakeSameWidth>Make Same Width as {0}</MakeSameWidth>
    <MakeVerticalSpacingEqual>Make Vertical Spacing Equal</MakeVerticalSpacingEqual>
    <MoveBackward>Move Backward</MoveBackward>
    <MoveForward>Move Forward</MoveForward>
    <Order>Order</Order>
    <RibbonClassic>Classic Ribbon</RibbonClassic>
    <RibbonLayout>Ribbon Layout</RibbonLayout>
    <RibbonSingleLine>Single Line Ribbon</RibbonSingleLine>
    <SendToBack>Send to Back</SendToBack>
    <Size>Size</Size>
    <StyleDesigner>Style Designer</StyleDesigner>
    <Styles>List of the styles</Styles>
    <TabHome>Home</TabHome>
    <TabLayout>Layout</TabLayout>
    <TabPage>Page</TabPage>
    <TabView>View</TabView>
    <TextBrush>Text Brush</TextBrush>
    <ToolbarAlignment>Alignment</ToolbarAlignment>
    <ToolbarArrange>Arrange</ToolbarArrange>
    <ToolbarBorders>Borders</ToolbarBorders>
    <ToolbarClipboard>Clipboard</ToolbarClipboard>
    <ToolbarDockStyle>Dock Style</ToolbarDockStyle>
    <ToolbarFont>Font</ToolbarFont>
    <ToolbarFormatting>Formatting</ToolbarFormatting>
    <ToolbarLayout>Layout</ToolbarLayout>
    <ToolbarPageSetup>Page Setup</ToolbarPageSetup>
    <ToolbarStandard>Standard</ToolbarStandard>
    <ToolbarStyle>Style</ToolbarStyle>
    <ToolbarTextFormat>Text Format</ToolbarTextFormat>
    <ToolbarTools>Tools</ToolbarTools>
    <ToolbarViewOptions>View Options</ToolbarViewOptions>
    <ToolbarWatermarkImage>Watermark Image</ToolbarWatermarkImage>
    <ToolbarWatermarkText>Watermark Text</ToolbarWatermarkText>
  </Toolbars>
  <Toolbox>
    <Create>Creation components</Create>
    <Hand>Hand</Hand>
    <Select>Select</Select>
    <Style>Copy Style</Style>
    <TextEditor>Text Editor</TextEditor>
    <title>Toolbox</title>
  </Toolbox>
  <UserFunctionEditor>
    <ArgumentName>Argument Name</ArgumentName>
    <ArgumentType>Argument Type</ArgumentType>
    <ConvertScript>Convert Script</ConvertScript>
    <CustomCategory>Custom Category</CustomCategory>
    <EditCode>Edit Code</EditCode>
    <EditWithBlockly>Edit with blockly</EditWithBlockly>
    <ReturnType>Return Type</ReturnType>
    <Script>Script</Script>
    <ScriptMode>Script Mode</ScriptMode>
    <Title>User Function</Title>
    <UserFunctionEdit>Edit User Function</UserFunctionEdit>
    <UserFunctionNew>New User Function</UserFunctionNew>
  </UserFunctionEditor>
  <WelcomeScreen>
    <AllDownloadsWillCanceled>Are you sure you want to close this window? All downloads will be canceled.</AllDownloadsWillCanceled>
    <Description>We have selected for you reports that we think are the best for a quick start.</Description>
    <GetStarted>Get Started</GetStarted>
    <GetStartedWithDashboards>Get Started with Dashboards</GetStartedWithDashboards>
    <GetStartedWithReports>Get Started with Reports</GetStartedWithReports>
    <MoreReports>More Reports</MoreReports>
    <ShowNextTime>Show '{0}' next time</ShowNextTime>
    <Title>Welcome to Stimulsoft Demo</Title>
  </WelcomeScreen>
  <Wizards>
    <BlankDashboard>Blank Dashboard</BlankDashboard>
    <BlankForm>Blank Form</BlankForm>
    <BlankReport>Blank Report</BlankReport>
    <BlankScreen>Blank Screen</BlankScreen>
    <ButtonBack>&lt; &amp;Back</ButtonBack>
    <ButtonCancel>Cancel</ButtonCancel>
    <ButtonFinish>&amp;Finish</ButtonFinish>
    <ButtonNext>&amp;Next &gt;</ButtonNext>
    <ColumnsOrder>Columns Order</ColumnsOrder>
    <Company>Company</Company>
    <Custom>Custom</Custom>
    <DataRelation>Relation</DataRelation>
    <DataSource>Data Source</DataSource>
    <DataSources>Data Sources</DataSources>
    <DefaultThemes>Default Themes</DefaultThemes>
    <Filters>Filters</Filters>
    <FromReportTemplate>From Report Template</FromReportTemplate>
    <GetData>Get Data</GetData>
    <groupCreateNewDashboard>Create a New Dashboard</groupCreateNewDashboard>
    <groupCreateNewPageOrForm>Create a New Page or Form</groupCreateNewPageOrForm>
    <groupCreateNewReport>Create a New Report</groupCreateNewReport>
    <Groups>Groups</Groups>
    <groupTemplates>Templates</groupTemplates>
    <groupWizards>Wizards</groupWizards>
    <infoColumnsOrder>Arrange columns in the necessary order.</infoColumnsOrder>
    <infoCompanyInfo>Enter your company information.</infoCompanyInfo>
    <infoDataSource>Select one Data Source from available.</infoDataSource>
    <infoDataSources>Select Data Sources from available. The first selected one will be the Master data source.</infoDataSources>
    <infoFilters>Filter Data for your report</infoFilters>
    <infoGroups>Select columns on which necessary to group.</infoGroups>
    <infoLabelSettings>Set the settings of labels.</infoLabelSettings>
    <infoLanguages>Select the report culture.</infoLanguages>
    <infoLayout>Specify layout of report.</infoLayout>
    <infoRelation>Select one Data Relation from available.</infoRelation>
    <infoSelectColumns>Select columns from which the information will be displayed.</infoSelectColumns>
    <infoSelectTemplate>Choose the appropriate template for your needs.</infoSelectTemplate>
    <infoSort>Set the sorting of data. You can sort immediately on multiple columns.</infoSort>
    <infoThemes>Select theme for your report.</infoThemes>
    <infoTotals>Add summary information to your report.</infoTotals>
    <LabelDirection>Direction:</LabelDirection>
    <LabelHeight>Height:</LabelHeight>
    <LabelHorizontalGap>Horizontal Gap:</LabelHorizontalGap>
    <LabelLabelType>Label Type:</LabelLabelType>
    <LabelLeftMargin>Left Margin:</LabelLeftMargin>
    <LabelNumberOfColumns>Number of Columns:</LabelNumberOfColumns>
    <LabelNumberOfRows>Number of Rows:</LabelNumberOfRows>
    <LabelPageHeight>Page Height:</LabelPageHeight>
    <LabelPageWidth>Page Width:</LabelPageWidth>
    <LabelReport>Label Report</LabelReport>
    <LabelSettings>Label Settings</LabelSettings>
    <LabelSize>Size:</LabelSize>
    <LabelTopMargin>Top Margin:</LabelTopMargin>
    <LabelVerticalGap>Vertical Gap:</LabelVerticalGap>
    <LabelWidth>Width:</LabelWidth>
    <Layout>Layout</Layout>
    <Mapping>Mapping</Mapping>
    <MarkAll>Mark &amp;All</MarkAll>
    <MasterDetailReport>Master-Detail Report</MasterDetailReport>
    <NoFunction>[None]</NoFunction>
    <OpenExistingReport>Open Existing File</OpenExistingReport>
    <OpenFrom>Open from {0}</OpenFrom>
    <Preview>Preview</Preview>
    <Reset>&amp;Reset</Reset>
    <Results>Results</Results>
    <RunWizard>Run Wizard</RunWizard>
    <SelectColumns>Select Columns</SelectColumns>
    <SelectTemplate>Template</SelectTemplate>
    <Sort>Sort</Sort>
    <StandardReport>Standard Report</StandardReport>
    <Themes>Themes</Themes>
    <title>New Report</title>
    <Totals>Totals</Totals>
    <UseDemoData>Use Demo Data</UseDemoData>
    <UsingReportWizard>Using Report Wizard</UsingReportWizard>
    <YouHaveNotOpenedAnyReportRecently>You haven't opened any report recently. To browse for a report, start by clicking on Open Existing File.</YouHaveNotOpenedAnyReportRecently>
  </Wizards>
  <Zoom>
    <EmptyValue>Empty Value</EmptyValue>
    <MultiplePages>Multiple Pages</MultiplePages>
    <OnePage>One Page</OnePage>
    <PageHeight>Page Height</PageHeight>
    <PageWidth>Page Width</PageWidth>
    <TwoPages>Two Pages</TwoPages>
    <ZoomTo100>Zoom to 100%</ZoomTo100>
  </Zoom>
</Localization>