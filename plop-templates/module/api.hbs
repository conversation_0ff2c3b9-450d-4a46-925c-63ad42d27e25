import api from '../index'

export default {
  list: (data: {
    title?: string
    from: number
    limit: number
  }) => api.get('{{ kebabCase name }}/list', {
    params: data,
    baseURL: '/mock/',
  }),

  detail: (id: number | string) => api.get('{{ kebabCase name }}/detail', {
    params: {
      id,
    },
    baseURL: '/mock/',
  }),

  create: (data: any) => api.post('{{ kebabCase name }}/create', data, {
    baseURL: '/mock/',
  }),

  edit: (data: any) => api.post('{{ kebabCase name }}/edit', data, {
    baseURL: '/mock/',
  }),

  delete: (id: number | string) => api.post('{{ kebabCase name }}/delete', {
    id,
  }, {
    baseURL: '/mock/',
  }),
}
