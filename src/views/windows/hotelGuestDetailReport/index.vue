<i18n>
{
  "zh": {
    "search": {
      "store": "门店",
      "checkinDate": "入住日期",
      "date": "日期",
      "timeType": "时间类型",
      "statChannel": "统计渠道",
      "guestSource": "客源",
      "state": "入住状态",
      "operator": "操作员",
      "shift": "班次",
      "currentRoomNo": "现房号",
      "currentRoomNoPlaceholder": "输入现房号",
      "originalRoomNo": "原发生房号",
      "originalRoomNoPlaceholder": "输入原发生房号",
      "query": "查询",
      "selectStore": "请选择门店",
      "selectChannel": "请选择统计渠道",
      "selectGuestSource": "请选择客源",
      "selectState": "请选择入住状态",
      "selectOperator": "请选择操作员",
      "selectShift": "请选择班次",
      "startDate": "开始日期",
      "endDate": "结束日期",
      "to": "至",
      "today": "今天",
      "thisWeek": "本周",
      "thisMonth": "本月",
      "checkoutDate": "离店日期"
    }
  },
  "en": {
    "search": {
      "store": "Store",
      "checkinDate": "Checkin Date",
      "date": "Date",
      "checkoutDate": "Checkout Date",
      "timeType": "Time Type",
      "statChannel": "Channel",
      "guestSource": "Source",
      "state": "State",
      "operator": "Operator",
      "shift": "Shift",
      "currentRoomNo": "Room No",
      "currentRoomNoPlaceholder": "Enter Current Room No",
      "originalRoomNo": "Old Room",
      "originalRoomNoPlaceholder": "Enter Original Room No",
      "query": "Search",
      "selectStore": "Select Store",
      "selectChannel": "Select Channel",
      "selectGuestSource": "Select Guest Source",
      "selectState": "Select State",
      "selectOperator": "Select Operator",
      "selectShift": "Select Shift",
      "startDate": "Start Date",
      "endDate": "End Date",
      "to": "to",
      "today": "Today",
      "thisWeek": "This Week",
      "thisMonth": "This Month"
    }
  },
  "km": {
    "search": {
      "store": "ហាង",
      "checkinDate": "កាលបរិច្ឆេទចូលស្នាក់នៅ",
      "date": "កាលបរិច្ឆេទ",
      "checkoutDate": "កាលបរិច្ឆេទចាកចេញ",
      "timeType": "ប្រភេទពេលវេលា",
      "statChannel": "ឆានែលស្ថិតិ",
      "guestSource": "ប្រភពភ្ញៀវ",
      "state": "ស្ថានភាពស្នាក់នៅ",
      "operator": "ប្រតិបត្តិករ",
      "shift": "វេន",
      "currentRoomNo": "លេខបន្ទប់បច្ចុប្បន្ន",
      "currentRoomNoPlaceholder": "បញ្ចូលលេខបន្ទប់បច្ចុប្បន្ន",
      "originalRoomNo": "លេខបន្ទប់ដើម",
      "originalRoomNoPlaceholder": "បញ្ចូលលេខបន្ទប់ដើម",
      "query": "ស្វែងរក",
      "selectStore": "សូមជ្រើសរើសហាង",
      "selectChannel": "សូមជ្រើសរើសឆានែលស្ថិតិ",
      "selectGuestSource": "សូមជ្រើសរើសប្រភពភ្ញៀវ",
      "selectState": "សូមជ្រើសរើសស្ថានភាពស្នាក់នៅ",
      "selectOperator": "សូមជ្រើសរើសប្រតិបត្តិករ",
      "selectShift": "សូមជ្រើសរើសវេន",
      "startDate": "កាលបរិច្ឆេទចាប់ផ្តើម",
      "endDate": "កាលបរិច្ឆេទបញ្ចប់",
      "to": "ដល់",
      "today": "ថ្ងៃនេះ",
      "thisWeek": "សប្តាហ៍នេះ",
      "thisMonth": "ខែនេះ"
    }
  }
}
</i18n>

<script setup lang="ts">
import { dayjs } from 'element-plus'
import { useI18n } from 'vue-i18n'
import {
  hotelGuestDetailReportApi,
  simpleListApi,
  simpleUserListApi,
} from '@/api/modules/pms/shiftTimeApi.ts'
import useUserStore from '@/store/modules/user.ts'
import { getFirstDayOfMonth, getMonday } from '@/utils/tool'
import { ReportUtils } from '@/utils/report-utils.ts'

const userStore = useUserStore()
const { t } = useI18n()
const route = useRoute()

const data = ref({
  searchFold: false,
  // 表格是否自适应高度
  tableAutoHeight: true,
  labelState: '1',
  bizDate: [dayjs().subtract(dayjs().daysInMonth(), 'day').toDate(), dayjs().toDate()] as any, // 起始时间为当月默认天数，结束时间为当天
  search: {
    gcode: userStore.gcode,
    hcode: userStore.hcode,
    timeType: '1',
    shiftNos: [] as string[],
    orderSource: undefined,
    orderSource2: undefined,
    guestSrcType: '', // 客源类型
    state: [] as string[], // 消费科目
    operator: '', // 操作员
    // name: '',
    // name2: '',
    statChannel: '',
  },
})

const shortcuts = [
  {
    text: t('search.today'),
    value: () => {
      const end = new Date()
      const start = new Date()
      return [start, end]
    },
  },
  {
    text: t('search.thisWeek'),
    value: () => {
      const end = new Date()
      const start = getMonday()
      return [start, end]
    },
  },
  {
    text: t('search.thisMonth'),
    value: () => {
      const end = new Date()
      const start = getFirstDayOfMonth()
      return [start, end]
    },
  },
]

const timeList = [
  { code: '1', label: t('search.checkinDate') },
  { code: '2', label: t('search.checkoutDate') },
]

const simpleList = ref<{ hcode: string, hname: string }[]>([])
const operators = ref<{ username: string, nickname: string }[]>([])

// 监听门店变化，重新查询相关数据
watch(() => data.value.search.hcode, (newHcode) => {
  if (newHcode) {
    // 切换门店时清空已选择的数据
    data.value.search.operator = ''
    data.value.search.state = []
    data.value.search.timeType = '1'

    // 重新加载数据
    getOperatorList()
    // 不自动查询报表，等用户点击查询按钮时再查询
  }
})

const stateList = ref([
  { state: 'check_in', stateName: '在住' },
  { state: 'check_out', stateName: '已退房' },
  { state: 'credit', stateName: '挂账' },
])
const loading = ref(false)
function onSearch() {
  loading.value = true
  const timeData = {
    startDate: dayjs(data.value.bizDate[0]).format('YYYY-MM-DD'),
    endDate: dayjs(data.value.bizDate[1]).format('YYYY-MM-DD'),
  }
  hotelGuestDetailReportApi({
    ...data.value.search,
    ...timeData,
  }).then((res) => {
    nextTick(() => {
      setJson(res)
      loading.value = false
    })
  }).catch(() => {
    loading.value = false
  })
}

async function setJson(json: any) {
  const viewer = ReportUtils.getViewer('hotelGuestDetailReportStiViewer')
  const report = ReportUtils.getReportObj('reports/hotelGuestDetailReport.mrt', json)

  ReportUtils.loadData(viewer, report, 'hotelGuestDetailReport')
}

function onChangeTime() { }

async function getsimpleList() {
  const params = {
    gcode: userStore.gcode,
    hcode: data.value.search.hcode,
  }
  await simpleListApi(params).then((res) => {
    simpleList.value = res.data
  })
}

async function getOperatorList() {
  const params = {
    gcode: userStore.gcode,
    hcode: data.value.search.hcode,
  }
  await simpleUserListApi(params).then((res) => {
    operators.value = res.data
  })
}

async function getQueryParams() {
  const queryParams = route.query || {}
  if (!queryParams || JSON.stringify(queryParams) === '{}') {
    return
  }
  if (!queryParams.name || queryParams.name != 'hotelGuestDetailReport') {
    return
  }

  // 开始结束时间
  if (queryParams.startDate && queryParams.endDate) {
    data.value.bizDate = [queryParams.startDate, queryParams.endDate]
  }
  if (queryParams.hcode) {
    data.value.search.hcode = String(queryParams.hcode)
  }
  if (queryParams.subCode) {
    data.value.search.state.push(String(queryParams.state))
  }
  if (queryParams.operator) {
    data.value.search.operator = String(queryParams.operator)
  }
}

onMounted(async () => {
  await getQueryParams()
  await getsimpleList()
  await getOperatorList()
  onSearch()
})
// -----------最多只能选择一个月，且只能选昨天之前的-----------------
const startVal = ref(null)
/** 选择第一个时间的当月天数 */
const daysInMonth = ref(0)
function calendarChange(e) {
  if (e[0] && !e[1]) {
    startVal.value = e[0]
    daysInMonth.value = dayjs(e[0]).daysInMonth()
  }
}
/** 选择第一个时间 */
function visibleChange(e) {
  if (e) {
    startVal.value = null
  }
}
/** 禁止选择一个上下自然月之外时间 */
function disabledDate(time: Date) {
  const flag_yestodayAfter = dayjs(time).isAfter(dayjs().subtract(0, 'day'), 'day')
  if (!startVal.value) { return flag_yestodayAfter }
  const range = [dayjs(startVal.value).subtract(daysInMonth.value, 'day'), dayjs(startVal.value).add(daysInMonth.value, 'day')]
  const cur = dayjs(time)
  return cur < range[0] || cur > range[1] || flag_yestodayAfter
}
// -----------最多只能选择一个月，且只能选今天(包含)之前的-----------------
</script>

<template>
  <div :class="{ 'absolute-container': data.tableAutoHeight }">
    <page-main>
      <search-bar :fold="data.searchFold" :show-toggle="false">
        <el-form :model="data.search" size="default" label-width="80px" inline-message inline class="search-form">
          <el-form-item :label="t('search.store')" style="width: 260px;">
            <el-select v-model="data.search.hcode" :placeholder="t('search.selectStore')">
              <el-option v-for="item in simpleList" :key="item.hcode" :label="item.hname" :value="item.hcode" />
            </el-select>
          </el-form-item>
          <el-form-item :label="t('search.date')">
            <el-select
              v-model="data.search.timeType" style="display: inline-block;width: 100px;"
              @change="onChangeTime"
            >
              <el-option v-for="item in timeList" :key="item.code" :label="item.label" :value="item.code" />
            </el-select>
            <el-date-picker
              v-if="data.search.timeType === '1'" v-model="data.bizDate" type="daterange"
              :range-separator="t('search.to')" :start-placeholder="t('search.startDate')" :end-placeholder="t('search.endDate')" :shortcuts="shortcuts"
              :clearable="false"
              :disabled-date="disabledDate"
              @calendar-change="calendarChange"
              @visible-change="visibleChange"
            />
            <el-date-picker
              v-else v-model="data.bizDate" type="daterange" :range-separator="t('search.to')"
              :start-placeholder="t('search.startDate')" :end-placeholder="t('search.endDate')" :shortcuts="shortcuts" :clearable="false"
            />
          </el-form-item>
          <el-form-item :label="t('search.state')" style="width: 260px;">
            <el-select v-model="data.search.state" style="width: 200px;" :placeholder="t('search.selectState')" clearable>
              <el-option v-for="item in stateList" :key="item.state" :label="item.stateName" :value="item.state" />
            </el-select>
          </el-form-item>
          <el-form-item :label="t('search.operator')" style="width: 260px;">
            <el-select v-model="data.search.operator" :placeholder="t('search.selectOperator')" style="width: 150px;" clearable>
              <el-option v-for="item in operators" :key="item.username" :label="item.nickname" :value="item.username" />
            </el-select>
          </el-form-item>
          <!--          <el-form-item :label="t('search.currentRoomNo')" style="width: 300px;">
            <el-input v-model="data.search.name" :placeholder="t('search.currentRoomNoPlaceholder')" />
          </el-form-item>
          <el-form-item :label="t('search.originalRoomNo')" label-width="100px" style="width: 300px;">
            <el-input v-model="data.search.name" :placeholder="t('search.originalRoomNoPlaceholder')" />
          </el-form-item> -->
          <el-form-item>
            <el-button type="primary" @click="onSearch">
              {{ t('search.query') }}
            </el-button>
          </el-form-item>
        </el-form>
      </search-bar>
      <div id="hotelGuestDetailReport">
        <el-skeleton :loading="loading" animated :rows="30" />
      </div>
    </page-main>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-radio) {
  margin-right: 14px;
}
</style>
