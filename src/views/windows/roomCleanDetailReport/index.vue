<i18n>
{
  "zh": {
    "search": {
      "store": "门店",
      "bizDate": "营业日",
      "query": "查询",
      "selectStore": "请选择门店",
      "startDate": "开始日期",
      "endDate": "结束日期",
      "to": "至",
      "today": "今天",
      "thisWeek": "本周",
      "thisMonth": "本月",
      "cleanState": "原房态",
      "cleaner": "房扫人员",
      "roomType": "房型",
      "cleanStatePlaceholder": "请输入原房态",
      "cleanerPlaceholder": "请选择房扫人员",
      "roomTypePlaceholder": "请选择房型列表",
      "storePlaceholder": "请选择门店"
    }
  },
  "en": {
    "search": {
      "store": "Store",
      "bizDate": "Biz Date",
      "query": "Search",
      "selectStore": "Select Store",
      "startDate": "Start Date",
      "endDate": "End Date",
      "to": "to",
      "today": "Today",
      "thisWeek": "This Week",
      "thisMonth": "This Month",
      "cleanState": "Room Status",
      "cleaner": "Cleaner",
      "roomType": "Type",
      "cleanStatePlaceholder": "Enter Original Room Status",
      "cleanerPlaceholder": "Select Cleaner",
      "roomTypePlaceholder": "Select Room Type List",
      "storePlaceholder": "Select Store"
    }
  },
  "km": {
    "search": {
      "store": "ហាង",
      "bizDate": "ថ្ងៃធ្វើអាជីវកម្ម",
      "query": "ស្វែងរក",
      "selectStore": "សូមជ្រើសរើសហាង",
      "startDate": "កាលបរិច្ឆេទចាប់ផ្តើម",
      "endDate": "កាលបរិច្ឆេទបញ្ចប់",
      "to": "ទៅ",
      "today": "ថ្ងៃនេះ",
      "thisWeek": "សប្តាហ៍នេះ",
      "thisMonth": "ខែនេះ",
      "cleanState": "ស្ថានភាពបន្ទប់ដើម",
      "cleaner": "បុគ្គលិកសំអាតបន្ទប់",
      "roomType": "ប្រភេទបន្ទប់",
      "cleanStatePlaceholder": "សូមបញ្ចូលស្ថានភាពបន្ទប់ដើម",
      "cleanerPlaceholder": "សូមជ្រើសរើសបុគ្គលិកសំអាតបន្ទប់",
      "roomTypePlaceholder": "សូមជ្រើសរើសបញ្ជីប្រភេទបន្ទប់",
      "storePlaceholder": "សូមជ្រើសរើសហាង"
    }
  }
}
</i18n>

<script setup lang="ts">
import { dayjs } from 'element-plus'
import { useI18n } from 'vue-i18n'
import {
  dictDataByTypeApi,
  listCleanerApi,
  roomCleanDetailReportApi,
  roomTypeSimpleListApi,
  simpleListApi,
} from '@/api/modules/pms/shiftTimeApi.ts'
import useUserStore from '@/store/modules/user.ts'
import { getFirstDayOfMonth, getMonday } from '@/utils/tool'
import { ReportUtils } from '@/utils/report-utils.ts'

const userStore = useUserStore()
const { t } = useI18n()

const data = ref({
  searchFold: false,
  // 表格是否自适应高度
  tableAutoHeight: true,
  labelState: '1',
  bizDate: [dayjs().subtract(dayjs().daysInMonth(), 'day').toDate(), dayjs().toDate()] as any, // 起始时间为当月默认天数，结束时间为当天
  search: {
    gcode: userStore.gcode,
    hcode: userStore.hcode,
    cleaner: '', // 清洁员
    rtCodes: [] as string[], // 房型
    cleanState: '',
  },
})

const shortcuts = [
  {
    text: t('search.today'),
    value: () => {
      const end = new Date()
      const start = new Date()
      return [start, end]
    },
  },
  {
    text: t('search.thisWeek'),
    value: () => {
      const end = new Date()
      const start = getMonday()
      return [start, end]
    },
  },
  {
    text: t('search.thisMonth'),
    value: () => {
      const end = new Date()
      const start = getFirstDayOfMonth()
      return [start, end]
    },
  },
]

const simpleList = ref<{ hcode: string, hname: string }[]>([])
const cleanerList = ref<{ username: string, nickname: string }[]>([])
const roomTypeList = ref<{ rtCode: string, rtName: string }[]>([])
const cleanStateList = ref<{ code: string, label: string }[]>([])

// 监听门店变化，重新查询相关数据
watch(() => data.value.search.hcode, (newHcode) => {
  if (newHcode) {
    // 切换门店时清空已选择的数据
    data.value.search.rtCodes = []
    data.value.search.cleanState = ''
    data.value.search.cleaner = ''

    // 重新加载数据
    getRoomTypeList()
    getCleanStateList()
    getCleanerList()
    // 不自动查询报表，等用户点击查询按钮时再查询
  }
})
const loading = ref(false)
function onSearch() {
  loading.value = true
  const timeData = {
    startDate: dayjs(data.value.bizDate[0]).format('YYYY-MM-DD'),
    endDate: dayjs(data.value.bizDate[1]).format('YYYY-MM-DD'),
  }
  roomCleanDetailReportApi({
    ...data.value.search,
    ...timeData,
  }).then((res) => {
    nextTick(() => {
      setJson(res)
      loading.value = false
    })
  }).catch(() => {
    loading.value = false
  })
}

async function setJson(json: any) {
  const viewer = ReportUtils.getViewer('roomCleanDetailStiViewer')
  const report = ReportUtils.getReportObj('reports/roomCleanDetailReport.mrt', json)
  ReportUtils.loadData(viewer, report, 'RoomCleanDetailReport')
}

async function getsimpleList() {
  const params = {
    gcode: userStore.gcode,
    hcode: data.value.search.hcode,
  }
  await simpleListApi(params).then((res) => {
    simpleList.value = res.data
  })
}

async function getCleanStateList() {
  await dictDataByTypeApi({
    gcode: userStore.gcode,
    hcode: data.value.search.hcode,
    dictType: 'room_status',
  }).then((res: any) => {
    const list = [] as any
    const data = [] as any
    res.data.forEach((item: any) => {
      if (!!item.code && !list.includes(item.code)) {
        list.push(item.code)
        data.push(item)
      }
    })
    cleanStateList.value = data
  })
}

async function getCleanerList() {
  const params = {
    gcode: userStore.gcode,
    hcode: data.value.search.hcode,
  }
  await listCleanerApi(params).then((res) => {
    cleanerList.value = res.data
  })
}

async function getRoomTypeList() {
  const params = {
    gcode: userStore.gcode,
    hcode: data.value.search.hcode,
    isEnable: '1',
    isVirtual: '0',
  }
  await roomTypeSimpleListApi(params).then((res) => {
    roomTypeList.value = res.data
  })
}

onMounted(async () => {
  await getsimpleList()
  await getRoomTypeList()
  await getCleanStateList()
  await getCleanerList()
  onSearch()
})
// -----------最多只能选择一个月，且只能选昨天之前的-----------------
const startVal = ref(null)
/** 选择第一个时间的当月天数 */
const daysInMonth = ref(0)
function calendarChange(e) {
  if (e[0] && !e[1]) {
    startVal.value = e[0]
    daysInMonth.value = dayjs(e[0]).daysInMonth()
  }
}
/** 选择第一个时间 */
function visibleChange(e) {
  if (e) {
    startVal.value = null
  }
}
/** 禁止选择一个上下自然月之外时间 */
function disabledDate(time: Date) {
  const flag_yestodayAfter = dayjs(time).isAfter(dayjs().subtract(0, 'day'), 'day')
  if (!startVal.value) {
    return flag_yestodayAfter
  }
  const range = [dayjs(startVal.value).subtract(daysInMonth.value, 'day'), dayjs(startVal.value).add(daysInMonth.value, 'day')]
  const cur = dayjs(time)
  return cur < range[0] || cur > range[1] || flag_yestodayAfter
}
// -----------最多只能选择一个月，且只能选今天(包含)之前的-----------------
</script>

<template>
  <div :class="{ 'absolute-container': data.tableAutoHeight }">
    <page-main>
      <search-bar :fold="data.searchFold" :show-toggle="false">
        <el-form :model="data.search" size="default" label-width="80px" inline-message inline class="search-form">
          <el-form-item :label="t('search.store')" style="width: 260px;">
            <el-select v-model="data.search.hcode" :placeholder="t('search.selectStore')">
              <el-option v-for="item in simpleList" :key="item.hcode" :label="item.hname" :value="item.hcode" />
            </el-select>
          </el-form-item>
          <el-form-item :label="t('search.bizDate')">
            <el-date-picker
              v-model="data.bizDate" type="daterange"
              :range-separator="t('search.to')" :start-placeholder="t('search.startDate')"
              :end-placeholder="t('search.endDate')" :shortcuts="shortcuts"
              :clearable="false"
              :disabled-date="disabledDate"
              @calendar-change="calendarChange"
              @visible-change="visibleChange"
            />
          </el-form-item>
          <el-form-item :label="t('search.cleanState')" style="width: 260px;">
            <el-select v-model="data.search.cleanState" :placeholder="t('search.cleanStatePlaceholder')" clearable>
              <el-option v-for="item in cleanStateList" :key="item.code" :label="item.label" :value="item.code" />
            </el-select>
          </el-form-item>
          <el-form-item :label="t('search.cleaner')" style="width: 260px;">
            <el-select v-model="data.search.cleaner" :placeholder="t('search.cleanerPlaceholder')" clearable>
              <el-option v-for="item in cleanerList" :key="item.username" :label="item.nickname" :value="item.username" />
            </el-select>
          </el-form-item>
          <el-form-item :label="t('search.roomType')" style="width: 260px;">
            <el-select v-model="data.search.rtCodes" multiple style="width: 200px;" :placeholder="t('search.roomTypePlaceholder')" clearable>
              <el-option v-for="item in roomTypeList" :key="item.rtCode" :label="item.rtName" :value="item.rtCode" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSearch">
              {{ t('search.query') }}
            </el-button>
          </el-form-item>
        </el-form>
      </search-bar>
      <div id="RoomCleanDetailReport">
        <el-skeleton :loading="loading" animated :rows="30" />
      </div>
    </page-main>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-radio) {
  margin-right: 14px;
}
</style>
