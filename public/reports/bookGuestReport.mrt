<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <CalculationMode>Interpretation</CalculationMode>
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="1">
      <bookGuestReport Ref="2" type="Stimulsoft.Report.Dictionary.StiJsonDatabase" isKey="true">
        <Alias>bookGuestReport</Alias>
        <HeadersString />
        <Key />
        <Name>bookGuestReport</Name>
        <PathData>D:\11\2\pms-report\public\reports\json\bookGuestReport.json</PathData>
      </bookGuestReport>
    </Databases>
    <DataSources isList="true" count="4">
      <root Ref="3" type="DataTableSource" isKey="true">
        <Alias>root</Alias>
        <Columns isList="true" count="4">
          <value>code,System.Decimal</value>
          <value>data,System.String</value>
          <value>msg,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>08203f2e688d4f3eb24dc2e7392bf507</Key>
        <Name>root</Name>
        <NameInSource>bookGuestReport.root</NameInSource>
      </root>
      <root_data Ref="4" type="DataTableSource" isKey="true">
        <Alias>root_data</Alias>
        <Columns isList="true" count="7">
          <value>hname,System.String</value>
          <value>hcode,System.String</value>
          <value>bookDate,System.String</value>
          <value>lastSelectTime,System.String</value>
          <value>prepayAmountSum,System.Decimal</value>
          <value>list,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>ac0d1c36ac5d4605b0237a05d66fe5a9</Key>
        <Name>root_data</Name>
        <NameInSource>bookGuestReport.root_data</NameInSource>
      </root_data>
      <root_data_list Ref="5" type="DataTableSource" isKey="true">
        <Alias>root_data_list</Alias>
        <Columns isList="true" count="4">
          <value>bookNo,System.String</value>
          <value>url,System.String</value>
          <value>bookList,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>9aa4df876fc64b819bc6d3d7f0cd1555</Key>
        <Name>root_data_list</Name>
        <NameInSource>bookGuestReport.root_data_list</NameInSource>
      </root_data_list>
      <root_data_list_bookList Ref="6" type="DataTableSource" isKey="true">
        <Alias>root_data_list_bookList</Alias>
        <Columns isList="true" count="21">
          <value>serialNo,System.Decimal</value>
          <value>orderNo,System.String</value>
          <value>name,System.String</value>
          <value>phone,System.String</value>
          <value>planCheckinTime,System.String</value>
          <value>planCheckoutTime,System.String</value>
          <value>roomType,System.String</value>
          <value>roomCount,System.String</value>
          <value>roomPrice,System.Decimal</value>
          <value>prepayAmount,System.Decimal</value>
          <value>retainTime,System.String</value>
          <value>guarantyStyle,System.String</value>
          <value>guarantyStyleName,System.String</value>
          <value>orderSource,System.String</value>
          <value>orderSourceName,System.String</value>
          <value>guestSrcType,System.String</value>
          <value>guestSrcTypeName,System.String</value>
          <value>levelOrCompanyName,System.String</value>
          <value>remark,System.String</value>
          <value>rNo,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>315534e29a4643c193b97867cc9c8df4</Key>
        <Name>root_data_list_bookList</Name>
        <NameInSource>bookGuestReport.root_data_list_bookList</NameInSource>
      </root_data_list_bookList>
    </DataSources>
    <Relations isList="true" count="3">
      <root Ref="7" type="DataRelation" isKey="true">
        <Alias>root</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="4" />
        <Dictionary isRef="1" />
        <Name>root</Name>
        <NameInSource>root_data</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>data</value>
        </ParentColumns>
        <ParentSource isRef="3" />
      </root>
      <root_data Ref="8" type="DataRelation" isKey="true">
        <Alias>root_data</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="5" />
        <Dictionary isRef="1" />
        <Name>root_data</Name>
        <NameInSource>root_data_list</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>list</value>
        </ParentColumns>
        <ParentSource isRef="4" />
      </root_data>
      <root_data_list Ref="9" type="DataRelation" isKey="true">
        <Alias>root_data_list</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="6" />
        <Dictionary isRef="1" />
        <Name>root_data_list</Name>
        <NameInSource>root_data_list_bookList</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>bookList</value>
        </ParentColumns>
        <ParentSource isRef="5" />
      </root_data_list>
    </Relations>
    <Report isRef="0" />
    <Resources isList="true" count="0" />
    <UserFunctions isList="true" count="0" />
    <Variables isList="true" count="0" />
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <Key>1b71202e7c5b45c8926992a03486d2d1</Key>
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="10" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="5">
        <PageHeaderBand1 Ref="11" type="PageHeaderBand" isKey="true">
          <Border>Bottom;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,41,0.8</ClientRectangle>
          <Components isList="true" count="2">
            <Text2 Ref="12" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0.01,0,7,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Name>Text2</Name>
              <Page isRef="10" />
              <Parent isRef="11" />
              <Text>{root_data.hname}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text2>
            <Text6 Ref="13" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>34.4,0,5.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Name>Text6</Name>
              <Page isRef="10" />
              <Parent isRef="11" />
              <Text>第{PageNumber}页,共{TotalPageCount}页</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text6>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>PageHeaderBand1</Name>
          <Page isRef="10" />
          <Parent isRef="10" />
        </PageHeaderBand1>
        <ReportTitleBand1 Ref="14" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,2,41,2</ClientRectangle>
          <Components isList="true" count="2">
            <Text1 Ref="15" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>18.1,0,5.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,12,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text1</Name>
              <Page isRef="10" />
              <Parent isRef="14" />
              <Text>今日预抵客人报表</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text1>
            <Text5 Ref="16" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.2,20.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Name>Text5</Name>
              <Page isRef="10" />
              <Parent isRef="14" />
              <Text>门店:{root_data.hname}   预抵日期:{root_data.bookDate}  最后查询时间：{root_data.lastSelectTime}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text5>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>ReportTitleBand1</Name>
          <Page isRef="10" />
          <Parent isRef="10" />
        </ReportTitleBand1>
        <页眉1 Ref="17" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,4.8,41,0.8</ClientRectangle>
          <Components isList="true" count="15">
            <Text10 Ref="18" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5.6,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text10</Name>
              <Page isRef="10" />
              <Parent isRef="17" />
              <Text>联系人</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text10>
            <Text4 Ref="19" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.2,0,4.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text4</Name>
              <Page isRef="10" />
              <Parent isRef="17" />
              <Text>预订单号</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text4>
            <Text3 Ref="20" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,1.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text3</Name>
              <Page isRef="10" />
              <Parent isRef="17" />
              <Text>序号</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text3>
            <Text11 Ref="21" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.8,0,3.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text11</Name>
              <Page isRef="10" />
              <Parent isRef="17" />
              <Text>预抵时间</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text11>
            <文本3 Ref="22" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>11.4,0,3.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Interaction Ref="23" type="Stimulsoft.Report.Components.StiInteraction" isKey="true">
                <SortingColumn>数据区2.planCheckoutTime</SortingColumn>
              </Interaction>
              <Name>文本3</Name>
              <Page isRef="10" />
              <Parent isRef="17" />
              <Text>预离时间</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本3>
            <Text14 Ref="24" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15,0,4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text14</Name>
              <Page isRef="10" />
              <Parent isRef="17" />
              <Text>房型</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text14>
            <文本1 Ref="25" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>19,0,2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本1</Name>
              <Page isRef="10" />
              <Parent isRef="17" />
              <Text>预分配房号</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本1>
            <文本5 Ref="26" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>21,0,2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Interaction Ref="27" type="Stimulsoft.Report.Components.StiInteraction" isKey="true">
                <SortingColumn>数据区2.roomPrice</SortingColumn>
              </Interaction>
              <Name>文本5</Name>
              <Page isRef="10" />
              <Parent isRef="17" />
              <Text>房价</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本5>
            <文本6 Ref="28" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>23,0,2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Interaction Ref="29" type="Stimulsoft.Report.Components.StiInteraction" isKey="true">
                <SortingColumn>数据区2.prepayAmount</SortingColumn>
              </Interaction>
              <Name>文本6</Name>
              <Page isRef="10" />
              <Parent isRef="17" />
              <Text>预付款</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本6>
            <文本7 Ref="30" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>25,0,3.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Interaction Ref="31" type="Stimulsoft.Report.Components.StiInteraction" isKey="true">
                <SortingColumn>数据区2.retainTime</SortingColumn>
              </Interaction>
              <Margins>0,5,0,0</Margins>
              <Name>文本7</Name>
              <Page isRef="10" />
              <Parent isRef="17" />
              <Text>保留时间</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本7>
            <文本14 Ref="32" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>28.6,0,2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本14</Name>
              <Page isRef="10" />
              <Parent isRef="17" />
              <Text>担保方式</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本14>
            <文本15 Ref="33" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>30.6,0,2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本15</Name>
              <Page isRef="10" />
              <Parent isRef="17" />
              <Text>订单来源</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本15>
            <文本16 Ref="34" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>32.6,0,2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本16</Name>
              <Page isRef="10" />
              <Parent isRef="17" />
              <Text>客源</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本16>
            <文本17 Ref="35" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>34.6,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本17</Name>
              <Page isRef="10" />
              <Parent isRef="17" />
              <Text>会员级别/公司</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本17>
            <文本18 Ref="36" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>37,0,4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本18</Name>
              <Page isRef="10" />
              <Parent isRef="17" />
              <Text>备注</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本18>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>页眉1</Name>
          <Page isRef="10" />
          <Parent isRef="10" />
        </页眉1>
        <数据区1 Ref="37" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,6.4,41,0.6</ClientRectangle>
          <Components isList="true" count="3">
            <面板1 Ref="38" type="Panel" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <CanShrink>True</CanShrink>
              <ClientRectangle>5.6,0,35.4,1.4</ClientRectangle>
              <Components isList="true" count="1">
                <数据区2 Ref="39" type="DataBand" isKey="true">
                  <Brush>Transparent</Brush>
                  <BusinessObjectGuid isNull="true" />
                  <ClientRectangle>0,0.4,35.4,0.8</ClientRectangle>
                  <Components isList="true" count="13">
                    <文本11 Ref="40" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <CanGrow>True</CanGrow>
                      <ClientRectangle>0,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <GrowToHeight>True</GrowToHeight>
                      <Margins>5,0,0,0</Margins>
                      <Name>文本11</Name>
                      <Page isRef="10" />
                      <Parent isRef="39" />
                      <Text>{root_data_list_bookList.name}</Text>
                      <TextBrush>Black</TextBrush>
                      <TextFormat Ref="41" type="DateFormat" isKey="true">
                        <StringFormat>G</StringFormat>
                      </TextFormat>
                      <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本11>
                    <文本13 Ref="42" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <CanGrow>True</CanGrow>
                      <ClientRectangle>2.2,0,3.6,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <GrowToHeight>True</GrowToHeight>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本13</Name>
                      <Page isRef="10" />
                      <Parent isRef="39" />
                      <Text>{root_data_list_bookList.planCheckinTime}</Text>
                      <TextBrush>Black</TextBrush>
                      <TextFormat Ref="43" type="DateFormat" isKey="true">
                        <StringFormat>G</StringFormat>
                      </TextFormat>
                      <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本13>
                    <文本2 Ref="44" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <CanGrow>True</CanGrow>
                      <ClientRectangle>23,0,2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <GrowToHeight>True</GrowToHeight>
                      <Margins>5,0,0,0</Margins>
                      <Name>文本2</Name>
                      <Page isRef="10" />
                      <Parent isRef="39" />
                      <Text>{root_data_list_bookList.guarantyStyleName}</Text>
                      <TextBrush>Black</TextBrush>
                      <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本2>
                    <文本9 Ref="45" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <CanGrow>True</CanGrow>
                      <ClientRectangle>19.4,0,3.6,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <GrowToHeight>True</GrowToHeight>
                      <HorAlignment>Center</HorAlignment>
                      <Margins>5,0,0,0</Margins>
                      <Name>文本9</Name>
                      <Page isRef="10" />
                      <Parent isRef="39" />
                      <Text>{root_data_list_bookList.retainTime}</Text>
                      <TextBrush>Black</TextBrush>
                      <TextFormat Ref="46" type="DateFormat" isKey="true">
                        <StringFormat>G</StringFormat>
                      </TextFormat>
                      <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本9>
                    <文本10 Ref="47" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <CanGrow>True</CanGrow>
                      <ClientRectangle>17.4,0,2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <GrowToHeight>True</GrowToHeight>
                      <HorAlignment>Right</HorAlignment>
                      <Margins>0,5,0,0</Margins>
                      <Name>文本10</Name>
                      <Page isRef="10" />
                      <Parent isRef="39" />
                      <Text>{root_data_list_bookList.prepayAmount}</Text>
                      <TextBrush>Black</TextBrush>
                      <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本10>
                    <文本8 Ref="48" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <CanGrow>True</CanGrow>
                      <ClientRectangle>15.4,0,2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <GrowToHeight>True</GrowToHeight>
                      <HorAlignment>Right</HorAlignment>
                      <Margins>0,5,0,0</Margins>
                      <Name>文本8</Name>
                      <Page isRef="10" />
                      <Parent isRef="39" />
                      <Text>{root_data_list_bookList.roomPrice}</Text>
                      <TextBrush>Black</TextBrush>
                      <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本8>
                    <Text27 Ref="49" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <CanGrow>True</CanGrow>
                      <ClientRectangle>13.4,0,2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <GrowToHeight>True</GrowToHeight>
                      <HorAlignment>Right</HorAlignment>
                      <Margins>0,5,0,0</Margins>
                      <Name>Text27</Name>
                      <Page isRef="10" />
                      <Parent isRef="39" />
                      <Text>{root_data_list_bookList.rNo}</Text>
                      <TextBrush>Black</TextBrush>
                      <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </Text27>
                    <Text26 Ref="50" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <CanGrow>True</CanGrow>
                      <ClientRectangle>9.4,0,4,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <GrowToHeight>True</GrowToHeight>
                      <Margins>5,0,0,0</Margins>
                      <Name>Text26</Name>
                      <Page isRef="10" />
                      <Parent isRef="39" />
                      <Text>{root_data_list_bookList.roomType}</Text>
                      <TextBrush>Black</TextBrush>
                      <TextFormat Ref="51" type="DateFormat" isKey="true" />
                      <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </Text26>
                    <文本4 Ref="52" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <CanGrow>True</CanGrow>
                      <ClientRectangle>5.8,0,3.6,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <GrowToHeight>True</GrowToHeight>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本4</Name>
                      <Page isRef="10" />
                      <Parent isRef="39" />
                      <Text>{root_data_list_bookList.planCheckoutTime}</Text>
                      <TextBrush>Black</TextBrush>
                      <TextFormat Ref="53" type="DateFormat" isKey="true">
                        <StringFormat>G</StringFormat>
                      </TextFormat>
                      <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本4>
                    <文本19 Ref="54" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <CanGrow>True</CanGrow>
                      <ClientRectangle>25,0,2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <GrowToHeight>True</GrowToHeight>
                      <Margins>5,0,0,0</Margins>
                      <Name>文本19</Name>
                      <Page isRef="10" />
                      <Parent isRef="39" />
                      <Text>{root_data_list_bookList.orderSourceName}</Text>
                      <TextBrush>Black</TextBrush>
                      <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本19>
                    <文本20 Ref="55" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <CanGrow>True</CanGrow>
                      <ClientRectangle>27,0,2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <GrowToHeight>True</GrowToHeight>
                      <Margins>5,0,0,0</Margins>
                      <Name>文本20</Name>
                      <Page isRef="10" />
                      <Parent isRef="39" />
                      <Text>{root_data_list_bookList.guestSrcTypeName}</Text>
                      <TextBrush>Black</TextBrush>
                      <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本20>
                    <文本21 Ref="56" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <CanGrow>True</CanGrow>
                      <ClientRectangle>29,0,2.4,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <GrowToHeight>True</GrowToHeight>
                      <Margins>5,0,0,0</Margins>
                      <Name>文本21</Name>
                      <Page isRef="10" />
                      <Parent isRef="39" />
                      <Text>{root_data_list_bookList.levelOrCompanyName}</Text>
                      <TextBrush>Black</TextBrush>
                      <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本21>
                    <文本22 Ref="57" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <CanGrow>True</CanGrow>
                      <ClientRectangle>31.4,0,4,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <GrowToHeight>True</GrowToHeight>
                      <Margins>5,0,0,0</Margins>
                      <Name>文本22</Name>
                      <Page isRef="10" />
                      <Parent isRef="39" />
                      <Text>{root_data_list_bookList.remark}</Text>
                      <TextBrush>Black</TextBrush>
                      <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本22>
                  </Components>
                  <Conditions isList="true" count="0" />
                  <DataRelationName>root_data_list_bookList</DataRelationName>
                  <DataSourceName>root_data_list_bookList</DataSourceName>
                  <Expressions isList="true" count="0" />
                  <Filters isList="true" count="0" />
                  <MasterComponent isRef="37" />
                  <Name>数据区2</Name>
                  <Page isRef="10" />
                  <Parent isRef="38" />
                  <Sort isList="true" count="0" />
                </数据区2>
              </Components>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <GrowToHeight>True</GrowToHeight>
              <Name>面板1</Name>
              <Page isRef="10" />
              <Parent isRef="37" />
            </面板1>
            <文本12 Ref="58" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <CanShrink>True</CanShrink>
              <ClientRectangle>1.2,0,4.4,1.2</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Hyperlink>{(root_data_list.url) ?? ""}</Hyperlink>
              <Margins>5,0,0,0</Margins>
              <Name>文本12</Name>
              <Page isRef="10" />
              <Parent isRef="37" />
              <Text>{root_data_list.bookNo}</Text>
              <TextBrush>[0:176:240]</TextBrush>
              <TextFormat Ref="59" type="DateFormat" isKey="true">
                <StringFormat>G</StringFormat>
              </TextFormat>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本12>
            <面板2 Ref="60" type="Panel" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,1.2,0.8</ClientRectangle>
              <Components isList="true" count="1">
                <数据区3 Ref="61" type="DataBand" isKey="true">
                  <Brush>Transparent</Brush>
                  <BusinessObjectGuid isNull="true" />
                  <ClientRectangle>0,0.4,1.2,0.8</ClientRectangle>
                  <Components isList="true" count="1">
                    <Text17 Ref="62" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <CanGrow>True</CanGrow>
                      <ClientRectangle>0,0,1.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <GrowToHeight>True</GrowToHeight>
                      <HorAlignment>Center</HorAlignment>
                      <Name>Text17</Name>
                      <Page isRef="10" />
                      <Parent isRef="61" />
                      <Text>{root_data_list_bookList.serialNo}</Text>
                      <TextBrush>Black</TextBrush>
                      <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </Text17>
                  </Components>
                  <Conditions isList="true" count="0" />
                  <DataRelationName>root_data_list_bookList</DataRelationName>
                  <DataSourceName>root_data_list_bookList</DataSourceName>
                  <Expressions isList="true" count="0" />
                  <Filters isList="true" count="0" />
                  <MasterComponent isRef="37" />
                  <Name>数据区3</Name>
                  <Page isRef="10" />
                  <Parent isRef="60" />
                  <Sort isList="true" count="0" />
                </数据区3>
              </Components>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <GrowToHeight>True</GrowToHeight>
              <Name>面板2</Name>
              <Page isRef="10" />
              <Parent isRef="37" />
            </面板2>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>root_data_list</DataSourceName>
          <Expressions isList="true" count="0" />
          <Filters isList="true" count="0" />
          <Name>数据区1</Name>
          <Page isRef="10" />
          <Parent isRef="10" />
          <Sort isList="true" count="0" />
        </数据区1>
        <页脚1 Ref="63" type="FooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,7.8,41,0.8</ClientRectangle>
          <Components isList="true" count="8">
            <文本23 Ref="64" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,23,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本23</Name>
              <Page isRef="10" />
              <Parent isRef="63" />
              <Text>合计</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本23>
            <文本24 Ref="65" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>23,0,2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本24</Name>
              <Page isRef="10" />
              <Parent isRef="63" />
              <Text>{root_data.prepayAmountSum}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本24>
            <文本25 Ref="66" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>25,0,3.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本25</Name>
              <Page isRef="10" />
              <Parent isRef="63" />
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本25>
            <文本26 Ref="67" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>28.6,0,2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本26</Name>
              <Page isRef="10" />
              <Parent isRef="63" />
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本26>
            <文本27 Ref="68" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>30.6,0,2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本27</Name>
              <Page isRef="10" />
              <Parent isRef="63" />
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本27>
            <文本28 Ref="69" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>32.6,0,2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本28</Name>
              <Page isRef="10" />
              <Parent isRef="63" />
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本28>
            <文本29 Ref="70" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>34.6,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本29</Name>
              <Page isRef="10" />
              <Parent isRef="63" />
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本29>
            <文本30 Ref="71" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>37,0,4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本30</Name>
              <Page isRef="10" />
              <Parent isRef="63" />
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本30>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>页脚1</Name>
          <Page isRef="10" />
          <Parent isRef="10" />
        </页脚1>
      </Components>
      <Conditions isList="true" count="0" />
      <Expressions isList="true" count="0" />
      <Guid>0ffe2267e98e41918d553a35f450f054</Guid>
      <Margins>1,1,1,1</Margins>
      <Name>Page1</Name>
      <PageHeight>29.7</PageHeight>
      <PageWidth>43</PageWidth>
      <Report isRef="0" />
    </Page1>
  </Pages>
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>报表</ReportAlias>
  <ReportAuthor>xiao lin</ReportAuthor>
  <ReportChanged>7/1/2025 2:39:24 PM</ReportChanged>
  <ReportCreated>5/23/2025 9:26:24 AM</ReportCreated>
  <ReportFile>D:\11\2\pms-report\public\reports\bookGuestReport.mrt</ReportFile>
  <ReportGuid>138ccd6b575c45a2909959eb297cbb10</ReportGuid>
  <ReportName>报表</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2024.3.5.0</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class 报表 : Stimulsoft.Report.StiReport
    {
        public 报表()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
		#endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>