<script setup lang="ts">
import Sortable from 'sortablejs'
import ContextMenu from '@imengyu/vue3-context-menu'
import '@imengyu/vue3-context-menu/lib/vue3-context-menu.css'
import { useI18n } from 'vue-i18n'
import hotkeys from 'hotkeys-js'
import Message from 'vue-m-message'
import { OverlayScrollbarsComponent } from 'overlayscrollbars-vue'
import Empty from './Empty/index.vue'
import useSettingsStore from '@/store/modules/settings'
import useWindowStore from '@/store/modules/window'
import useFavoritesStore from '@/store/modules/favorites'
import storage from '@/utils/storage'
import eventBus from '@/utils/eventBus'
import useMenuBadgeStore from '@/store/modules/menuBadge'
import pinia from '@/store'

defineOptions({
  name: 'Dashboard',
})

const menuBadgeStore = useMenuBadgeStore(pinia)

const settingsStore = useSettingsStore()
const windowStore = useWindowStore()
const favoritesStore = useFavoritesStore()
const { t } = useI18n()

const appWindow = useWindow()
const { generateI18nTitle } = useMenu()

const previewAllMode = ref('preview')

const windowList = computed({
  get() {
    return windowStore.list
  },
  set(value) {
    windowStore.sort(value)
  },
})

const windowsRef = ref()
const dashboardContainerRef = ref()
const windowItemRef = ref<any>([])
const setWindowItemRef = (el: any) => windowItemRef.value.push(el)
onBeforeUpdate(() => {
  windowItemRef.value = []
})

const isDragging = ref(false)
// eslint-disable-next-line unused-imports/no-unused-vars
let dashboardSortable: Sortable
onMounted(() => {
  dashboardSortable = new Sortable(dashboardContainerRef.value.$el, {
    animation: 200,
    ghostClass: 'window-ghost',
    draggable: '.window',
    handle: '.draggable',
    onStart: () => {
      isDragging.value = true
    },
    onEnd: () => {
      isDragging.value = false
    },
    onUpdate: (e) => {
      if (e.newIndex !== undefined && e.oldIndex !== undefined) {
        windowList.value.splice(e.newIndex, 0, windowList.value.splice(e.oldIndex, 1)[0])
      }
    },
  })
})

const focusList = ref<string[]>([])
const focusEnabled = ref(false)
function focusToggle(windowName: string) {
  if (focusList.value.includes(windowName)) {
    focusList.value = focusList.value.filter(item => item !== windowName)
  }
  else {
    if (focusList.value.length < settingsStore.settings.window.focusMaxNum) {
      focusList.value.push(windowName)
    }
    else {
      Message.warning(`最多可勾选不超过 ${settingsStore.settings.window.focusMaxNum} 个窗口`, {
        zIndex: 2000,
      })
    }
  }
}
function intoFocus() {
  focusEnabled.value = true
  exitPreviewAllWindows()
}

watch(() => windowStore.list, (val) => {
  nextTick(() => {
    if (val.length > 0) {
      windowScrollTip()
    }
  })
}, {
  deep: true,
})

const showWindowScrollTip = ref(false)
function windowScrollTip() {
  if (windowsRef.value.scrollWidth > windowsRef.value.clientWidth && !storage.local.has('windowScrollTip')) {
    showWindowScrollTip.value = true
    storage.local.set('windowScrollTip', '')
  }
}

// 记录进入窗口预览界面前 scrollLeft 的值，退出的时候可以进行复原
const originalScrollLeft = ref(0)
watch(() => settingsStore.previewAllWindows, (val) => {
  if (val) {
    previewAllMode.value = 'preview'
    focusList.value = []
    originalScrollLeft.value = windowsRef.value.scrollLeft
  }
})

onMounted(() => {
  hotkeys('alt+w', (e) => {
    if (settingsStore.settings.window.enableHotkeys && windowStore.list.length > 1 && windowStore.list.every(item => !item.isMaximize)) {
      e.preventDefault()
      if (settingsStore.previewAllWindows) {
        exitPreviewAllWindows()
      }
      else {
        settingsStore.$patch({
          previewAllWindows: true,
        })
      }
    }
  })
})

function scrollToOriginal(scrollLeft: number) {
  nextTick(() => {
    windowsRef.value.scroll(scrollLeft, 0)
  })
}
function scrollToWindow(windowName: string | unknown) {
  nextTick(() => {
    const index = windowStore.list.findIndex(item => item.name === windowName)
    const offsetLeft = windowItemRef.value[index].offsetLeft
    windowsRef.value.scrollTo({
      left: offsetLeft,
      behavior: 'smooth',
    })
  })
}
function scrollToPrevWindow(windowName: string) {
  const index = windowStore.list.findIndex(item => item.name === windowName)
  if (index > 0) {
    scrollToWindow(windowStore.list[index - 1].name)
  }
}
function scrollToNextWindow(windowName: string) {
  const index = windowStore.list.findIndex(item => item.name === windowName)
  if (index + 1 <= windowStore.list.length) {
    scrollToWindow(windowStore.list[index + 1].name)
  }
}

eventBus.on('scrollToWindow', scrollToWindow)

const showWindowFocusTip = ref(false)

function onWindowContextmenu(event: MouseEvent, windowItem: window) {
  event.preventDefault()
  ContextMenu.showContextMenu({
    x: event.x,
    y: event.y,
    zIndex: 3,
    iconFontClass: '',
    customClass: 'contextmenu-custom',
    items: [
      {
        label: t('app.window.reload'),
        icon: 'i-ri:refresh-line',
        onClick: () => appWindow.reload(windowItem.name),
      },
      {
        label: t('app.window.remove'),
        icon: 'i-ri:close-line',
        divided: true,
        onClick: () => appWindow.remove(windowItem.name),
      },
      {
        label: t('app.window.maximize'),
        icon: 'i-ri:picture-in-picture-exit-line',
        onClick: () => appWindow.toggleMaximize(windowItem.name),
      },
      {
        label: t('app.window.focus'),
        icon: 'i-ri:focus-3-line',
        divided: true,
        onClick: () => {
          scrollToWindow(windowItem.name)
          if (!storage.local.has('windowFocusTip')) {
            showWindowFocusTip.value = true
            storage.local.set('windowFocusTip', '')
          }
        },
      },
      {
        label: t('app.window.focusPrev'),
        disabled: windowStore.list[0].name === windowItem.name,
        onClick: () => scrollToPrevWindow(windowItem.name),
      },
      {
        label: t('app.window.focusNext'),
        disabled: windowStore.list[windowStore.list.length - 1].name === windowItem.name,
        divided: true,
        onClick: () => scrollToNextWindow(windowItem.name),
      },
      {
        label: t('app.window.removeOtherSide'),
        disabled: !windowStore.list.some(item => item.name !== windowItem.name),
        onClick: () => {
          windowStore.removeOtherSide(windowItem.name)
        },
      },
      {
        label: t('app.window.removeLeftSide'),
        disabled: windowStore.list[0].name === windowItem.name,
        onClick: () => {
          windowStore.removeLeftSide(windowItem.name)
        },
      },
      {
        label: t('app.window.removeRightSide'),
        disabled: windowStore.list[windowStore.list.length - 1].name === windowItem.name,
        onClick: () => {
          windowStore.removeRightSide(windowItem.name)
        },
      },
    ],
  })
}

function exitPreviewAllWindows() {
  if (settingsStore.previewAllWindows) {
    settingsStore.$patch({
      previewAllWindows: false,
    })
    scrollToOriginal(originalScrollLeft.value)
  }
}
function exitFocus() {
  if (focusEnabled.value) {
    focusEnabled.value = false
    focusList.value = []
  }
}
function maskClick(windowName: string) {
  exitPreviewAllWindows()
  setTimeout(() => {
    scrollToWindow(windowName)
  }, 0)
}

// 收藏
function onCollection(element: any) {
  // console.log(2222222222,element);
  // MenuBadgeExample.children[0].badge = true
  favoritesStore.isAdd(element.name) ? favoritesStore.remove(element.name) : favoritesStore.add(element.name)
}
</script>

<template>
  <div
    ref="windowsRef" class="dashboard" :class="{
      [`scroll-snap-${settingsStore.settings.window.autoPosition}`]: settingsStore.settings.window.autoPosition !== '',
      'preview-all': settingsStore.previewAllWindows,
      'focus': focusEnabled,
    }" @click="exitPreviewAllWindows"
  >
    <div class="preview-all-mode">
      <HTabList
        v-model="previewAllMode" :options="[
          { label: '预览', value: 'preview' },
          { label: '排序', value: 'drag' },
        ]" class="w-sm" @click.stop
      />
    </div>
    <TransitionGroup
      v-show="windowStore.list.length > 0 && windowStore.isAllWindowsVisible" ref="dashboardContainerRef" :name="!isDragging ? 'window' : undefined" tag="div" class="dashboard-container" :class="{
        'mode-drag': settingsStore.previewAllWindows && previewAllMode === 'drag',
        'dragging': isDragging,
      }"
    >
      <div
        v-for="element in windowList" :key="element.name" :ref="setWindowItemRef" class="window" :class="{
          'window-focus': focusEnabled && focusList.findIndex(item => item === element.name) >= 0,
        }" :style="element.width
          ? `--g-window-width: ${typeof element.width === 'string' ? element.width : `${element.width}px`};`
          : `--g-window-width: ${typeof settingsStore.settings.window.defaultWidth === 'string' ? settingsStore.settings.window.defaultWidth : `${settingsStore.settings.window.defaultWidth}px`};`
        "
      >
        <div
          class="window-container" :class="{
            maximize: element.isMaximize,
            preview: settingsStore.previewAllWindows,
          }"
        >
          <div
            v-if="!element.noTitle" class="header" @dblclick="scrollToWindow(element.name)"
            @contextmenu="onWindowContextmenu($event, element)"
          >
            <div class="titles">
              <HTooltip
                v-if="element.title"
                :text="element.breadcrumbNeste?.map(bc => generateI18nTitle(bc.title)).join(' / ')"
                placement="bottom-start" :delay="500"
              >
                <span class="title">
                  {{ generateI18nTitle(element.title) }}
                </span>
              </HTooltip>
            </div>
            <div class="btns" @dblclick.stop>
              <div
                v-if="settingsStore.settings.toolbar.favorites && favoritesStore.canAdd(element.name)" class="btn"
                @click="onCollection(element)"
              >
                <SvgIcon :name="favoritesStore.isAdd(element.name) ? 'i-tabler:star-filled' : 'i-tabler:star'" />
              </div>
              <div class="btn" @click="appWindow.toggleMaximize(element.name)">
                <SvgIcon name="i-ri:picture-in-picture-exit-line" />
              </div>
              <div class="btn" @click="appWindow.remove(element.name)">
                <SvgIcon name="i-ep:close-bold" />
              </div>
            </div>
          </div>
          <div class="maximize-exit" @click="appWindow.toggleMaximize(element.name)">
            <SvgIcon name="i-ri:picture-in-picture-2-line" class="icon" />
          </div>
          <div class="flex-1 of-auto overscroll-contain">
            <OverlayScrollbarsComponent
              :options="{ scrollbars: { autoHide: 'leave', autoHideDelay: 300 } }" defer
              class="h-full"
            >
              <Component
                :is="element.name" v-if="!element.reload" :is-maximize="element.isMaximize"
                :params="element.params"
              />
            </OverlayScrollbarsComponent>
          </div>
          <div class="mask" @click="maskClick(element.name)">
            <div
              class="w-full flex-center flex-1 cursor-pointer pt-[10%] text-5xl c-stone-3 text-shadow transition dark-c-stone-7 hover-c-stone-5"
            >
              点击进入
            </div>
            <div
              class="h-[20%] w-full flex-center cursor-pointer border-t border-t-stone-3 border-t-dashed c-stone-3 transition dark-border-t-stone-7 dark-c-stone-7 hover-c-stone-5"
              :class="{ 'c-stone-5': focusList.includes(element.name) }" @click.stop="focusToggle(element.name)"
            >
              <SvgIcon
                :name="focusList.includes(element.name) ? 'i-material-symbols:check-box' : 'i-material-symbols:check-box-outline'"
                :size="60"
              />
            </div>
          </div>
          <div class="draggable" @click.stop>
            <div class="title">
              {{ generateI18nTitle(element.title) }}
            </div>
          </div>
        </div>
      </div>
    </TransitionGroup>
    <Empty v-show="windowStore.list.length === 0 || !windowStore.isAllWindowsVisible" />
  </div>
  <div
    class="preview-all-focus-actions" :class="{
      show: ((settingsStore.previewAllWindows && previewAllMode === 'preview') || focusEnabled) && focusList.length > 0,
    }"
  >
    <span v-if="focusList.length === 1">再勾选一个窗口，可开启专注模式</span>
    <HButton
      v-if="settingsStore.previewAllWindows && previewAllMode === 'preview' && focusList.length > 1"
      @click.stop="intoFocus"
    >
      进入专注模式
    </HButton>
    <HButton v-else-if="focusEnabled" @click.stop="exitFocus">
      退出专注模式
    </HButton>
  </div>
  <HDialog v-model="showWindowFocusTip" title="温馨提示">
    <div class="text-center text-sm">
      你可以通过双击标题栏快速聚焦窗口
    </div>
  </HDialog>
  <HDialog v-model="showWindowScrollTip" title="温馨提示">
    <div class="text-sm">
      当前窗口数量已超过浏览器展示区域，你可以通过拖动窗口下方的滚动条进行定位，除此之外，我们推荐使用以下三种更高效的方式进行窗口定位：
      <ol>
        <li>在窗口展示区域内，按住 Shift 键，滑动鼠标滚轮</li>
        <li>在窗口标题区域，双击或右键选择“聚焦”选项</li>
        <li>使用 Alt + W 快捷键进入预览界面</li>
      </ol>
    </div>
  </HDialog>
</template>

<style>
.contextmenu-custom {
  --uno: fixed ring-1 ring-stone-2 dark-ring-stone-7 shadow-2xl;

  background-color: var(--g-app-bg);

  .mx-context-menu-items .mx-context-menu-item {
    --uno: transition-background-color;

    &:not(.disabled):hover {
      --uno: cursor-pointer bg-stone-1 dark-bg-stone-9;
    }

    span {
      color: initial;
    }

    .icon {
      color: initial;
    }

    &.disabled span,
    &.disabled .icon {
      opacity: 0.25;
    }
  }

  .mx-context-menu-item-sperator {
    background-color: var(--g-app-bg);

    &::after {
      --uno: bg-stone-2 dark-bg-stone-7;
    }
  }
}
</style>

<style scoped>
.dashboard {
  --uno: absolute top-0 bottom-0 w-full of-x-auto of-y-hidden transition;

  &[class*="scroll-snap"] {
    scroll-snap-type: x mandatory;
  }

  &.scroll-snap-start .window {
    scroll-snap-align: start;
  }

  &.scroll-snap-center .window {
    scroll-snap-align: center;
  }

  &.scroll-snap-end .window {
    scroll-snap-align: end;
  }

  &.preview-all {
    --uno: fixed z-2000 top-0 bottom-0 left-0 right-0 of-y-auto bg-stone-200/75 dark-bg-stone-8/75 backdrop-blur-sm;

    .preview-all-mode {
      display: block;
    }

    .dashboard-container {
      --uno: flex-wrap justify-center bottom-a h-max pb-15;

      &:not(.mode-drag) {
        align-items: center;

        .window {
          height: var(--g-window-height);
          margin-inline-end: calc(var(--g-window-width) * (var(--g-window-perview-scale) - 1));
          margin-bottom: calc(var(--g-window-height) * (var(--g-window-perview-scale) - 1));
          user-select: none;
          transform: scale(var(--g-window-perview-scale));
          transform-origin: top left;
        }
      }

      &.mode-drag .window {
        width: 200px;
        margin-inline-end: unset;

        .window-container {
          margin: 8px;

          >* {
            display: none;
          }

          .draggable {
            display: block;
          }
        }
      }
    }
  }

  .preview-all-mode {
    display: none;
    padding: 20px 0;
    text-align: center;
  }

  &.focus {
    --uno: fixed z-2000 top-0 bottom-0 left-0 right-0 of-y-auto bg-stone-2/75 dark-bg-stone-8/75 backdrop-blur-sm;

    .dashboard-container {
      align-items: stretch;
      justify-content: space-between;
      height: calc(100% - 80px);

      >.window {
        display: none;
        flex: unset;
        width: 100%;
        padding: 10px;
        margin-inline-end: unset;
        overflow: auto;

        .window-container {
          width: 100%;
          height: 100%;
          margin: 0;

          .header {
            display: none;
          }
        }

        &.window-focus {
          display: block;
        }
      }
    }
  }

  .dashboard-container {
    display: flex;
    height: 100%;
  }

  .window {
    display: flex;
    flex: none;
    width: var(--g-window-width);
    margin-inline-end: -16px;

    &.window-ghost {
      opacity: 0;
    }

    .window-container {
      --uno: shadow hover-shadow-lg transition;

      display: flex;
      flex: 1;
      flex-direction: column;
      width: calc(100% - 32px);
      margin: 16px;
      background-color: var(--g-app-bg);

      &.maximize {
        position: fixed;
        top: 0;
        left: 0;
        z-index: 2000;
        width: 100%;
        height: 100%;
        margin: 0;

        .header {
          display: none;
        }

        .maximize-exit {
          display: block;
        }
      }

      &.preview {
        .mask {
          display: flex;
        }
      }

      .header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 40px;
        padding: 10px;
        border-bottom: 1px solid var(--g-border-color);

        .titles {
          display: flex;
          align-items: center;

          .title {
            --uno: c-dark dark-c-light;

            font-size: 14px;
            font-weight: bold;
          }

          .title + .btns {
            margin-inline-start: 10px;
          }
        }

        .btns {
          display: flex;
          gap: 5px;

          .btn {
            --uno: c-dark dark-c-light bg-stone-1 hover-bg-stone-2 dark-bg-stone-9 dark-hover-bg-stone-8 transition;

            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px;
            font-size: 16px;
            cursor: pointer;
            border-radius: 3px;
          }
        }
      }

      .maximize-exit {
        position: fixed;
        top: -40px;
        right: -40px;
        z-index: 1000;
        display: none;
        width: 80px;
        height: 80px;
        cursor: pointer;
        background-color: rgb(0 0 0 / 30%);
        border-radius: 50%;
        transition: 0.3s;

        .icon {
          position: absolute;
          bottom: 16px;
          left: 16px;
          transition: 0.3s;
        }

        &:hover {
          background-color: rgb(0 0 0 / 70%);

          .icon {
            color: #fff;
          }
        }
      }

      .mask {
        --uno: bg-stone-1/75 dark-bg-stone-9/75;

        position: absolute;
        top: 0;
        left: 0;
        z-index: 1000;
        display: none;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: calc(100% - 32px);
        height: calc(100% - 32px);
        margin: 16px;
      }

      .draggable {
        display: none;
        padding: 10px;
        cursor: move;

        .title {
          display: -webkit-box;
          height: 48px;
          overflow: hidden;
          font-size: 14px;
          line-height: 24px;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 3;
          line-clamp: 3;
        }
      }
    }

    &:last-child {
      margin-inline-end: 0;
    }
  }
}

.preview-all-focus-actions {
  --uno: fixed z-2001 left-0 -bottom-15 w-full h-15 flex-center transition-bottom;

  &.show {
    --uno: bottom-0;
  }

  span {
    --uno: text-lg text-shadow;
  }
}

.window-enter-from {
  opacity: 0;
  transform: translateY(100px);
}

.window-enter-active {
  transition: all 0.5s;
}
</style>
