<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <CalculationMode>Interpretation</CalculationMode>
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="1">
      <ManagerMonthStatistics Ref="2" type="Stimulsoft.Report.Dictionary.StiJsonDatabase" isKey="true">
        <Alias>ManagerMonthStatistics</Alias>
        <HeadersString />
        <Key />
        <Name>ManagerMonthStatistics</Name>
        <PathData>D:\11\2\pms-report\public\reports\json\ManagerMonthStatistics.json</PathData>
      </ManagerMonthStatistics>
    </Databases>
    <DataSources isList="true" count="18">
      <root Ref="3" type="DataTableSource" isKey="true">
        <Alias>root</Alias>
        <Columns isList="true" count="4">
          <value>code,System.Decimal</value>
          <value>data,System.String</value>
          <value>msg,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>605affbf17a04c4e932d4ba7213b0a4f</Key>
        <Name>root</Name>
        <NameInSource>ManagerMonthStatistics.root</NameInSource>
      </root>
      <root_data Ref="4" type="DataTableSource" isKey="true">
        <Alias>root_data</Alias>
        <Columns isList="true" count="18">
          <value>hname,System.String</value>
          <value>hcode,System.String</value>
          <value>startDate,System.String</value>
          <value>endDate,System.String</value>
          <value>lastSelectTime,System.String</value>
          <value>operator,System.String</value>
          <value>roomRevenueIndexList,System.String</value>
          <value>orderSrcList,System.String</value>
          <value>orderSrcSumList,System.String</value>
          <value>rtCodeList,System.String</value>
          <value>rtCodeSumList,System.String</value>
          <value>gsrcList,System.String</value>
          <value>gsrcSumList,System.String</value>
          <value>statChannelList,System.String</value>
          <value>statChannelSumList,System.String</value>
          <value>inTypeList,System.String</value>
          <value>inTypeSumList,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>71249b1a74294db5a3b0069377773a77</Key>
        <Name>root_data</Name>
        <NameInSource>ManagerMonthStatistics.root_data</NameInSource>
      </root_data>
      <root_data_gsrcList Ref="5" type="DataTableSource" isKey="true">
        <Alias>root_data_gsrcList</Alias>
        <Columns isList="true" count="8">
          <value>gcode,System.String</value>
          <value>hcode,System.String</value>
          <value>nightNum,System.Decimal</value>
          <value>roomFee,System.Decimal</value>
          <value>avgRoomFee,System.Decimal</value>
          <value>month,System.String</value>
          <value>dataClassStatMonthList,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>fa207452a18440db80e9895d36c2bfe7</Key>
        <Name>root_data_gsrcList</Name>
        <NameInSource>ManagerMonthStatistics.root_data_gsrcList</NameInSource>
      </root_data_gsrcList>
      <root_data_gsrcList_dataClassStatMonthList Ref="6" type="DataTableSource" isKey="true">
        <Alias>root_data_gsrcList_dataClassStatMonthList</Alias>
        <Columns isList="true" count="9">
          <value>statType,System.String</value>
          <value>statTypeName,System.String</value>
          <value>statCode,System.String</value>
          <value>statName,System.String</value>
          <value>nightNum,System.Decimal</value>
          <value>occ,System.Decimal</value>
          <value>roomFee,System.Decimal</value>
          <value>avgRoomFee,System.Decimal</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>65638967a4674c248cfe224f48727274</Key>
        <Name>root_data_gsrcList_dataClassStatMonthList</Name>
        <NameInSource>ManagerMonthStatistics.root_data_gsrcList_dataClassStatMonthList</NameInSource>
      </root_data_gsrcList_dataClassStatMonthList>
      <root_data_gsrcSumList Ref="7" type="DataTableSource" isKey="true">
        <Alias>root_data_gsrcSumList</Alias>
        <Columns isList="true" count="9">
          <value>statType,System.String</value>
          <value>statTypeName,System.String</value>
          <value>statCode,System.String</value>
          <value>statName,System.String</value>
          <value>nightNum,System.Decimal</value>
          <value>occ,System.String</value>
          <value>roomFee,System.Decimal</value>
          <value>avgRoomFee,System.Decimal</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>725292c676e543cf8c6fe90e0b5b184a</Key>
        <Name>root_data_gsrcSumList</Name>
        <NameInSource>ManagerMonthStatistics.root_data_gsrcSumList</NameInSource>
      </root_data_gsrcSumList>
      <root_data_inTypeList Ref="8" type="DataTableSource" isKey="true">
        <Alias>root_data_inTypeList</Alias>
        <Columns isList="true" count="8">
          <value>gcode,System.String</value>
          <value>hcode,System.String</value>
          <value>nightNum,System.Decimal</value>
          <value>roomFee,System.Decimal</value>
          <value>avgRoomFee,System.Decimal</value>
          <value>month,System.String</value>
          <value>dataClassStatMonthList,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>25078acf54f241639196ffbf71e31ba0</Key>
        <Name>root_data_inTypeList</Name>
        <NameInSource>ManagerMonthStatistics.root_data_inTypeList</NameInSource>
      </root_data_inTypeList>
      <root_data_inTypeList_dataClassStatMonthList Ref="9" type="DataTableSource" isKey="true">
        <Alias>root_data_inTypeList_dataClassStatMonthList</Alias>
        <Columns isList="true" count="9">
          <value>statType,System.String</value>
          <value>statTypeName,System.String</value>
          <value>statCode,System.String</value>
          <value>statName,System.String</value>
          <value>nightNum,System.Decimal</value>
          <value>occ,System.Decimal</value>
          <value>roomFee,System.Decimal</value>
          <value>avgRoomFee,System.Decimal</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>03a5baa15ccd41859b2213a827d2b99f</Key>
        <Name>root_data_inTypeList_dataClassStatMonthList</Name>
        <NameInSource>ManagerMonthStatistics.root_data_inTypeList_dataClassStatMonthList</NameInSource>
      </root_data_inTypeList_dataClassStatMonthList>
      <root_data_inTypeSumList Ref="10" type="DataTableSource" isKey="true">
        <Alias>root_data_inTypeSumList</Alias>
        <Columns isList="true" count="9">
          <value>statType,System.String</value>
          <value>statTypeName,System.String</value>
          <value>statCode,System.String</value>
          <value>statName,System.String</value>
          <value>nightNum,System.Decimal</value>
          <value>occ,System.String</value>
          <value>roomFee,System.Decimal</value>
          <value>avgRoomFee,System.Decimal</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>5c855d79ea7e4026ac57211f9d0fc417</Key>
        <Name>root_data_inTypeSumList</Name>
        <NameInSource>ManagerMonthStatistics.root_data_inTypeSumList</NameInSource>
      </root_data_inTypeSumList>
      <root_data_orderSrcList Ref="11" type="DataTableSource" isKey="true">
        <Alias>root_data_orderSrcList</Alias>
        <Columns isList="true" count="8">
          <value>gcode,System.String</value>
          <value>hcode,System.String</value>
          <value>nightNum,System.Decimal</value>
          <value>roomFee,System.Decimal</value>
          <value>avgRoomFee,System.Decimal</value>
          <value>month,System.String</value>
          <value>dataClassStatMonthList,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>aaeeb63662da445dbe35c4d17c661559</Key>
        <Name>root_data_orderSrcList</Name>
        <NameInSource>ManagerMonthStatistics.root_data_orderSrcList</NameInSource>
      </root_data_orderSrcList>
      <root_data_orderSrcList_dataClassStatMonthList Ref="12" type="DataTableSource" isKey="true">
        <Alias>root_data_orderSrcList_dataClassStatMonthList</Alias>
        <Columns isList="true" count="9">
          <value>statType,System.String</value>
          <value>statTypeName,System.String</value>
          <value>statCode,System.String</value>
          <value>statName,System.String</value>
          <value>nightNum,System.Decimal</value>
          <value>occ,System.Decimal</value>
          <value>roomFee,System.Decimal</value>
          <value>avgRoomFee,System.Decimal</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>c28950e98f1b4b52a3cda3d9dd3b8b58</Key>
        <Name>root_data_orderSrcList_dataClassStatMonthList</Name>
        <NameInSource>ManagerMonthStatistics.root_data_orderSrcList_dataClassStatMonthList</NameInSource>
      </root_data_orderSrcList_dataClassStatMonthList>
      <root_data_orderSrcSumList Ref="13" type="DataTableSource" isKey="true">
        <Alias>root_data_orderSrcSumList</Alias>
        <Columns isList="true" count="9">
          <value>statType,System.String</value>
          <value>statTypeName,System.String</value>
          <value>statCode,System.String</value>
          <value>statName,System.String</value>
          <value>nightNum,System.Decimal</value>
          <value>occ,System.String</value>
          <value>roomFee,System.Decimal</value>
          <value>avgRoomFee,System.Decimal</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>dae5e9c512cb4a4e8db118a800f791cc</Key>
        <Name>root_data_orderSrcSumList</Name>
        <NameInSource>ManagerMonthStatistics.root_data_orderSrcSumList</NameInSource>
      </root_data_orderSrcSumList>
      <root_data_roomRevenueIndexList Ref="14" type="DataTableSource" isKey="true">
        <Alias>root_data_roomRevenueIndexList</Alias>
        <Columns isList="true" count="35">
          <value>id,System.Decimal</value>
          <value>gcode,System.String</value>
          <value>hcode,System.String</value>
          <value>nightNum,System.Decimal</value>
          <value>roomFee,System.Decimal</value>
          <value>couponDeduction,System.Decimal</value>
          <value>revPar,System.Decimal</value>
          <value>avgRoomFee,System.Decimal</value>
          <value>occ,System.Decimal</value>
          <value>totalRoomNum,System.Decimal</value>
          <value>emptyNum,System.Decimal</value>
          <value>repairNum,System.Decimal</value>
          <value>selfNum,System.Decimal</value>
          <value>overnightNum,System.Decimal</value>
          <value>overnightOcc,System.Decimal</value>
          <value>openRoomNum,System.Decimal</value>
          <value>workInNum,System.Decimal</value>
          <value>bookInNum,System.Decimal</value>
          <value>cancelBookNum,System.Decimal</value>
          <value>noShowNum,System.Decimal</value>
          <value>memberCardSellNum,System.Decimal</value>
          <value>creditNum,System.Decimal</value>
          <value>freeUpNum,System.Decimal</value>
          <value>memberCardFee,System.Decimal</value>
          <value>month,System.String</value>
          <value>week,System.String</value>
          <value>memberCard,System.String</value>
          <value>good,System.String</value>
          <value>catering,System.String</value>
          <value>indemnityFee,System.String</value>
          <value>otherFee,System.String</value>
          <value>lobbyFee,System.String</value>
          <value>memberRecharge,System.String</value>
          <value>createTime,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>6fdebbca08694f83a64eb10f4a0aef82</Key>
        <Name>root_data_roomRevenueIndexList</Name>
        <NameInSource>ManagerMonthStatistics.root_data_roomRevenueIndexList</NameInSource>
      </root_data_roomRevenueIndexList>
      <root_data_rtCodeList Ref="15" type="DataTableSource" isKey="true">
        <Alias>root_data_rtCodeList</Alias>
        <Columns isList="true" count="8">
          <value>gcode,System.String</value>
          <value>hcode,System.String</value>
          <value>nightNum,System.Decimal</value>
          <value>roomFee,System.Decimal</value>
          <value>avgRoomFee,System.Decimal</value>
          <value>month,System.String</value>
          <value>dataClassStatMonthList,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>62749731273446d79afc56dffbd176bf</Key>
        <Name>root_data_rtCodeList</Name>
        <NameInSource>ManagerMonthStatistics.root_data_rtCodeList</NameInSource>
      </root_data_rtCodeList>
      <root_data_rtCodeList_dataClassStatMonthList Ref="16" type="DataTableSource" isKey="true">
        <Alias>root_data_rtCodeList_dataClassStatMonthList</Alias>
        <Columns isList="true" count="9">
          <value>statType,System.String</value>
          <value>statTypeName,System.String</value>
          <value>statCode,System.String</value>
          <value>statName,System.String</value>
          <value>nightNum,System.Decimal</value>
          <value>occ,System.Decimal</value>
          <value>roomFee,System.Decimal</value>
          <value>avgRoomFee,System.Decimal</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>97e9f6ebeaeb4adba3768221fecb901c</Key>
        <Name>root_data_rtCodeList_dataClassStatMonthList</Name>
        <NameInSource>ManagerMonthStatistics.root_data_rtCodeList_dataClassStatMonthList</NameInSource>
      </root_data_rtCodeList_dataClassStatMonthList>
      <root_data_rtCodeSumList Ref="17" type="DataTableSource" isKey="true">
        <Alias>root_data_rtCodeSumList</Alias>
        <Columns isList="true" count="9">
          <value>statType,System.String</value>
          <value>statTypeName,System.String</value>
          <value>statCode,System.String</value>
          <value>statName,System.String</value>
          <value>nightNum,System.Decimal</value>
          <value>occ,System.String</value>
          <value>roomFee,System.Decimal</value>
          <value>avgRoomFee,System.Decimal</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>5aa33409b792448eb07b3120f7be715e</Key>
        <Name>root_data_rtCodeSumList</Name>
        <NameInSource>ManagerMonthStatistics.root_data_rtCodeSumList</NameInSource>
      </root_data_rtCodeSumList>
      <root_data_statChannelList Ref="18" type="DataTableSource" isKey="true">
        <Alias>root_data_statChannelList</Alias>
        <Columns isList="true" count="8">
          <value>gcode,System.String</value>
          <value>hcode,System.String</value>
          <value>nightNum,System.Decimal</value>
          <value>roomFee,System.Decimal</value>
          <value>avgRoomFee,System.Decimal</value>
          <value>month,System.String</value>
          <value>dataClassStatMonthList,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>3083bd420e9642478fcdf16b6c704076</Key>
        <Name>root_data_statChannelList</Name>
        <NameInSource>ManagerMonthStatistics.root_data_statChannelList</NameInSource>
      </root_data_statChannelList>
      <root_data_statChannelList_dataClassStatMonthList Ref="19" type="DataTableSource" isKey="true">
        <Alias>root_data_statChannelList_dataClassStatMonthList</Alias>
        <Columns isList="true" count="9">
          <value>statType,System.String</value>
          <value>statTypeName,System.String</value>
          <value>statCode,System.String</value>
          <value>statName,System.String</value>
          <value>nightNum,System.Decimal</value>
          <value>occ,System.Decimal</value>
          <value>roomFee,System.Decimal</value>
          <value>avgRoomFee,System.Decimal</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>0e93a686cc94481095fcdb1c6399321b</Key>
        <Name>root_data_statChannelList_dataClassStatMonthList</Name>
        <NameInSource>ManagerMonthStatistics.root_data_statChannelList_dataClassStatMonthList</NameInSource>
      </root_data_statChannelList_dataClassStatMonthList>
      <root_data_statChannelSumList Ref="20" type="DataTableSource" isKey="true">
        <Alias>root_data_statChannelSumList</Alias>
        <Columns isList="true" count="9">
          <value>statType,System.String</value>
          <value>statTypeName,System.String</value>
          <value>statCode,System.String</value>
          <value>statName,System.String</value>
          <value>nightNum,System.Decimal</value>
          <value>occ,System.String</value>
          <value>roomFee,System.Decimal</value>
          <value>avgRoomFee,System.Decimal</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>2be4a1e867d547f1a600d2b4def526fc</Key>
        <Name>root_data_statChannelSumList</Name>
        <NameInSource>ManagerMonthStatistics.root_data_statChannelSumList</NameInSource>
      </root_data_statChannelSumList>
    </DataSources>
    <Relations isList="true" count="17">
      <root Ref="21" type="DataRelation" isKey="true">
        <Alias>root</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="4" />
        <Dictionary isRef="1" />
        <Name>root</Name>
        <NameInSource>root_data</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>data</value>
        </ParentColumns>
        <ParentSource isRef="3" />
      </root>
      <root_data Ref="22" type="DataRelation" isKey="true">
        <Alias>root_data</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="5" />
        <Dictionary isRef="1" />
        <Name>root_data</Name>
        <NameInSource>root_data_gsrcList</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>gsrcList</value>
        </ParentColumns>
        <ParentSource isRef="4" />
      </root_data>
      <root_data_gsrcList Ref="23" type="DataRelation" isKey="true">
        <Alias>root_data_gsrcList</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="6" />
        <Dictionary isRef="1" />
        <Name>root_data_gsrcList</Name>
        <NameInSource>root_data_gsrcList_dataClassStatMonthList</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>dataClassStatMonthList</value>
        </ParentColumns>
        <ParentSource isRef="5" />
      </root_data_gsrcList>
      <root_data Ref="24" type="DataRelation" isKey="true">
        <Alias>root_data</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="7" />
        <Dictionary isRef="1" />
        <Name>root_data</Name>
        <NameInSource>root_data_gsrcSumList</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>gsrcSumList</value>
        </ParentColumns>
        <ParentSource isRef="4" />
      </root_data>
      <root_data Ref="25" type="DataRelation" isKey="true">
        <Alias>root_data</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="8" />
        <Dictionary isRef="1" />
        <Name>root_data</Name>
        <NameInSource>root_data_inTypeList</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>inTypeList</value>
        </ParentColumns>
        <ParentSource isRef="4" />
      </root_data>
      <root_data_inTypeList Ref="26" type="DataRelation" isKey="true">
        <Alias>root_data_inTypeList</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="9" />
        <Dictionary isRef="1" />
        <Name>root_data_inTypeList</Name>
        <NameInSource>root_data_inTypeList_dataClassStatMonthList</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>dataClassStatMonthList</value>
        </ParentColumns>
        <ParentSource isRef="8" />
      </root_data_inTypeList>
      <root_data Ref="27" type="DataRelation" isKey="true">
        <Alias>root_data</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="10" />
        <Dictionary isRef="1" />
        <Name>root_data</Name>
        <NameInSource>root_data_inTypeSumList</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>inTypeSumList</value>
        </ParentColumns>
        <ParentSource isRef="4" />
      </root_data>
      <root_data Ref="28" type="DataRelation" isKey="true">
        <Alias>root_data</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="11" />
        <Dictionary isRef="1" />
        <Name>root_data</Name>
        <NameInSource>root_data_orderSrcList</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>orderSrcList</value>
        </ParentColumns>
        <ParentSource isRef="4" />
      </root_data>
      <root_data_orderSrcList Ref="29" type="DataRelation" isKey="true">
        <Alias>root_data_orderSrcList</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="12" />
        <Dictionary isRef="1" />
        <Name>root_data_orderSrcList</Name>
        <NameInSource>root_data_orderSrcList_dataClassStatMonthList</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>dataClassStatMonthList</value>
        </ParentColumns>
        <ParentSource isRef="11" />
      </root_data_orderSrcList>
      <root_data Ref="30" type="DataRelation" isKey="true">
        <Alias>root_data</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="13" />
        <Dictionary isRef="1" />
        <Name>root_data</Name>
        <NameInSource>root_data_orderSrcSumList</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>orderSrcSumList</value>
        </ParentColumns>
        <ParentSource isRef="4" />
      </root_data>
      <root_data Ref="31" type="DataRelation" isKey="true">
        <Alias>root_data</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="14" />
        <Dictionary isRef="1" />
        <Name>root_data</Name>
        <NameInSource>root_data_roomRevenueIndexList</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>roomRevenueIndexList</value>
        </ParentColumns>
        <ParentSource isRef="4" />
      </root_data>
      <root_data Ref="32" type="DataRelation" isKey="true">
        <Alias>root_data</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="15" />
        <Dictionary isRef="1" />
        <Name>root_data</Name>
        <NameInSource>root_data_rtCodeList</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>rtCodeList</value>
        </ParentColumns>
        <ParentSource isRef="4" />
      </root_data>
      <root_data_rtCodeList Ref="33" type="DataRelation" isKey="true">
        <Alias>root_data_rtCodeList</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="16" />
        <Dictionary isRef="1" />
        <Name>root_data_rtCodeList</Name>
        <NameInSource>root_data_rtCodeList_dataClassStatMonthList</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>dataClassStatMonthList</value>
        </ParentColumns>
        <ParentSource isRef="15" />
      </root_data_rtCodeList>
      <root_data Ref="34" type="DataRelation" isKey="true">
        <Alias>root_data</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="17" />
        <Dictionary isRef="1" />
        <Name>root_data</Name>
        <NameInSource>root_data_rtCodeSumList</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>rtCodeSumList</value>
        </ParentColumns>
        <ParentSource isRef="4" />
      </root_data>
      <root_data Ref="35" type="DataRelation" isKey="true">
        <Alias>root_data</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="18" />
        <Dictionary isRef="1" />
        <Name>root_data</Name>
        <NameInSource>root_data_statChannelList</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>statChannelList</value>
        </ParentColumns>
        <ParentSource isRef="4" />
      </root_data>
      <root_data_statChannelList Ref="36" type="DataRelation" isKey="true">
        <Alias>root_data_statChannelList</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="19" />
        <Dictionary isRef="1" />
        <Name>root_data_statChannelList</Name>
        <NameInSource>root_data_statChannelList_dataClassStatMonthList</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>dataClassStatMonthList</value>
        </ParentColumns>
        <ParentSource isRef="18" />
      </root_data_statChannelList>
      <root_data Ref="37" type="DataRelation" isKey="true">
        <Alias>root_data</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="20" />
        <Dictionary isRef="1" />
        <Name>root_data</Name>
        <NameInSource>root_data_statChannelSumList</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>statChannelSumList</value>
        </ParentColumns>
        <ParentSource isRef="4" />
      </root_data>
    </Relations>
    <Report isRef="0" />
    <Resources isList="true" count="0" />
    <UserFunctions isList="true" count="0" />
    <Variables isList="true" count="1">
      <value>,TotalRoomNumSum,TotalRoomNumSum,,System.Int64,_x0030_,False,False,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <Key>107b414525344aa58c690a208caebc00</Key>
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="38" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="25">
        <PageHeaderBand1 Ref="39" type="PageHeaderBand" isKey="true">
          <Border>Bottom;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,51,0.8</ClientRectangle>
          <Components isList="true" count="2">
            <Text2 Ref="40" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0.01,0,7,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Name>Text2</Name>
              <Page isRef="38" />
              <Parent isRef="39" />
              <Text>{root_data.hname}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text2>
            <Text6 Ref="41" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>45.4,0,5.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Name>Text6</Name>
              <Page isRef="38" />
              <Parent isRef="39" />
              <Text>第{PageNumber}页,共{TotalPageCount}页</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text6>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>PageHeaderBand1</Name>
          <Page isRef="38" />
          <Parent isRef="38" />
        </PageHeaderBand1>
        <报表标题区1 Ref="42" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,2,51,1.4</ClientRectangle>
          <Components isList="true" count="2">
            <Text1 Ref="43" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>21,0,6.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,12,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text1</Name>
              <Page isRef="38" />
              <Parent isRef="42" />
              <Text>酒店综合统计月报表（固化）</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text1>
            <Text5 Ref="44" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.8,23,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Name>Text5</Name>
              <Page isRef="38" />
              <Parent isRef="42" />
              <Text>门店:{root_data.hname}   月份:{root_data.startDate} - {root_data.endDate}  最后查询时间：{root_data.lastSelectTime}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text5>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>报表标题区1</Name>
          <Page isRef="38" />
          <Parent isRef="38" />
        </报表标题区1>
        <页眉1 Ref="45" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,4.2,51,0.8</ClientRectangle>
          <Components isList="true" count="19">
            <Text3 Ref="46" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text3</Name>
              <Page isRef="38" />
              <Parent isRef="45" />
              <Text>月份</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text3>
            <文本1 Ref="47" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.4,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本1</Name>
              <Page isRef="38" />
              <Parent isRef="45" />
              <Text>客房数</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本1>
            <文本2 Ref="48" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.6,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本2</Name>
              <Page isRef="38" />
              <Parent isRef="45" />
              <Text>间夜数</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本2>
            <文本4 Ref="49" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6.8,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本4</Name>
              <Page isRef="38" />
              <Parent isRef="45" />
              <Text>自用房</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本4>
            <文本5 Ref="50" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本5</Name>
              <Page isRef="38" />
              <Parent isRef="45" />
              <Text>维修房</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本5>
            <文本6 Ref="51" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>11.2,0,2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本6</Name>
              <Page isRef="38" />
              <Parent isRef="45" />
              <Text>过夜房</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本6>
            <文本7 Ref="52" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.2,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Interaction Ref="53" type="Stimulsoft.Report.Components.StiInteraction" isKey="true">
                <SortingColumn>数据区1.roomFee</SortingColumn>
              </Interaction>
              <Name>文本7</Name>
              <Page isRef="38" />
              <Parent isRef="45" />
              <Text>房费</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本7>
            <文本8 Ref="54" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>17.8,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Interaction Ref="55" type="Stimulsoft.Report.Components.StiInteraction" isKey="true">
                <SortingColumn>数据区1.avgRoomFee</SortingColumn>
              </Interaction>
              <Name>文本8</Name>
              <Page isRef="38" />
              <Parent isRef="45" />
              <Text>平均房价</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本8>
            <文本9 Ref="56" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>20,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本9</Name>
              <Page isRef="38" />
              <Parent isRef="45" />
              <Text>RevPar</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本9>
            <文本10 Ref="57" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>22.2,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本10</Name>
              <Page isRef="38" />
              <Parent isRef="45" />
              <Text>出租率</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本10>
            <文本11 Ref="58" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>24.4,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本11</Name>
              <Page isRef="38" />
              <Parent isRef="45" />
              <Text>过夜出租率</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本11>
            <文本12 Ref="59" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>26.8,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Interaction Ref="60" type="Stimulsoft.Report.Components.StiInteraction" isKey="true">
                <SortingColumn>数据区1.good</SortingColumn>
              </Interaction>
              <Name>文本12</Name>
              <Page isRef="38" />
              <Parent isRef="45" />
              <Text>小商品</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本12>
            <文本13 Ref="61" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>29.2,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Interaction Ref="62" type="Stimulsoft.Report.Components.StiInteraction" isKey="true">
                <SortingColumn>数据区1.memberCard</SortingColumn>
              </Interaction>
              <Name>文本13</Name>
              <Page isRef="38" />
              <Parent isRef="45" />
              <Text>会员卡</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本13>
            <文本14 Ref="63" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>31.6,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Interaction Ref="64" type="Stimulsoft.Report.Components.StiInteraction" isKey="true">
                <SortingColumn>数据区1.catering</SortingColumn>
              </Interaction>
              <Name>文本14</Name>
              <Page isRef="38" />
              <Parent isRef="45" />
              <Text>餐饮</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本14>
            <文本15 Ref="65" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>33.8,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Interaction Ref="66" type="Stimulsoft.Report.Components.StiInteraction" isKey="true">
                <SortingColumn>数据区1.otherFee</SortingColumn>
              </Interaction>
              <Name>文本15</Name>
              <Page isRef="38" />
              <Parent isRef="45" />
              <Text>其他消费</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本15>
            <文本16 Ref="67" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>36,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本16</Name>
              <Page isRef="38" />
              <Parent isRef="45" />
              <Text>赔偿费</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本16>
            <文本17 Ref="68" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>38.2,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Interaction Ref="69" type="Stimulsoft.Report.Components.StiInteraction" isKey="true">
                <SortingColumn>数据区1.lobbyFee</SortingColumn>
              </Interaction>
              <Name>文本17</Name>
              <Page isRef="38" />
              <Parent isRef="45" />
              <Text>门店收入</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本17>
            <文本18 Ref="70" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>40.4,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Interaction Ref="71" type="Stimulsoft.Report.Components.StiInteraction" isKey="true">
                <SortingColumn>数据区1.memberRecharge</SortingColumn>
              </Interaction>
              <Name>文本18</Name>
              <Page isRef="38" />
              <Parent isRef="45" />
              <Text>会员充值</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本18>
            <文本140 Ref="72" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.6,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Interaction Ref="73" type="Stimulsoft.Report.Components.StiInteraction" isKey="true">
                <SortingColumn>数据区1.couponDeduction</SortingColumn>
              </Interaction>
              <Name>文本140</Name>
              <Page isRef="38" />
              <Parent isRef="45" />
              <Text>优惠卷抵扣</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本140>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>页眉1</Name>
          <Page isRef="38" />
          <Parent isRef="38" />
        </页眉1>
        <数据区1 Ref="74" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,5.8,51,0.8</ClientRectangle>
          <Components isList="true" count="19">
            <文本20 Ref="75" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="1">
                <value>TextBrush,SolidBrushValue_x0028__x0022__x0023_00B0F0_x0022__x0029_</value>
              </Expressions>
              <Font>微软雅黑,10</Font>
              <HorAlignment>Center</HorAlignment>
              <Hyperlink>#/?name=StoreManagerMonthReport&amp;hcode={root_data.hcode}&amp;startDate={root_data_roomRevenueIndexList.month}&amp;endDate={root_data_roomRevenueIndexList.month}</Hyperlink>
              <Name>文本20</Name>
              <Page isRef="38" />
              <Parent isRef="74" />
              <Text>{root_data_roomRevenueIndexList.month}</Text>
              <TextBrush>[0:176:240]</TextBrush>
              <TextFormat Ref="76" type="CustomFormat" isKey="true">
                <StringFormat>yyyy/MM</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本20>
            <文本22 Ref="77" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.4,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Margins>9,0,0,0</Margins>
              <Name>文本22</Name>
              <Page isRef="38" />
              <Parent isRef="74" />
              <Text>{root_data_roomRevenueIndexList.totalRoomNum}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本22>
            <文本23 Ref="78" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.6,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Hyperlink>#</Hyperlink>
              <Margins>9,0,0,0</Margins>
              <Name>文本23</Name>
              <Page isRef="38" />
              <Parent isRef="74" />
              <Text>{root_data_roomRevenueIndexList.nightNum}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本23>
            <文本25 Ref="79" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6.8,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Margins>9,0,0,0</Margins>
              <Name>文本25</Name>
              <Page isRef="38" />
              <Parent isRef="74" />
              <Text>{root_data_roomRevenueIndexList.selfNum}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本25>
            <文本26 Ref="80" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Margins>9,0,0,0</Margins>
              <Name>文本26</Name>
              <Page isRef="38" />
              <Parent isRef="74" />
              <Text>{root_data_roomRevenueIndexList.repairNum}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本26>
            <文本28 Ref="81" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.2,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="1">
                <value>TextBrush,SolidBrushValue_x0028__x0022__x0023_000000_x0022__x0029_</value>
              </Expressions>
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Hyperlink>#</Hyperlink>
              <Margins>0,5,0,0</Margins>
              <Name>文本28</Name>
              <Page isRef="38" />
              <Parent isRef="74" />
              <Text>{root_data_roomRevenueIndexList.roomFee}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextFormat Ref="82" type="CurrencyFormat" isKey="true">
                <DecimalDigits>2</DecimalDigits>
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>2</NegativePattern>
                <PositivePattern>0</PositivePattern>
                <Symbol>¥</Symbol>
              </TextFormat>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本28>
            <文本29 Ref="83" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>17.8,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本29</Name>
              <Page isRef="38" />
              <Parent isRef="74" />
              <Text>{root_data_roomRevenueIndexList.avgRoomFee}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本29>
            <文本30 Ref="84" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>20,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Margins>9,0,0,0</Margins>
              <Name>文本30</Name>
              <Page isRef="38" />
              <Parent isRef="74" />
              <Text>{root_data_roomRevenueIndexList.revPar}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本30>
            <文本31 Ref="85" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>22.2,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Margins>9,0,0,0</Margins>
              <Name>文本31</Name>
              <Page isRef="38" />
              <Parent isRef="74" />
              <Text>{root_data_roomRevenueIndexList.occ}%</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本31>
            <文本32 Ref="86" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>24.4,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Margins>5,0,0,0</Margins>
              <Name>文本32</Name>
              <Page isRef="38" />
              <Parent isRef="74" />
              <Text>{root_data_roomRevenueIndexList.overnightOcc}%</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本32>
            <文本33 Ref="87" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>26.8,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本33</Name>
              <Page isRef="38" />
              <Parent isRef="74" />
              <Text>{root_data_roomRevenueIndexList.good}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本33>
            <文本34 Ref="88" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>29.2,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本34</Name>
              <Page isRef="38" />
              <Parent isRef="74" />
              <Text>{root_data_roomRevenueIndexList.memberCardFee}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本34>
            <文本35 Ref="89" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>31.6,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本35</Name>
              <Page isRef="38" />
              <Parent isRef="74" />
              <Text>{root_data_roomRevenueIndexList.catering}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本35>
            <文本36 Ref="90" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>33.8,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本36</Name>
              <Page isRef="38" />
              <Parent isRef="74" />
              <Text>{root_data_roomRevenueIndexList.otherFee}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本36>
            <文本37 Ref="91" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>36,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Interaction Ref="92" type="Stimulsoft.Report.Components.StiInteraction" isKey="true">
                <SortingColumn>数据区1.indemnityFee</SortingColumn>
              </Interaction>
              <Margins>0,5,0,0</Margins>
              <Name>文本37</Name>
              <Page isRef="38" />
              <Parent isRef="74" />
              <Text>{root_data_roomRevenueIndexList.indemnityFee}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本37>
            <文本38 Ref="93" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>38.2,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本38</Name>
              <Page isRef="38" />
              <Parent isRef="74" />
              <Text>{root_data_roomRevenueIndexList.lobbyFee}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本38>
            <文本39 Ref="94" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>40.4,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本39</Name>
              <Page isRef="38" />
              <Parent isRef="74" />
              <Text>{root_data_roomRevenueIndexList.memberRecharge}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本39>
            <文本27 Ref="95" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>11.2,0,2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Margins>9,0,0,0</Margins>
              <Name>文本27</Name>
              <Page isRef="38" />
              <Parent isRef="74" />
              <Text>{root_data_roomRevenueIndexList.overnightNum}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本27>
            <文本141 Ref="96" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.6,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本141</Name>
              <Page isRef="38" />
              <Parent isRef="74" />
              <Text>{root_data_roomRevenueIndexList.couponDeduction}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本141>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>root_data_roomRevenueIndexList</DataSourceName>
          <Expressions isList="true" count="0" />
          <Filters isList="true" count="0" />
          <Name>数据区1</Name>
          <Page isRef="38" />
          <Parent isRef="38" />
          <Sort isList="true" count="0" />
        </数据区1>
        <页脚1 Ref="97" type="FooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,7.4,51,0.8</ClientRectangle>
          <Components isList="true" count="19">
            <文本100 Ref="98" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本100</Name>
              <Page isRef="38" />
              <Parent isRef="97" />
              <Text>合计</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="99" type="DateFormat" isKey="true" />
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本100>
            <文本101 Ref="100" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.4,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <Margins>9,0,0,0</Margins>
              <Name>文本101</Name>
              <Page isRef="38" />
              <Parent isRef="97" />
              <Text>{TotalRoomNumSum=Sum(root_data_roomRevenueIndexList.totalRoomNum)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本101>
            <文本102 Ref="101" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.6,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <Hyperlink>#</Hyperlink>
              <Margins>9,0,0,0</Margins>
              <Name>文本102</Name>
              <Page isRef="38" />
              <Parent isRef="97" />
              <Text>{Sum(root_data_roomRevenueIndexList.nightNum)}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本102>
            <文本103 Ref="102" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6.8,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <Margins>9,0,0,0</Margins>
              <Name>文本103</Name>
              <Page isRef="38" />
              <Parent isRef="97" />
              <Text>{Sum(root_data_roomRevenueIndexList.selfNum)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本103>
            <文本104 Ref="103" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <Margins>9,0,0,0</Margins>
              <Name>文本104</Name>
              <Page isRef="38" />
              <Parent isRef="97" />
              <Text>{Sum(root_data_roomRevenueIndexList.repairNum)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本104>
            <文本105 Ref="104" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>11.2,0,2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <Margins>9,0,0,0</Margins>
              <Name>文本105</Name>
              <Page isRef="38" />
              <Parent isRef="97" />
              <Text>{Sum(root_data_roomRevenueIndexList.overnightNum)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本105>
            <文本106 Ref="105" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.2,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Right</HorAlignment>
              <Hyperlink>#</Hyperlink>
              <Margins>0,5,0,0</Margins>
              <Name>文本106</Name>
              <Page isRef="38" />
              <Parent isRef="97" />
              <Text>{Sum(root_data_roomRevenueIndexList.roomFee)}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextFormat Ref="106" type="CurrencyFormat" isKey="true">
                <DecimalDigits>2</DecimalDigits>
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>2</NegativePattern>
                <PositivePattern>0</PositivePattern>
                <Symbol>¥</Symbol>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本106>
            <文本107 Ref="107" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>17.8,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本107</Name>
              <Page isRef="38" />
              <Parent isRef="97" />
              <Text>{Sum(root_data_roomRevenueIndexList.roomFee)/Sum(root_data_roomRevenueIndexList.nightNum)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="108" type="NumberFormat" isKey="true">
                <DecimalDigits>2</DecimalDigits>
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本107>
            <文本108 Ref="109" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>20,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <Margins>9,0,0,0</Margins>
              <Name>文本108</Name>
              <Page isRef="38" />
              <Parent isRef="97" />
              <Text>{Sum(root_data_roomRevenueIndexList.roomFee)/TotalRoomNumSum}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="110" type="NumberFormat" isKey="true">
                <DecimalDigits>2</DecimalDigits>
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本108>
            <文本109 Ref="111" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>22.2,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <Margins>9,0,0,0</Margins>
              <Name>文本109</Name>
              <Page isRef="38" />
              <Parent isRef="97" />
              <Text>{Sum(root_data_roomRevenueIndexList.nightNum)/TotalRoomNumSum}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="112" type="PercentageFormat" isKey="true">
                <DecimalDigits>2</DecimalDigits>
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <PositivePattern>1</PositivePattern>
                <Symbol>%</Symbol>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本109>
            <文本110 Ref="113" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>24.4,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>文本110</Name>
              <Page isRef="38" />
              <Parent isRef="97" />
              <Text>{Sum(root_data_roomRevenueIndexList.overnightNum/TotalRoomNumSum)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="114" type="PercentageFormat" isKey="true">
                <DecimalDigits>2</DecimalDigits>
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <PositivePattern>1</PositivePattern>
                <Symbol>%</Symbol>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本110>
            <文本111 Ref="115" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>26.8,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本111</Name>
              <Page isRef="38" />
              <Parent isRef="97" />
              <Text>{Sum(root_data_roomRevenueIndexList.good)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本111>
            <文本112 Ref="116" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>29.2,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本112</Name>
              <Page isRef="38" />
              <Parent isRef="97" />
              <Text>{Sum(root_data_roomRevenueIndexList.memberCardFee)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </文本112>
            <文本113 Ref="117" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>31.6,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本113</Name>
              <Page isRef="38" />
              <Parent isRef="97" />
              <Text>{Sum(root_data_roomRevenueIndexList.catering)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本113>
            <文本114 Ref="118" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>33.8,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本114</Name>
              <Page isRef="38" />
              <Parent isRef="97" />
              <Text>{Sum(root_data_roomRevenueIndexList.otherFee)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本114>
            <文本115 Ref="119" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>36,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本115</Name>
              <Page isRef="38" />
              <Parent isRef="97" />
              <Text>{Sum(root_data_roomRevenueIndexList.indemnityFee)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本115>
            <文本116 Ref="120" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>38.2,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本116</Name>
              <Page isRef="38" />
              <Parent isRef="97" />
              <Text>{Sum(root_data_roomRevenueIndexList.lobbyFee)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本116>
            <文本117 Ref="121" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>40.4,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本117</Name>
              <Page isRef="38" />
              <Parent isRef="97" />
              <Text>{Sum(root_data_roomRevenueIndexList.memberRecharge)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本117>
            <文本142 Ref="122" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.6,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本142</Name>
              <Page isRef="38" />
              <Parent isRef="97" />
              <Text>{Sum(root_data_roomRevenueIndexList.couponDeduction)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </文本142>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>页脚1</Name>
          <Page isRef="38" />
          <Parent isRef="38" />
        </页脚1>
        <页眉2 Ref="123" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,9,51,1.2</ClientRectangle>
          <Components isList="true" count="1">
            <文本46 Ref="124" type="Text" isKey="true">
              <Border>Left, Right, Bottom;Black;1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,3,1.2</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <Margins>5,0,0,5</Margins>
              <Name>文本46</Name>
              <Page isRef="38" />
              <Parent isRef="123" />
              <Text>客源</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </文本46>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>页眉2</Name>
          <Page isRef="38" />
          <Parent isRef="38" />
        </页眉2>
        <栏首1 Ref="125" type="Stimulsoft.Report.Components.StiColumnHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,11,51,1.6</ClientRectangle>
          <Components isList="true" count="2">
            <面板1 Ref="126" type="Panel" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.4,0,13,1.6</ClientRectangle>
              <Components isList="true" count="1">
                <交叉_数据区1 Ref="127" type="CrossDataBand" isKey="true">
                  <Brush>Transparent</Brush>
                  <BusinessObjectGuid isNull="true" />
                  <ClientRectangle>0,0,8.8,1.6</ClientRectangle>
                  <Components isList="true" count="5">
                    <文本3 Ref="128" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0,8.8,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本3</Name>
                      <Page isRef="38" />
                      <Parent isRef="127" />
                      <Text>{root_data_gsrcList_dataClassStatMonthList.statName}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本3>
                    <文本44 Ref="129" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0.8,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本44</Name>
                      <Page isRef="38" />
                      <Parent isRef="127" />
                      <Text>间夜数</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本44>
                    <文本45 Ref="130" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>2.2,0.8,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本45</Name>
                      <Page isRef="38" />
                      <Parent isRef="127" />
                      <Text>房费</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本45>
                    <文本49 Ref="131" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>4.4,0.8,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本49</Name>
                      <Page isRef="38" />
                      <Parent isRef="127" />
                      <Text>平均房价</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本49>
                    <文本24 Ref="132" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>6.6,0.8,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本24</Name>
                      <Page isRef="38" />
                      <Parent isRef="127" />
                      <Text>出租率</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本24>
                  </Components>
                  <Conditions isList="true" count="0" />
                  <DataRelationName>root_data_gsrcList_dataClassStatMonthList</DataRelationName>
                  <DataSourceName>root_data_gsrcList_dataClassStatMonthList</DataSourceName>
                  <Expressions isList="true" count="0" />
                  <Filters isList="true" count="0" />
                  <MasterComponent isRef="134" />
                  <Name>交叉_数据区1</Name>
                  <Page isRef="38" />
                  <Parent isRef="126" />
                  <Sort isList="true" count="0" />
                </交叉_数据区1>
              </Components>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Name>面板1</Name>
              <Page isRef="38" />
              <Parent isRef="125" />
            </面板1>
            <文本40 Ref="133" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2.4,1.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本40</Name>
              <Page isRef="38" />
              <Parent isRef="125" />
              <Text>月份</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本40>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>栏首1</Name>
          <Page isRef="38" />
          <Parent isRef="38" />
        </栏首1>
        <数据区2 Ref="134" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,13.4,51,0.8</ClientRectangle>
          <Components isList="true" count="2">
            <文本19 Ref="135" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Center</HorAlignment>
              <Hyperlink>#/?name=StoreManagerMonthReport&amp;hcode={root_data.hcode}&amp;startDate={root_data_gsrcList.month}&amp;endDate={root_data_gsrcList.month}</Hyperlink>
              <Name>文本19</Name>
              <Page isRef="38" />
              <Parent isRef="134" />
              <Text>{root_data_gsrcList.month}</Text>
              <TextBrush>[0:176:240]</TextBrush>
              <TextFormat Ref="136" type="CustomFormat" isKey="true">
                <StringFormat>yyyy/MM</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本19>
            <面板2 Ref="137" type="Panel" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.4,0,13,0.8</ClientRectangle>
              <Components isList="true" count="1">
                <交叉_数据区2 Ref="138" type="CrossDataBand" isKey="true">
                  <Brush>Transparent</Brush>
                  <BusinessObjectGuid isNull="true" />
                  <ClientRectangle>0,0,8.8,0.8</ClientRectangle>
                  <Components isList="true" count="4">
                    <文本21 Ref="139" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本21</Name>
                      <Page isRef="38" />
                      <Parent isRef="138" />
                      <Text>{root_data_gsrcList_dataClassStatMonthList.nightNum}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本21>
                    <文本41 Ref="140" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>2.2,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本41</Name>
                      <Page isRef="38" />
                      <Parent isRef="138" />
                      <Text>{root_data_gsrcList_dataClassStatMonthList.roomFee}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本41>
                    <文本42 Ref="141" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>4.4,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本42</Name>
                      <Page isRef="38" />
                      <Parent isRef="138" />
                      <Text>{root_data_gsrcList_dataClassStatMonthList.avgRoomFee}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本42>
                    <文本43 Ref="142" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>6.6,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本43</Name>
                      <Page isRef="38" />
                      <Parent isRef="138" />
                      <Text>{root_data_gsrcList_dataClassStatMonthList.occ}%</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本43>
                  </Components>
                  <Conditions isList="true" count="0" />
                  <DataRelationName>root_data_gsrcList_dataClassStatMonthList</DataRelationName>
                  <DataSourceName>root_data_gsrcList_dataClassStatMonthList</DataSourceName>
                  <Expressions isList="true" count="0" />
                  <Filters isList="true" count="0" />
                  <MasterComponent isRef="134" />
                  <Name>交叉_数据区2</Name>
                  <Page isRef="38" />
                  <Parent isRef="137" />
                  <Sort isList="true" count="0" />
                </交叉_数据区2>
              </Components>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Name>面板2</Name>
              <Page isRef="38" />
              <Parent isRef="134" />
            </面板2>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>root_data_gsrcList</DataSourceName>
          <Expressions isList="true" count="0" />
          <FilterOn>False</FilterOn>
          <Filters isList="true" count="0" />
          <Name>数据区2</Name>
          <Page isRef="38" />
          <Parent isRef="38" />
          <Sort isList="true" count="0" />
        </数据区2>
        <栏尾1 Ref="143" type="Stimulsoft.Report.Components.StiColumnFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,15,51,0.8</ClientRectangle>
          <Components isList="true" count="2">
            <文本128 Ref="144" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本128</Name>
              <Page isRef="38" />
              <Parent isRef="143" />
              <Text>合计</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="145" type="DateFormat" isKey="true" />
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本128>
            <面板11 Ref="146" type="Panel" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.4,0,13,0.8</ClientRectangle>
              <Components isList="true" count="1">
                <交叉_数据区11 Ref="147" type="CrossDataBand" isKey="true">
                  <Brush>Transparent</Brush>
                  <BusinessObjectGuid isNull="true" />
                  <ClientRectangle>0,0,8.8,0.8</ClientRectangle>
                  <Components isList="true" count="4">
                    <文本95 Ref="148" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本95</Name>
                      <Page isRef="38" />
                      <Parent isRef="147" />
                      <Text>{root_data_gsrcSumList.nightNum}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本95>
                    <文本96 Ref="149" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>2.2,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本96</Name>
                      <Page isRef="38" />
                      <Parent isRef="147" />
                      <Text>{root_data_gsrcSumList.roomFee}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本96>
                    <文本98 Ref="150" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>4.4,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本98</Name>
                      <Page isRef="38" />
                      <Parent isRef="147" />
                      <Text>{root_data_gsrcSumList.avgRoomFee}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本98>
                    <文本99 Ref="151" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>6.6,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本99</Name>
                      <Page isRef="38" />
                      <Parent isRef="147" />
                      <Text>{root_data_gsrcSumList.nightNum/TotalRoomNumSum}</Text>
                      <TextBrush>Black</TextBrush>
                      <TextFormat Ref="152" type="PercentageFormat" isKey="true">
                        <DecimalDigits>2</DecimalDigits>
                        <GroupSeparator>,</GroupSeparator>
                        <NegativePattern>1</NegativePattern>
                        <PositivePattern>1</PositivePattern>
                        <Symbol>%</Symbol>
                      </TextFormat>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本99>
                  </Components>
                  <Conditions isList="true" count="0" />
                  <DataRelationName isNull="true" />
                  <DataSourceName>root_data_gsrcSumList</DataSourceName>
                  <Expressions isList="true" count="0" />
                  <Filters isList="true" count="0" />
                  <Name>交叉_数据区11</Name>
                  <Page isRef="38" />
                  <Parent isRef="146" />
                  <Sort isList="true" count="0" />
                </交叉_数据区11>
              </Components>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Name>面板11</Name>
              <Page isRef="38" />
              <Parent isRef="143" />
            </面板11>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>栏尾1</Name>
          <Page isRef="38" />
          <Parent isRef="38" />
        </栏尾1>
        <页眉3 Ref="153" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,16.6,51,1.2</ClientRectangle>
          <Components isList="true" count="1">
            <文本55 Ref="154" type="Text" isKey="true">
              <Border>Left, Right, Bottom;Black;1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2.4,1.2</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <Margins>5,0,0,5</Margins>
              <Name>文本55</Name>
              <Page isRef="38" />
              <Parent isRef="153" />
              <Text>入住类型</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </文本55>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>页眉3</Name>
          <Page isRef="38" />
          <Parent isRef="38" />
        </页眉3>
        <栏首2 Ref="155" type="Stimulsoft.Report.Components.StiColumnHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,18.6,51,1.6</ClientRectangle>
          <Components isList="true" count="2">
            <面板3 Ref="156" type="Panel" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.4,0,13,1.6</ClientRectangle>
              <Components isList="true" count="1">
                <交叉_数据区3 Ref="157" type="CrossDataBand" isKey="true">
                  <Brush>Transparent</Brush>
                  <BusinessObjectGuid isNull="true" />
                  <ClientRectangle>0,0,8.8,1.6</ClientRectangle>
                  <Components isList="true" count="5">
                    <文本47 Ref="158" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0,8.8,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本47</Name>
                      <Page isRef="38" />
                      <Parent isRef="157" />
                      <Text>{root_data_inTypeList_dataClassStatMonthList.statName}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本47>
                    <文本48 Ref="159" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0.8,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本48</Name>
                      <Page isRef="38" />
                      <Parent isRef="157" />
                      <Text>间夜数</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本48>
                    <文本50 Ref="160" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>2.2,0.8,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本50</Name>
                      <Page isRef="38" />
                      <Parent isRef="157" />
                      <Text>房费</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本50>
                    <文本51 Ref="161" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>4.4,0.8,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本51</Name>
                      <Page isRef="38" />
                      <Parent isRef="157" />
                      <Text>平均房价</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本51>
                    <文本52 Ref="162" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>6.6,0.8,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本52</Name>
                      <Page isRef="38" />
                      <Parent isRef="157" />
                      <Text>出租率</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本52>
                  </Components>
                  <Conditions isList="true" count="0" />
                  <DataRelationName>root_data_inTypeList_dataClassStatMonthList</DataRelationName>
                  <DataSourceName>root_data_inTypeList_dataClassStatMonthList</DataSourceName>
                  <Expressions isList="true" count="0" />
                  <Filters isList="true" count="0" />
                  <MasterComponent isRef="164" />
                  <Name>交叉_数据区3</Name>
                  <Page isRef="38" />
                  <Parent isRef="156" />
                  <Sort isList="true" count="0" />
                </交叉_数据区3>
              </Components>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Name>面板3</Name>
              <Page isRef="38" />
              <Parent isRef="155" />
            </面板3>
            <文本53 Ref="163" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2.4,1.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本53</Name>
              <Page isRef="38" />
              <Parent isRef="155" />
              <Text>月份</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本53>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>栏首2</Name>
          <Page isRef="38" />
          <Parent isRef="38" />
        </栏首2>
        <数据区3 Ref="164" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,21,51,0.8</ClientRectangle>
          <Components isList="true" count="2">
            <文本54 Ref="165" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Hyperlink>#/?name=StoreManagerMonthReport&amp;hcode={root_data.hcode}&amp;startDate={root_data_inTypeList.month}&amp;endDate={root_data_inTypeList.month}</Hyperlink>
              <Name>文本54</Name>
              <Page isRef="38" />
              <Parent isRef="164" />
              <Text>{root_data_inTypeList.month}</Text>
              <TextBrush>[0:176:240]</TextBrush>
              <TextFormat Ref="166" type="CustomFormat" isKey="true">
                <StringFormat>yyyy/MM</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本54>
            <面板4 Ref="167" type="Panel" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.4,0,13,0.8</ClientRectangle>
              <Components isList="true" count="1">
                <交叉_数据区4 Ref="168" type="CrossDataBand" isKey="true">
                  <Brush>Transparent</Brush>
                  <BusinessObjectGuid isNull="true" />
                  <ClientRectangle>0,0,8.8,0.8</ClientRectangle>
                  <Components isList="true" count="4">
                    <文本56 Ref="169" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本56</Name>
                      <Page isRef="38" />
                      <Parent isRef="168" />
                      <Text>{root_data_inTypeList_dataClassStatMonthList.nightNum}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本56>
                    <文本57 Ref="170" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>2.2,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本57</Name>
                      <Page isRef="38" />
                      <Parent isRef="168" />
                      <Text>{root_data_inTypeList_dataClassStatMonthList.roomFee}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本57>
                    <文本58 Ref="171" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>4.4,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本58</Name>
                      <Page isRef="38" />
                      <Parent isRef="168" />
                      <Text>{root_data_inTypeList_dataClassStatMonthList.avgRoomFee}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本58>
                    <文本59 Ref="172" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>6.6,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本59</Name>
                      <Page isRef="38" />
                      <Parent isRef="168" />
                      <Text>{root_data_inTypeList_dataClassStatMonthList.occ}%</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本59>
                  </Components>
                  <Conditions isList="true" count="0" />
                  <DataRelationName>root_data_inTypeList_dataClassStatMonthList</DataRelationName>
                  <DataSourceName>root_data_inTypeList_dataClassStatMonthList</DataSourceName>
                  <Expressions isList="true" count="0" />
                  <Filters isList="true" count="0" />
                  <MasterComponent isRef="164" />
                  <Name>交叉_数据区4</Name>
                  <Page isRef="38" />
                  <Parent isRef="167" />
                  <Sort isList="true" count="0" />
                </交叉_数据区4>
              </Components>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Name>面板4</Name>
              <Page isRef="38" />
              <Parent isRef="164" />
            </面板4>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>root_data_inTypeList</DataSourceName>
          <Expressions isList="true" count="0" />
          <FilterOn>False</FilterOn>
          <Filters isList="true" count="0" />
          <Name>数据区3</Name>
          <Page isRef="38" />
          <Parent isRef="38" />
          <Sort isList="true" count="0" />
        </数据区3>
        <栏尾2 Ref="173" type="Stimulsoft.Report.Components.StiColumnFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,22.6,51,0.8</ClientRectangle>
          <Components isList="true" count="2">
            <文本118 Ref="174" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本118</Name>
              <Page isRef="38" />
              <Parent isRef="173" />
              <Text>合计</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="175" type="DateFormat" isKey="true" />
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本118>
            <面板12 Ref="176" type="Panel" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.4,0,13,0.8</ClientRectangle>
              <Components isList="true" count="1">
                <交叉_数据区12 Ref="177" type="CrossDataBand" isKey="true">
                  <Brush>Transparent</Brush>
                  <BusinessObjectGuid isNull="true" />
                  <ClientRectangle>0,0,8.8,0.8</ClientRectangle>
                  <Components isList="true" count="4">
                    <文本119 Ref="178" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本119</Name>
                      <Page isRef="38" />
                      <Parent isRef="177" />
                      <Text>{root_data_inTypeSumList.nightNum}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本119>
                    <文本120 Ref="179" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>2.2,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本120</Name>
                      <Page isRef="38" />
                      <Parent isRef="177" />
                      <Text>{root_data_inTypeSumList.roomFee}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本120>
                    <文本121 Ref="180" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>4.4,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本121</Name>
                      <Page isRef="38" />
                      <Parent isRef="177" />
                      <Text>{root_data_inTypeSumList.avgRoomFee}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本121>
                    <文本122 Ref="181" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>6.6,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本122</Name>
                      <Page isRef="38" />
                      <Parent isRef="177" />
                      <Text>{root_data_inTypeSumList.nightNum/TotalRoomNumSum}</Text>
                      <TextBrush>Black</TextBrush>
                      <TextFormat Ref="182" type="PercentageFormat" isKey="true">
                        <DecimalDigits>2</DecimalDigits>
                        <GroupSeparator>,</GroupSeparator>
                        <NegativePattern>1</NegativePattern>
                        <PositivePattern>1</PositivePattern>
                        <Symbol>%</Symbol>
                      </TextFormat>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本122>
                  </Components>
                  <Conditions isList="true" count="0" />
                  <DataRelationName isNull="true" />
                  <DataSourceName>root_data_inTypeSumList</DataSourceName>
                  <Expressions isList="true" count="0" />
                  <Filters isList="true" count="0" />
                  <Name>交叉_数据区12</Name>
                  <Page isRef="38" />
                  <Parent isRef="176" />
                  <Sort isList="true" count="0" />
                </交叉_数据区12>
              </Components>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Name>面板12</Name>
              <Page isRef="38" />
              <Parent isRef="173" />
            </面板12>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>栏尾2</Name>
          <Page isRef="38" />
          <Parent isRef="38" />
        </栏尾2>
        <页眉4 Ref="183" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,24.2,51,1.2</ClientRectangle>
          <Components isList="true" count="1">
            <文本69 Ref="184" type="Text" isKey="true">
              <Border>Left, Right, Bottom;Black;1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2.4,1.2</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <Margins>5,0,0,5</Margins>
              <Name>文本69</Name>
              <Page isRef="38" />
              <Parent isRef="183" />
              <Text>订单来源</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </文本69>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>页眉4</Name>
          <Page isRef="38" />
          <Parent isRef="38" />
        </页眉4>
        <栏首3 Ref="185" type="Stimulsoft.Report.Components.StiColumnHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,26.2,51,1.6</ClientRectangle>
          <Components isList="true" count="2">
            <面板5 Ref="186" type="Panel" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.4,0,13,1.6</ClientRectangle>
              <Components isList="true" count="1">
                <交叉_数据区5 Ref="187" type="CrossDataBand" isKey="true">
                  <Brush>Transparent</Brush>
                  <BusinessObjectGuid isNull="true" />
                  <ClientRectangle>0,0,8.8,1.6</ClientRectangle>
                  <Components isList="true" count="5">
                    <文本60 Ref="188" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0,8.8,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本60</Name>
                      <Page isRef="38" />
                      <Parent isRef="187" />
                      <Text>{root_data_orderSrcList_dataClassStatMonthList.statName}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本60>
                    <文本61 Ref="189" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0.8,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本61</Name>
                      <Page isRef="38" />
                      <Parent isRef="187" />
                      <Text>间夜数</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本61>
                    <文本62 Ref="190" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>2.2,0.8,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本62</Name>
                      <Page isRef="38" />
                      <Parent isRef="187" />
                      <Text>房费</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本62>
                    <文本63 Ref="191" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>4.4,0.8,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本63</Name>
                      <Page isRef="38" />
                      <Parent isRef="187" />
                      <Text>平均房价</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本63>
                    <文本64 Ref="192" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>6.6,0.8,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本64</Name>
                      <Page isRef="38" />
                      <Parent isRef="187" />
                      <Text>出租率</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本64>
                  </Components>
                  <Conditions isList="true" count="0" />
                  <DataRelationName>root_data_orderSrcList_dataClassStatMonthList</DataRelationName>
                  <DataSourceName>root_data_orderSrcList_dataClassStatMonthList</DataSourceName>
                  <Expressions isList="true" count="0" />
                  <Filters isList="true" count="0" />
                  <MasterComponent isRef="194" />
                  <Name>交叉_数据区5</Name>
                  <Page isRef="38" />
                  <Parent isRef="186" />
                  <Sort isList="true" count="0" />
                </交叉_数据区5>
              </Components>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Name>面板5</Name>
              <Page isRef="38" />
              <Parent isRef="185" />
            </面板5>
            <文本65 Ref="193" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2.4,1.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本65</Name>
              <Page isRef="38" />
              <Parent isRef="185" />
              <Text>月份</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本65>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>栏首3</Name>
          <Page isRef="38" />
          <Parent isRef="38" />
        </栏首3>
        <数据区4 Ref="194" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,28.6,51,0.8</ClientRectangle>
          <Components isList="true" count="2">
            <文本66 Ref="195" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Hyperlink>#/?name=StoreManagerMonthReport&amp;hcode={root_data.hcode}&amp;startDate={root_data_orderSrcList.month}&amp;endDate={root_data_orderSrcList.month}</Hyperlink>
              <Name>文本66</Name>
              <Page isRef="38" />
              <Parent isRef="194" />
              <Text>{root_data_orderSrcList.month}</Text>
              <TextBrush>[0:176:240]</TextBrush>
              <TextFormat Ref="196" type="CustomFormat" isKey="true">
                <StringFormat>yyyy/MM</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本66>
            <面板6 Ref="197" type="Panel" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.4,0,13,0.8</ClientRectangle>
              <Components isList="true" count="1">
                <交叉_数据区6 Ref="198" type="CrossDataBand" isKey="true">
                  <Brush>Transparent</Brush>
                  <BusinessObjectGuid isNull="true" />
                  <ClientRectangle>0,0,8.8,0.8</ClientRectangle>
                  <Components isList="true" count="4">
                    <文本67 Ref="199" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本67</Name>
                      <Page isRef="38" />
                      <Parent isRef="198" />
                      <Text>{root_data_orderSrcList_dataClassStatMonthList.nightNum}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本67>
                    <文本68 Ref="200" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>2.2,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本68</Name>
                      <Page isRef="38" />
                      <Parent isRef="198" />
                      <Text>{root_data_orderSrcList_dataClassStatMonthList.roomFee}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本68>
                    <文本70 Ref="201" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>4.4,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本70</Name>
                      <Page isRef="38" />
                      <Parent isRef="198" />
                      <Text>{root_data_orderSrcList_dataClassStatMonthList.avgRoomFee}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本70>
                    <文本71 Ref="202" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>6.6,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本71</Name>
                      <Page isRef="38" />
                      <Parent isRef="198" />
                      <Text>{root_data_orderSrcList_dataClassStatMonthList.occ}%</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本71>
                  </Components>
                  <Conditions isList="true" count="0" />
                  <DataRelationName>root_data_orderSrcList_dataClassStatMonthList</DataRelationName>
                  <DataSourceName>root_data_orderSrcList_dataClassStatMonthList</DataSourceName>
                  <Expressions isList="true" count="0" />
                  <Filters isList="true" count="0" />
                  <MasterComponent isRef="194" />
                  <Name>交叉_数据区6</Name>
                  <Page isRef="38" />
                  <Parent isRef="197" />
                  <Sort isList="true" count="0" />
                </交叉_数据区6>
              </Components>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Name>面板6</Name>
              <Page isRef="38" />
              <Parent isRef="194" />
            </面板6>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>root_data_orderSrcList</DataSourceName>
          <Expressions isList="true" count="0" />
          <FilterOn>False</FilterOn>
          <Filters isList="true" count="0" />
          <Name>数据区4</Name>
          <Page isRef="38" />
          <Parent isRef="38" />
          <Sort isList="true" count="0" />
        </数据区4>
        <栏尾3 Ref="203" type="Stimulsoft.Report.Components.StiColumnFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,30.2,51,0.8</ClientRectangle>
          <Components isList="true" count="2">
            <文本123 Ref="204" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本123</Name>
              <Page isRef="38" />
              <Parent isRef="203" />
              <Text>合计</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="205" type="DateFormat" isKey="true" />
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本123>
            <面板13 Ref="206" type="Panel" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.4,0,13,0.8</ClientRectangle>
              <Components isList="true" count="1">
                <交叉_数据区13 Ref="207" type="CrossDataBand" isKey="true">
                  <Brush>Transparent</Brush>
                  <BusinessObjectGuid isNull="true" />
                  <ClientRectangle>0,0,8.8,0.8</ClientRectangle>
                  <Components isList="true" count="4">
                    <文本124 Ref="208" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本124</Name>
                      <Page isRef="38" />
                      <Parent isRef="207" />
                      <Text>{root_data_orderSrcSumList.nightNum}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本124>
                    <文本125 Ref="209" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>2.2,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本125</Name>
                      <Page isRef="38" />
                      <Parent isRef="207" />
                      <Text>{root_data_orderSrcSumList.roomFee}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本125>
                    <文本126 Ref="210" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>4.4,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本126</Name>
                      <Page isRef="38" />
                      <Parent isRef="207" />
                      <Text>{root_data_orderSrcSumList.avgRoomFee}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本126>
                    <文本127 Ref="211" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>6.6,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本127</Name>
                      <Page isRef="38" />
                      <Parent isRef="207" />
                      <Text>{root_data_orderSrcSumList.nightNum/TotalRoomNumSum}</Text>
                      <TextBrush>Black</TextBrush>
                      <TextFormat Ref="212" type="PercentageFormat" isKey="true">
                        <DecimalDigits>2</DecimalDigits>
                        <GroupSeparator>,</GroupSeparator>
                        <NegativePattern>1</NegativePattern>
                        <PositivePattern>1</PositivePattern>
                        <Symbol>%</Symbol>
                      </TextFormat>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本127>
                  </Components>
                  <Conditions isList="true" count="0" />
                  <DataRelationName isNull="true" />
                  <DataSourceName>root_data_orderSrcSumList</DataSourceName>
                  <Expressions isList="true" count="0" />
                  <Filters isList="true" count="0" />
                  <Name>交叉_数据区13</Name>
                  <Page isRef="38" />
                  <Parent isRef="206" />
                  <Sort isList="true" count="0" />
                </交叉_数据区13>
              </Components>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Name>面板13</Name>
              <Page isRef="38" />
              <Parent isRef="203" />
            </面板13>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>栏尾3</Name>
          <Page isRef="38" />
          <Parent isRef="38" />
        </栏尾3>
        <页眉5 Ref="213" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,31.8,51,1.2</ClientRectangle>
          <Components isList="true" count="1">
            <文本83 Ref="214" type="Text" isKey="true">
              <Border>Left, Right, Bottom;Black;1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2.4,1.2</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <Margins>5,0,0,5</Margins>
              <Name>文本83</Name>
              <Page isRef="38" />
              <Parent isRef="213" />
              <Text>房型</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </文本83>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>页眉5</Name>
          <Page isRef="38" />
          <Parent isRef="38" />
        </页眉5>
        <栏首4 Ref="215" type="Stimulsoft.Report.Components.StiColumnHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,33.8,51,1.6</ClientRectangle>
          <Components isList="true" count="2">
            <面板7 Ref="216" type="Panel" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.4,0,13,1.6</ClientRectangle>
              <Components isList="true" count="1">
                <交叉_数据区7 Ref="217" type="CrossDataBand" isKey="true">
                  <Brush>Transparent</Brush>
                  <BusinessObjectGuid isNull="true" />
                  <ClientRectangle>0,0,8.8,1.6</ClientRectangle>
                  <Components isList="true" count="5">
                    <文本72 Ref="218" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0,8.8,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本72</Name>
                      <Page isRef="38" />
                      <Parent isRef="217" />
                      <Text>{root_data_rtCodeList_dataClassStatMonthList.statName}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本72>
                    <文本73 Ref="219" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0.8,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本73</Name>
                      <Page isRef="38" />
                      <Parent isRef="217" />
                      <Text>间夜数</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本73>
                    <文本74 Ref="220" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>2.2,0.8,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本74</Name>
                      <Page isRef="38" />
                      <Parent isRef="217" />
                      <Text>房费</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本74>
                    <文本75 Ref="221" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>4.4,0.8,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本75</Name>
                      <Page isRef="38" />
                      <Parent isRef="217" />
                      <Text>平均房价</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本75>
                    <文本76 Ref="222" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>6.6,0.8,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本76</Name>
                      <Page isRef="38" />
                      <Parent isRef="217" />
                      <Text>出租率</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本76>
                  </Components>
                  <Conditions isList="true" count="0" />
                  <DataRelationName>root_data_rtCodeList_dataClassStatMonthList</DataRelationName>
                  <DataSourceName>root_data_rtCodeList_dataClassStatMonthList</DataSourceName>
                  <Expressions isList="true" count="0" />
                  <Filters isList="true" count="0" />
                  <MasterComponent isRef="224" />
                  <Name>交叉_数据区7</Name>
                  <Page isRef="38" />
                  <Parent isRef="216" />
                  <Sort isList="true" count="0" />
                </交叉_数据区7>
              </Components>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Name>面板7</Name>
              <Page isRef="38" />
              <Parent isRef="215" />
            </面板7>
            <文本77 Ref="223" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2.4,1.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本77</Name>
              <Page isRef="38" />
              <Parent isRef="215" />
              <Text>月份</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本77>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>栏首4</Name>
          <Page isRef="38" />
          <Parent isRef="38" />
        </栏首4>
        <数据区5 Ref="224" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,36.2,51,0.8</ClientRectangle>
          <Components isList="true" count="2">
            <文本78 Ref="225" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Hyperlink>#/?name=StoreManagerMonthReport&amp;hcode={root_data.hcode}&amp;startDate={root_data_rtCodeList.month}&amp;endDate={root_data_rtCodeList.month}</Hyperlink>
              <Name>文本78</Name>
              <Page isRef="38" />
              <Parent isRef="224" />
              <Text>{root_data_rtCodeList.month}</Text>
              <TextBrush>[0:176:240]</TextBrush>
              <TextFormat Ref="226" type="CustomFormat" isKey="true">
                <StringFormat>yyyy/MM</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本78>
            <面板8 Ref="227" type="Panel" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.4,0,13,0.8</ClientRectangle>
              <Components isList="true" count="1">
                <交叉_数据区8 Ref="228" type="CrossDataBand" isKey="true">
                  <Brush>Transparent</Brush>
                  <BusinessObjectGuid isNull="true" />
                  <ClientRectangle>0,0,8.8,0.8</ClientRectangle>
                  <Components isList="true" count="4">
                    <文本79 Ref="229" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本79</Name>
                      <Page isRef="38" />
                      <Parent isRef="228" />
                      <Text>{root_data_rtCodeList_dataClassStatMonthList.nightNum}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本79>
                    <文本80 Ref="230" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>2.2,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本80</Name>
                      <Page isRef="38" />
                      <Parent isRef="228" />
                      <Text>{root_data_rtCodeList_dataClassStatMonthList.roomFee}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本80>
                    <文本81 Ref="231" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>4.4,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本81</Name>
                      <Page isRef="38" />
                      <Parent isRef="228" />
                      <Text>{root_data_rtCodeList_dataClassStatMonthList.avgRoomFee}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本81>
                    <文本82 Ref="232" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>6.6,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本82</Name>
                      <Page isRef="38" />
                      <Parent isRef="228" />
                      <Text>{root_data_rtCodeList_dataClassStatMonthList.occ}%</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本82>
                  </Components>
                  <Conditions isList="true" count="0" />
                  <DataRelationName>root_data_rtCodeList_dataClassStatMonthList</DataRelationName>
                  <DataSourceName>root_data_rtCodeList_dataClassStatMonthList</DataSourceName>
                  <Expressions isList="true" count="0" />
                  <Filters isList="true" count="0" />
                  <MasterComponent isRef="224" />
                  <Name>交叉_数据区8</Name>
                  <Page isRef="38" />
                  <Parent isRef="227" />
                  <Sort isList="true" count="0" />
                </交叉_数据区8>
              </Components>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Name>面板8</Name>
              <Page isRef="38" />
              <Parent isRef="224" />
            </面板8>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>root_data_rtCodeList</DataSourceName>
          <Expressions isList="true" count="0" />
          <FilterOn>False</FilterOn>
          <Filters isList="true" count="0" />
          <Name>数据区5</Name>
          <Page isRef="38" />
          <Parent isRef="38" />
          <Sort isList="true" count="0" />
        </数据区5>
        <栏尾4 Ref="233" type="Stimulsoft.Report.Components.StiColumnFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,37.8,51,0.8</ClientRectangle>
          <Components isList="true" count="2">
            <文本129 Ref="234" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本129</Name>
              <Page isRef="38" />
              <Parent isRef="233" />
              <Text>合计</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="235" type="DateFormat" isKey="true" />
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本129>
            <面板14 Ref="236" type="Panel" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.4,0,13,0.8</ClientRectangle>
              <Components isList="true" count="1">
                <交叉_数据区14 Ref="237" type="CrossDataBand" isKey="true">
                  <Brush>Transparent</Brush>
                  <BusinessObjectGuid isNull="true" />
                  <ClientRectangle>0,0,8.8,0.8</ClientRectangle>
                  <Components isList="true" count="4">
                    <文本130 Ref="238" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本130</Name>
                      <Page isRef="38" />
                      <Parent isRef="237" />
                      <Text>{root_data_rtCodeSumList.nightNum}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本130>
                    <文本131 Ref="239" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>2.2,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本131</Name>
                      <Page isRef="38" />
                      <Parent isRef="237" />
                      <Text>{root_data_rtCodeSumList.roomFee}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本131>
                    <文本132 Ref="240" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>4.4,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本132</Name>
                      <Page isRef="38" />
                      <Parent isRef="237" />
                      <Text>{root_data_rtCodeSumList.avgRoomFee}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本132>
                    <文本133 Ref="241" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>6.6,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本133</Name>
                      <Page isRef="38" />
                      <Parent isRef="237" />
                      <Text>{root_data_rtCodeSumList.nightNum/TotalRoomNumSum}</Text>
                      <TextBrush>Black</TextBrush>
                      <TextFormat Ref="242" type="PercentageFormat" isKey="true">
                        <DecimalDigits>2</DecimalDigits>
                        <GroupSeparator>,</GroupSeparator>
                        <NegativePattern>1</NegativePattern>
                        <PositivePattern>1</PositivePattern>
                        <Symbol>%</Symbol>
                      </TextFormat>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本133>
                  </Components>
                  <Conditions isList="true" count="0" />
                  <DataRelationName isNull="true" />
                  <DataSourceName>root_data_rtCodeSumList</DataSourceName>
                  <Expressions isList="true" count="0" />
                  <Filters isList="true" count="0" />
                  <Name>交叉_数据区14</Name>
                  <Page isRef="38" />
                  <Parent isRef="236" />
                  <Sort isList="true" count="0" />
                </交叉_数据区14>
              </Components>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Name>面板14</Name>
              <Page isRef="38" />
              <Parent isRef="233" />
            </面板14>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>栏尾4</Name>
          <Page isRef="38" />
          <Parent isRef="38" />
        </栏尾4>
        <页眉6 Ref="243" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,39.4,51,1.2</ClientRectangle>
          <Components isList="true" count="1">
            <文本97 Ref="244" type="Text" isKey="true">
              <Border>Left, Right, Bottom;Black;1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2.4,1.2</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <Margins>5,0,0,5</Margins>
              <Name>文本97</Name>
              <Page isRef="38" />
              <Parent isRef="243" />
              <Text>渠道</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </文本97>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>页眉6</Name>
          <Page isRef="38" />
          <Parent isRef="38" />
        </页眉6>
        <栏首5 Ref="245" type="Stimulsoft.Report.Components.StiColumnHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,41.4,51,1.6</ClientRectangle>
          <Components isList="true" count="2">
            <面板9 Ref="246" type="Panel" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.4,0,13,1.6</ClientRectangle>
              <Components isList="true" count="1">
                <交叉_数据区9 Ref="247" type="CrossDataBand" isKey="true">
                  <Brush>Transparent</Brush>
                  <BusinessObjectGuid isNull="true" />
                  <ClientRectangle>0,0,8.8,1.6</ClientRectangle>
                  <Components isList="true" count="5">
                    <文本84 Ref="248" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0,8.8,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本84</Name>
                      <Page isRef="38" />
                      <Parent isRef="247" />
                      <Text>{root_data_statChannelList_dataClassStatMonthList.statName}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本84>
                    <文本85 Ref="249" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0.8,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本85</Name>
                      <Page isRef="38" />
                      <Parent isRef="247" />
                      <Text>间夜数</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本85>
                    <文本86 Ref="250" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>2.2,0.8,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本86</Name>
                      <Page isRef="38" />
                      <Parent isRef="247" />
                      <Text>房费</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本86>
                    <文本87 Ref="251" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>4.4,0.8,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本87</Name>
                      <Page isRef="38" />
                      <Parent isRef="247" />
                      <Text>平均房价</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本87>
                    <文本88 Ref="252" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>6.6,0.8,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本88</Name>
                      <Page isRef="38" />
                      <Parent isRef="247" />
                      <Text>出租率</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本88>
                  </Components>
                  <Conditions isList="true" count="0" />
                  <DataRelationName>root_data_statChannelList_dataClassStatMonthList</DataRelationName>
                  <DataSourceName>root_data_statChannelList_dataClassStatMonthList</DataSourceName>
                  <Expressions isList="true" count="0" />
                  <Filters isList="true" count="0" />
                  <MasterComponent isRef="254" />
                  <Name>交叉_数据区9</Name>
                  <Page isRef="38" />
                  <Parent isRef="246" />
                  <Sort isList="true" count="0" />
                </交叉_数据区9>
              </Components>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Name>面板9</Name>
              <Page isRef="38" />
              <Parent isRef="245" />
            </面板9>
            <文本89 Ref="253" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2.4,1.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本89</Name>
              <Page isRef="38" />
              <Parent isRef="245" />
              <Text>月份</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本89>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>栏首5</Name>
          <Page isRef="38" />
          <Parent isRef="38" />
        </栏首5>
        <数据区6 Ref="254" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,43.8,51,0.8</ClientRectangle>
          <Components isList="true" count="2">
            <文本90 Ref="255" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Hyperlink>#/?name=StoreManagerMonthReport&amp;hcode={root_data.hcode}&amp;startDate={root_data_statChannelList.month}&amp;endDate={root_data_statChannelList.month}</Hyperlink>
              <Name>文本90</Name>
              <Page isRef="38" />
              <Parent isRef="254" />
              <Text>{root_data_statChannelList.month}</Text>
              <TextBrush>[0:176:240]</TextBrush>
              <TextFormat Ref="256" type="CustomFormat" isKey="true">
                <StringFormat>yyyy/MM</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本90>
            <面板10 Ref="257" type="Panel" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.4,0,13,0.8</ClientRectangle>
              <Components isList="true" count="1">
                <交叉_数据区10 Ref="258" type="CrossDataBand" isKey="true">
                  <Brush>Transparent</Brush>
                  <BusinessObjectGuid isNull="true" />
                  <ClientRectangle>0,0,8.8,0.8</ClientRectangle>
                  <Components isList="true" count="4">
                    <文本91 Ref="259" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本91</Name>
                      <Page isRef="38" />
                      <Parent isRef="258" />
                      <Text>{root_data_statChannelList_dataClassStatMonthList.nightNum}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本91>
                    <文本92 Ref="260" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>2.2,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本92</Name>
                      <Page isRef="38" />
                      <Parent isRef="258" />
                      <Text>{root_data_statChannelList_dataClassStatMonthList.roomFee}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本92>
                    <文本93 Ref="261" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>4.4,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本93</Name>
                      <Page isRef="38" />
                      <Parent isRef="258" />
                      <Text>{root_data_statChannelList_dataClassStatMonthList.avgRoomFee}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本93>
                    <文本94 Ref="262" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>6.6,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本94</Name>
                      <Page isRef="38" />
                      <Parent isRef="258" />
                      <Text>{root_data_statChannelList_dataClassStatMonthList.occ}%</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本94>
                  </Components>
                  <Conditions isList="true" count="0" />
                  <DataRelationName>root_data_statChannelList_dataClassStatMonthList</DataRelationName>
                  <DataSourceName>root_data_statChannelList_dataClassStatMonthList</DataSourceName>
                  <Expressions isList="true" count="0" />
                  <Filters isList="true" count="0" />
                  <MasterComponent isRef="254" />
                  <Name>交叉_数据区10</Name>
                  <Page isRef="38" />
                  <Parent isRef="257" />
                  <Sort isList="true" count="0" />
                </交叉_数据区10>
              </Components>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Name>面板10</Name>
              <Page isRef="38" />
              <Parent isRef="254" />
            </面板10>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>root_data_statChannelList</DataSourceName>
          <Expressions isList="true" count="0" />
          <FilterOn>False</FilterOn>
          <Filters isList="true" count="0" />
          <Name>数据区6</Name>
          <Page isRef="38" />
          <Parent isRef="38" />
          <Sort isList="true" count="0" />
        </数据区6>
        <栏尾5 Ref="263" type="Stimulsoft.Report.Components.StiColumnFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,45.4,51,0.8</ClientRectangle>
          <Components isList="true" count="2">
            <文本134 Ref="264" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本134</Name>
              <Page isRef="38" />
              <Parent isRef="263" />
              <Text>合计</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="265" type="DateFormat" isKey="true" />
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本134>
            <面板15 Ref="266" type="Panel" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.4,0,13,0.8</ClientRectangle>
              <Components isList="true" count="1">
                <交叉_数据区15 Ref="267" type="CrossDataBand" isKey="true">
                  <Brush>Transparent</Brush>
                  <BusinessObjectGuid isNull="true" />
                  <ClientRectangle>0,0,8.8,0.8</ClientRectangle>
                  <Components isList="true" count="4">
                    <文本135 Ref="268" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本135</Name>
                      <Page isRef="38" />
                      <Parent isRef="267" />
                      <Text>{root_data_statChannelSumList.nightNum}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本135>
                    <文本136 Ref="269" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>2.2,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本136</Name>
                      <Page isRef="38" />
                      <Parent isRef="267" />
                      <Text>{root_data_statChannelSumList.roomFee}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本136>
                    <文本137 Ref="270" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>4.4,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本137</Name>
                      <Page isRef="38" />
                      <Parent isRef="267" />
                      <Text>{root_data_statChannelSumList.avgRoomFee}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本137>
                    <文本138 Ref="271" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>6.6,0,2.2,0.8</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>微软雅黑,10,Bold</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本138</Name>
                      <Page isRef="38" />
                      <Parent isRef="267" />
                      <Text>{root_data_statChannelSumList.nightNum/TotalRoomNumSum}</Text>
                      <TextBrush>Black</TextBrush>
                      <TextFormat Ref="272" type="PercentageFormat" isKey="true">
                        <DecimalDigits>2</DecimalDigits>
                        <GroupSeparator>,</GroupSeparator>
                        <NegativePattern>1</NegativePattern>
                        <PositivePattern>1</PositivePattern>
                        <Symbol>%</Symbol>
                      </TextFormat>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本138>
                  </Components>
                  <Conditions isList="true" count="0" />
                  <DataRelationName isNull="true" />
                  <DataSourceName>root_data_statChannelSumList</DataSourceName>
                  <Expressions isList="true" count="0" />
                  <Filters isList="true" count="0" />
                  <Name>交叉_数据区15</Name>
                  <Page isRef="38" />
                  <Parent isRef="266" />
                  <Sort isList="true" count="0" />
                </交叉_数据区15>
              </Components>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Name>面板15</Name>
              <Page isRef="38" />
              <Parent isRef="263" />
            </面板15>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>栏尾5</Name>
          <Page isRef="38" />
          <Parent isRef="38" />
        </栏尾5>
      </Components>
      <Conditions isList="true" count="0" />
      <Expressions isList="true" count="0" />
      <Guid>e2179456f84c44e185e5458abc9ad075</Guid>
      <Margins>1,1,1,1</Margins>
      <Name>Page1</Name>
      <PageHeight>34.7</PageHeight>
      <PageWidth>53</PageWidth>
      <Report isRef="0" />
    </Page1>
  </Pages>
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>报表</ReportAlias>
  <ReportAuthor>xiao lin</ReportAuthor>
  <ReportChanged>7/17/2025 9:30:26 AM</ReportChanged>
  <ReportCreated>5/8/2025 2:42:40 PM</ReportCreated>
  <ReportFile>D:\11\2\pms-report\public\reports\managerMonthStatistics.mrt</ReportFile>
  <ReportGuid>e7181ecef0b6408d9491f07a306dc517</ReportGuid>
  <ReportName>报表</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2024.3.5.0</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class 报表 : Stimulsoft.Report.StiReport
    {
        public 报表()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
		#endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>