import api from '../../index.ts'

// 获得班次设置列表
export const getShiftTimeList = (data: any) => api.get('/admin-api/pms/shift-time/list', { params: data })
// 获得交班报表
export const handoverReport = (data: any) => api.get('/admin-api/pms/account/handover-report', { params: data })
// 批量获取字典数据
export const getDictDataBatch = (dictTypes: string[]) => api.get('/admin-api/system/dict-data/get/batch', { params: { dictTypes: dictTypes.join(',') } })
// 获得门店简单信息
export const simpleListApi = (data: any) => api.get('/admin-api/system/merchant/simple-list', { params: data })
// 获得AR账户简单信息
export const arSetListApi = (data: any) => api.get('/admin-api/pms/ar-set/list', { params: data })
// 获得协议单位、中介列表(只包括代码,名称和渠道代码)
export const protocolAgentListSimpleApi = (data: any) => api.get('/admin-api/pms/protocol-agent/list-simple', { params: data })
// 获得操作人列表
export const simpleUserListApi = (data: any) => api.get('/admin-api/system/user/simple-list', { params: data })
// 获得房型列表
export const roomTypeSimpleListApi = (data: any) => api.get('/admin-api/pms/room-type/simple-list', { params: data })
// 获得楼栋楼层
export const buildFloorTreeApi = (data: any) => api.get('/admin-api/pms/build-floor/tree', { params: data })
// 获取会员等级列表
export const listMemberTypeAPi = (data: any) => api.get('/admin-api/member/type/list', { params: data })
// 获得协议单位,中介列表,只包括代码和名称
export const listSimpleApi = (data: any) => api.get('/admin-api/pms/protocol-agent/list-simple', { params: data })
// 中介
export const agentListSimpleApi = (data: any) => api.get('/admin-api/pms/protocol-agent/list-simple', { params: data })
// 获取会员类型列表
export const listMemberTypeApi = (data: any) => api.get('/admin-api/member/type/list', { params: data })
// 获得字典数据
export const dictDataByTypeApi = (data: any) => api.get('/admin-api/system/dict-data/get/by-type', { params: data })
// 获得清洁员
export const listCleanerApi = (data: any) => api.get('/admin-api/system/user/simple-list-cleaner', { params: data })
// 获得销售员
export const listSellerApi = (data: any) => api.get('/admin-api/system/user/simple-list-seller', { params: data })
// 获得消费科目
export const listConsumeAccountApi = (data: any) => api.get('/admin-api/pms/general-config/list-consume-account', { params: data })
// 获得付款科目
export const listPayAccountrApi = (data: any) => api.get('/admin-api/pms/general-config/list-pay-account', { params: data })
// 获得物品分类列表
export const listThingClassApi = (data: any) => api.get('/admin-api/erp/product-category/simple-list', { params: data })
// 获得渠道列表
export const listChannelApi = (data: any) => api.get('/admin-api/pms/channel/simple-list', { params: data })
// 获得门卡记录
export const getRoomCardLogReportApi = (data: any) => api.get('/admin-api/pms/room-card-log/report', { params: data })

/*
   前台结账报表
 */
export const SettleDetailReportApi = (data: any) => api.get('/admin-api/pms/report/settle-detail', { params: data })
/*
   AR账收款明细报表
 */
export const arentryReportApi = (data: any) => api.get('/admin-api/pms/report/ar-entry', { params: data })
/*
   AR账核销明细报表
 */
export const arVerifyReportApi = (data: any) => api.get('/admin-api/pms/report/ar-verify', { params: data })
/*
   AR账发生明细报表
 */
export const arSellReportApi = (data: any) => api.get('/admin-api/pms/report/ar-sell', { params: data })
/*
   获得经理日报(固化)
 */
export const managerDailyApi = (data: any) => api.get('/admin-api/report/manager-daily/get', { params: data })
/*
   获得夜审房态表(固化)
 */
export const nightAudiRoomTypeApi = (data: any) => api.get('/admin-api/report/night-audi-room-type/get', { params: data })
/*
   获得房费日报表(固化)
 */
export const rateDailyReportApi = (data: any) => api.get('/admin-api/report/room/rate-daily/get', { params: data })
/*
   获得宾客明细报表[固化]
 */
export const guestDetailReportApi = (data: any) => api.get('/admin-api/report/guest-detail/get', { params: data })
/*
   获得付款明细报表[固化]
 */
export const payReportApi = (data: any) => api.post('/admin-api/pms/report/pay', data)
/*
   获得消费明细报表[固化]
*/
export const consumeReportApi = (data: any) => api.post('/admin-api/pms/report/consume', data)
/*
   会员卡销售明细报表
 */
export const memberCardDetailReportApi = (data: any) => api.get('/admin-api/pms/report/member-card', { params: data })
/*
   会员充值明细报表
 */
export const memberRechargeDetailApi = (data: any) => api.get('/admin-api/pms/report/member-recharge', { params: data })
/*
   获得服务员打扫客房明细报表
*/
export const roomCleanDetailReportApi = (data: any) => api.post('/admin-api/pms/report/room-clean', data)
/*
   获得服务员打扫客房汇总报表
*/
export const roomCleanSummaryReportApi = (data: any) => api.post('/admin-api/pms/report/room-clean-summary', data)
/*
   获得商品销售明细报表
 */
export const goodsSellReportApi = (data: any) => api.get('/admin-api/pms/report/goods', { params: data })
/*
   获得商品销售汇总报表
 */
export const goodsSellSummaryReportApi = (data: any) => api.get('/admin-api/pms/report/goods-summary', { params: data })
/*
   获得当前在住客人余额报表
 */
export const inhouseGuestBalanceApi = (data: any) => api.get('/admin-api/pms/report/guest-balance', { params: data })
/*
   获得早餐备餐报表
 */
export const breakfastReportApi = (data: any) => api.get('/admin-api/pms/report/get-bk', { params: data })
/*
   获得发票明细报表
 */
export const invoiceDetailReportApi = (data: any) => api.get('/admin-api/pms/report/get-invoice', { params: data })
/*
   获得交易记录表[固
 */
export const transactionReportReportApi = (data: any) => api.post('/admin-api/pms/report/transaction', data)

/*
   获得住店客人明细报表
 */
export const hotelGuestDetailReportApi = (data: any) => api.get('/admin-api/pms/report/hotel-guest', { params: data })
/*
   获得经理综合月报表
 */
export const managerMonthReportApi = (data: any) => api.get('/admin-api/report/manager-daily/list', { params: data })
/*
   获得酒店经营科目汇总月报
 */
export const subjectSummaryReportApi = (data: any) => api.get('/admin-api/pms/report/subject-summary', { params: data })
/*
   获得开房记录明细报表
 */
export const hotelCheckInDetailReportApi = (data: any) => api.get('/admin-api/report/hotel/checkin/list', { params: data })
/*
   获得客房续住明细报表
 */
export const hotelCheckInContinueDetailReportApi = (data: any) => api.get('/admin-api/report/hotel/checkin/continue/list', { params: data })
/*
   酒店综合统计月报表(固化)
 */
export const managerMonthStatisticsReportApi = (data: any) => api.get('/admin-api/report/manager-month/month/list', { params: data })
/*
   冲账调账明细报表
 */
export const redAndAdjustReportApi = (data: any) => api.get('/admin-api/pms/report/red-adjust', { params: data })
/*
   零房费&负房费报表
 */
export const zeroRoomRateReportApi = (data: any) => api.get('/admin-api/report/zero-room-rate/list', { params: data })
/*
   转账报表
 */
export const transferAccountReportApi = (data: any) => api.get('/admin-api/pms/report/transfer', { params: data })
/*
   拆账报表
 */
export const splitAccountReportApi = (data: any) => api.get('/admin-api/pms/report/split', { params: data })
/*
   人工修改房价明细报表
 */
export const changePriceReportApi = (data: any) => api.get('/admin-api/pms/report/change/price', { params: data })
/*
   获得夜审房价预审报表
 */
export const nightPricePrequalifyReportApi = (data: any) => api.get('/admin-api/pms/report/night-prequalify', { params: data })
/*
   获得今日预抵客人报表
 */
export const bookGuestReportApi = (data: any) => api.get('/admin-api/pms/report/book', { params: data })
/*
   客房营收统计报表
 */
export const roomStatApi = (data: any) => api.get('/admin-api/report/room/stat/list', { params: data })
/*
   获得经理报表(单日)
 */
export const managerApi = (data: any) => api.get('/admin-api/report/manager-daily/get/daily', { params: data })
/*
   生成营业日报表
 */
export const generateBusinessDailyReport = (data: any) => api.get('/admin-api/report/manager-daily/generate/business/daily', { params: data })
/*
   获得换房及升级记录列表
 */
export const getRoomChangeUpgradeReportList = (data: any) => api.get('/admin-api/report/room-change-upgrade/list', { params: data })
/*
  获得消费(结账)收回明细报表
*/
export const getConsumeRecoveryDetailReport = (data: any) => api.get('/admin-api/pms/report/consume-recovery', { params: data })
/*
  获得付款(结账)收回明细报表
*/
export const getPaymentRecoveryDetailReport = (data: any) => api.get('/admin-api/pms/report/payment-recovery', { params: data })
/*
   获得房类预测报表
 */
export const getRoomClassPredictionReport = (data: any) => api.get('/admin-api/pms/report/room-class-prediction', { params: data })
