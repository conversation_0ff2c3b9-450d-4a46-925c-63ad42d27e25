<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <CalculationMode>Interpretation</CalculationMode>
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="1">
      <lockLock Ref="2" type="Stimulsoft.Report.Dictionary.StiJsonDatabase" isKey="true">
        <Alias>lockLock</Alias>
        <HeadersString />
        <Key />
        <Name>lockLock</Name>
        <PathData>D:\workspace\qz\frontend\pms-report\public\reports\json\lockLock.json</PathData>
      </lockLock>
    </Databases>
    <DataSources isList="true" count="3">
      <root Ref="3" type="DataTableSource" isKey="true">
        <Alias>root</Alias>
        <Columns isList="true" count="3">
          <value>code,System.Decimal</value>
          <value>data,System.String</value>
          <value>msg,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>7f4d93b233664567bd31527741ab6b84</Key>
        <Name>root</Name>
        <NameInSource>lockLock.root</NameInSource>
      </root>
      <root_data Ref="4" type="DataTableSource" isKey="true">
        <Alias>root_data</Alias>
        <Columns isList="true" count="8">
          <value>hname,System.String</value>
          <value>hcode,System.String</value>
          <value>startDate,System.String</value>
          <value>endDate,System.String</value>
          <value>lastSelectTime,System.String</value>
          <value>operator,System.String</value>
          <value>list,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>89c1431fab714a4f824314ed2ba4137a</Key>
        <Name>root_data</Name>
        <NameInSource>lockLock.root_data</NameInSource>
      </root_data>
      <root_data_list Ref="5" type="DataTableSource" isKey="true">
        <Alias>root_data_list</Alias>
        <Columns isList="true" count="14">
          <value>id,System.Decimal</value>
          <value>gcode,System.String</value>
          <value>hcode,System.String</value>
          <value>orderNo,System.String</value>
          <value>name,System.String</value>
          <value>cardNo,System.String</value>
          <value>type,System.String</value>
          <value>typeName,System.String</value>
          <value>periodTime,System.String</value>
          <value>creator,System.String</value>
          <value>createTime,System.String</value>
          <value>rNo,System.String</value>
          <value>url,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>98c05016e364447daef0819f61de916a</Key>
        <Name>root_data_list</Name>
        <NameInSource>lockLock.root_data_list</NameInSource>
      </root_data_list>
    </DataSources>
    <Relations isList="true" count="2">
      <root Ref="6" type="DataRelation" isKey="true">
        <Alias>root</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="4" />
        <Dictionary isRef="1" />
        <Name>root</Name>
        <NameInSource>root_data</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>data</value>
        </ParentColumns>
        <ParentSource isRef="3" />
      </root>
      <root_data Ref="7" type="DataRelation" isKey="true">
        <Alias>root_data</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="5" />
        <Dictionary isRef="1" />
        <Name>root_data</Name>
        <NameInSource>root_data_list</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>list</value>
        </ParentColumns>
        <ParentSource isRef="4" />
      </root_data>
    </Relations>
    <Report isRef="0" />
    <Resources isList="true" count="1">
      <value>Name=test, Alias=test, Type=Json, Content=XxyQoEmRgQaI6LbklQ8KLS1qlI8h2d7HGvmPZXdMzqZ0es047_x002B_l_x002B_NNIY5uU3GUZLIWy5IeMqOmGNn7hcP_x002F_zWs7mY5lr_x002B_kq0HcryNGBJIZpzzvH9BSDqfOvRYLsQzch6JW_x002F_DdwytZlZj7o_x002F_pJfQDBP7_x002B_aMAXLmlnE8dz3EgyGoOogeR627aWHdw3gbo0jbjZH6mb4FAZzrrN1ur00QTB9eS9DOWzPeC9IesOsRVhifQ_x002B_DhUL6QVyTAxREUp525_x002B_wssnynolMQA7G3g4PpidATuk0iuRiZwszj_x002B_iRQB51JO6qt5rcUjerNKdBV_x002B_OQF2SJrqzr9D2y1YcwClLgkEuUip336CBHCWDLGG43YR8TutLCPtvHjoF2O_x002F_E40ltSv6Tnh6ShTxsZ91ZMnJCaCRoy8rhNoZu2hAYYmO_x002B_dcg3VPvgxynrsqv6xsJ0ibu_x002F_Xb6X2qM6W8O_x002F_q00RRqHf9p_x002B_DLGHGuxEiCE2AjnPUHxRRBopxuGxENQtyyCd60nJ5AdJbuSN5Ecn75t5vcMH7MMJ2IcqACD0r1Q3t1biyhyz7SPlS2TourbiKxFv3RVrGOVjggIlPb2CFr1oQuSFGOWk1eFwlEcYkz3XIvFSD_x002B_FCioF3a8mOYr6RT48n_x002B_jgOS0KBNcdCOxmxGTaAZlocA8dzpVRMfuaGxjJ_x002B_iVJslDLIQVc6sATMJxGeURdBKXiKmlNZFolEz8Qv_x002F_ZEulSUWuePYEmQuoWkvziHqfYxqbBjVZXMuJI4_x002B_u51bRkk_x002B_xNwLG6l_x002F_VKMBo8QB_x002B_W8yZST3L7RgtMdBPEDgEDnVsTV9ZN97y5lC5WjUdW4jmCNKEF9l_x002F_b4MEu_x002F_jR6BtIcmuKu3QitbiS1MlbbsuzfrH5oii3a1G1g9WzIJrcudLvZPzTiRAHQvNIY02olfzkrMWzBb63_x002F_d1lgyQl9IA5r7plr0vGOAXuZ7InBqIEpCiTF99Ld7o_x002F_bf48txQ0YGkXnGo2TMdSqWmBGWh6DfXfOaKOeEwQswXxw_x002F_hXbTFGqXLZTYziHzhmOW7LflGebY8r3kijM2T382_x002F__x002B_cDEe8jj7rCKGraq_x002B_2bHkHPVceS7QlDE8nYaHOV6Y9qnKZstj7XKyIcZYpuY2JeN1bq9DDydkVsz1DlMhfHB2Nu2RjSAVCR6_x002B_vlLJ61dgYoFxgkv3ft3hSWAGRsGUW5cD3eLw6W67tCXRd_x002F_h5lcIfHlo9MQpz0il4HrDtQtb7VzEXaX8YyFWw_x002B_VA35YGWdmTX5wEjYn7_x002F_wIgqp7CwBy6tocvU1AkKJwQW0LnbReYGx87d_x002F_BGE2_x002B_MhsM0Xvq_x002B_xmp8ns0VV5fq8s96bBdIvgkMabBJcFEpZ8W8prrvDCgyJRqQ6jvdmdvSmEMQfpFPnfhbMwkLb1Wm6hGuDPmup0hXxuTHjRqLGbO9aY4zNJMi3Y6Qdkt3oyU8SxT6ntJBp_x002F_2HeXnjk9cGTocPR48HkSfhYhZGtOLPoLwGpPQmWS7dOc_x002F_Gq_x002B_LeaGfIQS8bRub_x002F_tc5qJt5nUYbC42EdyTNR5CKYX5nvnJu3t2B_x002F_s8GfgKRSswoSdYjmfcXuVdPXO_x002F_0PcNnRjobXKILfJJ9km1dhr4jj_x002B_wRz66KfbgPx3sp5Y_x002F_J91wtJdHjDlJAVvSDnqg5F07dvywH6pyvZgoIbuQmtHu9Qd5EgMH49i6DiUZ_x002F_yVfgCJ3Z_x002B_h0XLhO_x002F_</value>
    </Resources>
    <UserFunctions isList="true" count="0" />
    <Variables isList="true" count="1">
      <value>分类</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <Key>7f2309bf07224b2b89ae5b38956322af</Key>
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="8" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="5">
        <PageHeaderBand1 Ref="9" type="PageHeaderBand" isKey="true">
          <Border>Bottom;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,27,0.4</ClientRectangle>
          <Components isList="true" count="2">
            <Text2 Ref="10" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0.01,0,7,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Name>Text2</Name>
              <Page isRef="8" />
              <Parent isRef="9" />
              <Text>{root_data.hname}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text2>
            <Text6 Ref="11" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>21.4,0,5.6,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Name>Text6</Name>
              <Page isRef="8" />
              <Parent isRef="9" />
              <Text>第{PageNumber}页,共{TotalPageCount}页</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text6>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>PageHeaderBand1</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
        </PageHeaderBand1>
        <PageFooterBand1 Ref="12" type="PageFooterBand" isKey="true">
          <Border>Bottom;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>0,18.2,27,0.8</ClientRectangle>
          <Components isList="true" count="2">
            <Text45 Ref="13" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.2,2.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Name>Text45</Name>
              <Page isRef="8" />
              <Parent isRef="12" />
              <Text>{root_data.operator}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text45>
            <Text46 Ref="14" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>23.2,0.2,3.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Name>Text46</Name>
              <Page isRef="8" />
              <Parent isRef="12" />
              <Text>{root_data.lastSelectTime.Substring(0, 19).Replace("-", "/")}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text46>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>PageFooterBand1</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
        </PageFooterBand1>
        <ReportTitleBand1 Ref="15" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,1.6,27,2</ClientRectangle>
          <Components isList="true" count="2">
            <Text1 Ref="16" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>9.1,0.2,4.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,12,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text1</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>客房门卡操作明细报表</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text1>
            <Text5 Ref="17" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1,21.4,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Name>Text5</Name>
              <Page isRef="8" />
              <Parent isRef="15" />
              <Text>门店:{root_data.hname}  操作日期:{root_data.startDate.Substring(0, 10)} - {root_data.endDate.Substring(0, 10)}  最后查询时间：{root_data.lastSelectTime.Substring(0, 19).Replace("-", "/")}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text5>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>ReportTitleBand1</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
        </ReportTitleBand1>
        <HeaderBand1 Ref="18" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,4.4,27,0.8</ClientRectangle>
          <Components isList="true" count="8">
            <Text3 Ref="19" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,1.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text3</Name>
              <Page isRef="8" />
              <Parent isRef="18" />
              <Text>序号</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text3>
            <Text4 Ref="20" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.4,0,5.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text4</Name>
              <Page isRef="8" />
              <Parent isRef="18" />
              <Text>订单号
</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text4>
            <Text9 Ref="21" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13,0,4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Interaction Ref="22" type="Stimulsoft.Report.Components.StiInteraction" isKey="true">
                <SortingColumn>DataBand1.createTime</SortingColumn>
              </Interaction>
              <Name>Text9</Name>
              <Page isRef="8" />
              <Parent isRef="18" />
              <Text>操作时间
</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text9>
            <Text10 Ref="23" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>23.4,0,3.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text10</Name>
              <Page isRef="8" />
              <Parent isRef="18" />
              <Text>操作员</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text10>
            <文本1 Ref="24" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.8,0,2.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本1</Name>
              <Page isRef="8" />
              <Parent isRef="18" />
              <Text>房号</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本1>
            <Text12 Ref="25" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>10.2,0,2.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text12</Name>
              <Page isRef="8" />
              <Parent isRef="18" />
              <Text>姓名</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text12>
            <Text8 Ref="26" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>17,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Interaction Ref="27" type="Stimulsoft.Report.Components.StiInteraction" isKey="true">
                <SortingColumn>DataBand1.bizDate</SortingColumn>
              </Interaction>
              <Name>Text8</Name>
              <Page isRef="8" />
              <Parent isRef="18" />
              <Text>操作类型</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text8>
            <Text7 Ref="28" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>19.4,0,4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Interaction Ref="29" type="Stimulsoft.Report.Components.StiInteraction" isKey="true">
                <SortingColumn>DataBand1.createTime</SortingColumn>
              </Interaction>
              <Name>Text7</Name>
              <Page isRef="8" />
              <Parent isRef="18" />
              <Text>门卡有效期</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text7>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>HeaderBand1</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
        </HeaderBand1>
        <DataBand1 Ref="30" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,6,27,0.8</ClientRectangle>
          <Components isList="true" count="8">
            <Text47 Ref="31" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,1.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>Text47</Name>
              <Page isRef="8" />
              <Parent isRef="30" />
              <Text>{Line}</Text>
              <TextBrush>Black</TextBrush>
              <Type>SystemVariables</Type>
              <VertAlignment>Center</VertAlignment>
            </Text47>
            <Text15 Ref="32" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>1.8,0,2.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,0,0,0</Margins>
              <Name>Text15</Name>
              <Page isRef="8" />
              <Parent isRef="30" />
              <Text>{root_data_list.rNo}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text15>
            <Text17 Ref="33" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>17,0,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>Text17</Name>
              <Page isRef="8" />
              <Parent isRef="30" />
              <Text>{root_data_list.typeName}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="34" type="DateFormat" isKey="true">
                <StringFormat>G</StringFormat>
              </TextFormat>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text17>
            <Text18 Ref="35" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>23.4,0,3.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,0,0,0</Margins>
              <Name>Text18</Name>
              <Page isRef="8" />
              <Parent isRef="30" />
              <Text>{root_data_list.creator}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text18>
            <Text22 Ref="36" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>13,0,4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>Text22</Name>
              <Page isRef="8" />
              <Parent isRef="30" />
              <Text>{root_data_list.createTime}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="37" type="DateFormat" isKey="true">
                <StringFormat>G</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text22>
            <Text20 Ref="38" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>10.2,0,2.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,0,0,0</Margins>
              <Name>Text20</Name>
              <Page isRef="8" />
              <Parent isRef="30" />
              <Text>{root_data_list.name}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text20>
            <文本2 Ref="39" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>4.4,0,5.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Hyperlink>{(root_data_list.url) ?? ""}</Hyperlink>
              <Margins>5,0,0,0</Margins>
              <Name>文本2</Name>
              <Page isRef="8" />
              <Parent isRef="30" />
              <Text>{root_data_list.orderNo}</Text>
              <TextBrush>[79:129:189]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本2>
            <Text21 Ref="40" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>19.4,0,4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,0,0,0</Margins>
              <Name>Text21</Name>
              <Page isRef="8" />
              <Parent isRef="30" />
              <Text>{root_data_list.periodTime}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="41" type="DateFormat" isKey="true">
                <StringFormat>g</StringFormat>
              </TextFormat>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text21>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>root_data_list</DataSourceName>
          <Expressions isList="true" count="0" />
          <Filters isList="true" count="0" />
          <Name>DataBand1</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
          <Sort isList="true" count="0" />
        </DataBand1>
      </Components>
      <Conditions isList="true" count="0" />
      <Expressions isList="true" count="0" />
      <Guid>695b2a39df6d4d1082fa7197665400f1</Guid>
      <Margins>1,1,1,1</Margins>
      <Name>Page1</Name>
      <Orientation>Landscape</Orientation>
      <PageHeight>21</PageHeight>
      <PageWidth>29</PageWidth>
      <Report isRef="0" />
    </Page1>
  </Pages>
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>Report</ReportAlias>
  <ReportAuthor>Z zx</ReportAuthor>
  <ReportChanged>7/11/2025 3:55:12 PM</ReportChanged>
  <ReportCreated>7/5/2024 7:27:55 PM</ReportCreated>
  <ReportFile>D:\workspace\qz\frontend\pms-report\public\reports\lockLogReport.mrt</ReportFile>
  <ReportGuid>2a4494c251934123993fc303b3b526b5</ReportGuid>
  <ReportName>Report</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2025.1.1.0</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
		#endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>