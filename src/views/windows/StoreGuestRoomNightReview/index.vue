<i18n>
{
  "zh": {
    "search": {
      "today": "今天",
      "thisWeek": "本周",
      "thisMonth": "本月",
      "bizDate": "营业日",
      "selectBizDate": "选择日期",
      "store": "门店",
      "selectStore": "请选择门店",
      "buildingFloor": "楼栋楼层",
      "selectBuildingFloor": "请选择楼栋楼层",
      "roomType": "房型",
      "selectRoomType": "请选择房型",
      "roomState": "房态",
      "selectRoomState": "请选择房态",
      "operator": "操作员",
      "selectOperator": "请选择",
      "query": "查询"
    }
  },
  "en": {
    "search": {
      "today": "Today",
      "thisWeek": "This Week",
      "thisMonth": "This Month",
      "bizDate": "Biz Date",
      "selectBizDate": "Select Date",
      "store": "Store",
      "selectStore": "Select Store",
      "buildingFloor": "Building/Floor",
      "selectBuildingFloor": "Select Building/Floor",
      "roomType": "Type",
      "selectRoomType": "Select Room Type",
      "roomState": "State",
      "selectRoomState": "Select Room State",
      "operator": "Operator",
      "selectOperator": "Select",
      "query": "Search"
    }
  },
  "km": {
    "search": {
      "today": "ថ្ងៃនេះ",
      "thisWeek": "សប្តាហ៍នេះ",
      "thisMonth": "ខែនេះ",
      "bizDate": "កាលបរិច្ឆេទអាជីវកម្ម",
      "selectBizDate": "ជ្រើសរើសកាលបរិច្ឆេទ",
      "store": "ហាង",
      "selectStore": "សូមជ្រើសរើសហាង",
      "buildingFloor": "អគារ/ជាន់",
      "selectBuildingFloor": "សូមជ្រើសរើសអគារ/ជាន់",
      "roomType": "ប្រភេទបន្ទប់",
      "selectRoomType": "សូមជ្រើសរើសប្រភេទបន្ទប់",
      "roomState": "ស្ថានភាពបន្ទប់",
      "selectRoomState": "សូមជ្រើសរើសស្ថានភាពបន្ទប់",
      "operator": "ប្រតិបត្តិករ",
      "selectOperator": "សូមជ្រើសរើស",
      "query": "ស្វែងរក"
    }
  }
}
</i18n>

<script setup lang="ts">
import { dayjs } from 'element-plus'
import { useI18n } from 'vue-i18n'
import { buildFloorTreeApi, getDictDataBatch, nightAudiRoomTypeApi, roomTypeSimpleListApi, simpleListApi } from '@/api/modules/pms/shiftTimeApi.ts'
import useUserStore from '@/store/modules/user.ts'
import { ReportUtils } from '@/utils/report-utils.ts'

const userStore = useUserStore()
const { t } = useI18n()
const route = useRoute()

const data = ref({
  searchFold: false,
  // 表格是否自适应高度
  tableAutoHeight: true,
  labelState: '1',
  buildingFloorCodes: [] as string[],
  search: {
    gcode: userStore.gcode,
    hcode: userStore.hcode,
    bizDate: dayjs().subtract(1, 'day').toDate(), // 设置为前一天
    rtCode: '',
    states: [] as string[],
  },
})

/** 门店列表 */
const simpleList = ref<{ hcode: string, hname: string }[]>([])
/** 房间状态 */
const roomStates = ref<{ code: string, label: string }[]>([])
/** 房型列表 */
const rts = ref<{ rtCode: string, rtName: string }[]>([])
/** 楼栋楼层 */
const buildingFloors = ref<any[]>([])
const buildingfloorProps = {
  label: 'name',
  value: 'code',
  checkStrictly: true,
}

// 监听门店变化，重新查询相关数据
watch(() => data.value.search.hcode, (newHcode) => {
  if (newHcode) {
    // 清空已选数据
    data.value.search.rtCode = ''
    data.value.buildingFloorCodes = []
    data.value.search.states = []

    // 重新加载数据
    getRts()
    getBuildingFloors()
  }
})
const loading = ref(false)
function onSearch() {
  loading.value = true
  let buildingCode = ''
  let floorCode = ''
  if (data.value.buildingFloorCodes.length === 2) {
    buildingCode = data.value.buildingFloorCodes[0]
    floorCode = data.value.buildingFloorCodes[1]
  }
  else if (data.value.buildingFloorCodes.length === 1) {
    buildingCode = data.value.buildingFloorCodes[0]
    floorCode = ''
  }
  nightAudiRoomTypeApi({
    ...data.value.search,
    states: data.value.search.states.toString(),
    bizDate: dayjs(data.value.search.bizDate).format('YYYY-MM-DD'),
    buildingCode,
    floorCode,
  }).then((res) => {
    nextTick(() => {
      setJson(res)
      loading.value = false
    })
  }).catch(() => {
    loading.value = false
  })
}

async function setJson(json: any) {
  const viewer = ReportUtils.getViewer('GuestRoomStiViewer')
  const report = ReportUtils.getReportObj('reports/NightAudiRoomTypeReport.mrt', json)

  ReportUtils.loadData(viewer, report, 'StoreGuestRoomNightReviewReport')
}

const dictTypes = ['room_status']
async function getConstants() {
  await getDictDataBatch(dictTypes).then((res: any) => {
    roomStates.value = res.data.filter((item: any) => item.dictType === 'room_status')
  })
}
function getRts() {
  const params = {
    gcode: userStore.gcode,
    hcode: data.value.search.hcode,
    isVirtual: '0',
    isEnable: '1',
  }
  roomTypeSimpleListApi(params).then((res: any) => {
    if (res.code === 0) {
      rts.value = res.data
    }
  })
}
async function getsimpleList() {
  const params = {
    gcode: userStore.gcode,
    hcode: data.value.search.hcode,
  }
  await simpleListApi(params).then((res) => {
    simpleList.value = res.data
  })
}
async function getBuildingFloors() {
  buildFloorTreeApi({ gcode: userStore.gcode, hcode: data.value.search.hcode }).then((res) => {
    buildingFloors.value = res.data
  })
}

async function getQueryParams() {
  const queryParams = route.query || {}

  if (!queryParams || JSON.stringify(queryParams) === '{}') {
    return
  }
  if (!queryParams.name || queryParams.name !== 'StoreGuestRoomNightReview') {
    return
  }

  data.value.search.hcode = String(queryParams.hcode) || userStore.hcode
  data.value.search.bizDate = queryParams.bizDate || Date.now()
  if (queryParams.roomStatus) {
    data.value.search.states = String(queryParams.roomStatus).split(',')
  }
}

onMounted(async () => {
  await getQueryParams()
  await getRts()
  await getConstants()
  await getsimpleList()
  await getBuildingFloors()
  onSearch()
})

/** 禁止当天时间之后的日期 */
function disabledDate(time: Date) {
  return dayjs(time.getTime()).isAfter(dayjs().subtract(1, 'day'))
}
</script>

<template>
  <div :class="{ 'absolute-container': data.tableAutoHeight }">
    <page-main>
      <search-bar :fold="data.searchFold" :show-toggle="false">
        <el-form :model="data.search" size="default" label-width="80px" inline-message inline class="search-form">
          <el-form-item :label="t('search.bizDate')" style="width: 360px;">
            <el-date-picker
              v-model="data.search.bizDate" type="date" :placeholder="t('search.selectBizDate')" style="margin-left: 10px;"
              :clearable="false" :disabled-date="disabledDate"
            />
          </el-form-item>
          <el-form-item :label="t('search.store')" style="width: 300px;">
            <el-select v-model="data.search.hcode" :placeholder="t('search.selectStore')">
              <el-option v-for="item in simpleList" :key="item.hcode" :label="item.hname" :value="item.hcode" />
            </el-select>
          </el-form-item>
          <el-form-item :label="t('search.buildingFloor')" style="width: 300px;">
            <el-cascader
              v-model="data.buildingFloorCodes" :options="buildingFloors" :placeholder="t('search.selectBuildingFloor')"
              :props="buildingfloorProps" clearable
            />
          </el-form-item>
          <el-form-item :label="t('search.roomType')" style="width: 300px;">
            <el-select v-model="data.search.rtCode" :placeholder="t('search.selectRoomType')" style="width: 180px;" clearable>
              <el-option v-for="item in rts" :key="item.rtCode" :label="item.rtName" :value="item.rtCode" />
            </el-select>
          </el-form-item>
          <el-form-item :label="t('search.roomState')" style="width: 300px;">
            <el-select v-model="data.search.states" multiple :placeholder="t('search.selectRoomState')" clearable>
              <el-option v-for="item in roomStates" :key="item.code" :label="item.label" :value="item.code" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSearch">
              {{ t('search.query') }}
            </el-button>
          </el-form-item>
        </el-form>
      </search-bar>
      <div id="StoreGuestRoomNightReviewReport">
        <el-skeleton :loading="loading" animated :rows="30" />
      </div>
    </page-main>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-radio) {
  margin-right: 14px;
}
</style>
