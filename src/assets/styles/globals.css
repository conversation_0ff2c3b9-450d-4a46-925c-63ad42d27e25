/* 页面布局 CSS 变量 */
:root {
  /* 头部高度 */
  --g-header-height: 60px;

  /* 侧边栏宽度 */
  --g-main-sidebar-width: 80px;
  --g-sub-sidebar-width: 220px;
  --g-sub-sidebar-collapse-width: 90px;

  /* 侧边栏Logo高度 */
  --g-sidebar-logo-height: 50px;

  /* 顶栏高度 */
  --g-topbar-height: 50px;

  /* 窗口高度（仅在窗口预览时使用） */
  --g-window-height: 800px;

  /* 窗口预览缩放系数 */
  --g-window-perview-scale: 0.5;
}

/* 明暗模式 CSS 变量 */
/* stylelint-disable-next-line no-duplicate-selectors */
:root {
  --g-box-shadow-color: rgb(0 0 0 / 12%);

  &::view-transition-old(root),
  &::view-transition-new(root) {
    mix-blend-mode: normal;
    animation: none;
  }

  &::view-transition-old(root) {
    z-index: 1;
  }

  &::view-transition-new(root) {
    z-index: 9999;
  }

  &.dark {
    --g-box-shadow-color: rgb(0 0 0 / 72%);

    &::view-transition-old(root) {
      z-index: 9999;
    }

    &::view-transition-new(root) {
      z-index: 1;
    }
  }
}

::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

::-webkit-scrollbar-thumb {
  background-color: rgb(0 0 0 / 40%);
  background-clip: padding-box;
  border: 3px solid transparent;
  border-radius: 6px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgb(0 0 0 / 50%);
}

::-webkit-scrollbar-track {
  background-color: transparent;
}

html,
body {
  height: 100%;
}

body {
  box-sizing: border-box;
  margin: 0;
  font-family: Lato, "PingFang SC", "Microsoft YaHei", sans-serif;
  background-color: var(--g-app-bg);
  -webkit-tap-highlight-color: transparent;

  &.overflow-hidden {
    overflow: hidden;
  }
}

* {
  box-sizing: inherit;
}

/* 右侧内容区针对fixed元素，有横向铺满的需求，可在fixed元素上设置 [data-fixed-calc-width] */
[data-fixed-calc-width] {
  position: fixed;
  inset-inline: 50% 0;
  width: calc(100% - var(--g-main-sidebar-actual-width) - var(--g-sub-sidebar-actual-width));
  transform: translateX(-50%) translateX(calc(var(--g-main-sidebar-actual-width) / 2)) translateX(calc(var(--g-sub-sidebar-actual-width) / 2));

  [dir="rtl"] & {
    transform: translateX(50%) translateX(calc(var(--g-main-sidebar-actual-width) / 2 * -1)) translateX(calc(var(--g-sub-sidebar-actual-width) / 2 * -1));
  }
}

/* textarea 字体跟随系统 */
textarea {
  font-family: inherit;
}

/* Overrides Floating Vue */
.v-popper--theme-dropdown,
.v-popper--theme-tooltip {
  --uno: inline-flex;
}

.v-popper--theme-dropdown .v-popper__inner,
.v-popper--theme-tooltip .v-popper__inner {
  --uno: bg-white dark-bg-stone-8 text-dark dark-text-white rounded shadow ring-1 ring-gray-200 dark-ring-gray-800 border border-solid border-stone/20 text-xs font-normal;

  box-shadow: 0 6px 30px rgb(0 0 0 / 10%);
}

.v-popper--theme-tooltip .v-popper__arrow-inner,
.v-popper--theme-dropdown .v-popper__arrow-inner {
  visibility: visible;

  --uno: border-white dark-border-stone-8;
}

.v-popper--theme-tooltip .v-popper__arrow-outer,
.v-popper--theme-dropdown .v-popper__arrow-outer {
  --uno: border-stone/20;
}

.v-popper--theme-tooltip.v-popper--shown,
.v-popper--theme-tooltip.v-popper--shown * {
  transition: none !important;
}

[data-overlayscrollbars-contents] {
  overscroll-behavior: contain;
}

/* medium-zoom */
.medium-zoom-overlay,
.medium-zoom-image {
  z-index: 3000;
}
