<i18n>
{
  "zh": {
    "search": {
      "today": "今天",
      "thisWeek": "本周",
      "thisMonth": "本月",
      "bizDate": "营业日",
      "selectBizDate": "选择日期",
      "startPlaceholder": "开始日期",
      "endPlaceholder": "结束日期",
      "rangeSeparator": "至",
      "store": "门店",
      "selectStore": "请选择门店",
      "buildingFloor": "楼栋楼层",
      "selectBuildingFloor": "请选择楼栋楼层",
      "roomType": "房型",
      "selectRoomType": "请选择房型",
      "roomState": "房态",
      "selectRoomState": "请选择房态",
      "operator": "操作员",
      "selectOperator": "请选择",
      "orderSource": "订单来源",
      "selectOrderSource": "请选择订单来源",
      "keyword": "关键字",
      "keywordPlaceholder": "输入完整的姓名/手机号",
      "stayType": "住店类型",
      "all": "全部",
      "inStore": "本店",
      "crossStore": "跨店",
      "selfOnly": "只看本人本卡",
      "query": "查询"
    },
    "constants": {
      "shortcuts": {
        "today": "今天",
        "thisWeek": "本周",
        "thisMonth": "本月"
      }
    }
  },
  "en": {
    "search": {
      "today": "Today",
      "thisWeek": "This Week",
      "thisMonth": "This Month",
      "bizDate": "Biz Date",
      "selectBizDate": "Select Date",
      "startPlaceholder": "Start Date",
      "endPlaceholder": "End Date",
      "rangeSeparator": "to",
      "store": "Store",
      "selectStore": "Select Store",
      "buildingFloor": "Building/Floor",
      "selectBuildingFloor": "Select Building/Floor",
      "roomType": "Room Type",
      "selectRoomType": "Select Room Type",
      "roomState": "Room State",
      "selectRoomState": "Select Room State",
      "operator": "Operator",
      "selectOperator": "Select",
      "orderSource": "Order Source",
      "selectOrderSource": "Select Order Source",
      "keyword": "Keyword",
      "keywordPlaceholder": "Enter full name/phone number",
      "stayType": "Stay Type",
      "all": "All",
      "inStore": "In-store",
      "crossStore": "Cross-store",
      "selfOnly": "Self Only",
      "query": "Search"
    },
    "constants": {
      "shortcuts": {
        "today": "Today",
        "thisWeek": "This Week",
        "thisMonth": "This Month"
      }
    }
  },
  "km": {
    "search": {
      "today": "ថ្ងៃនេះ",
      "thisWeek": "សប្តាហ៍នេះ",
      "thisMonth": "ខែនេះ",
      "bizDate": "កាលបរិច្ឆេទអាជីវកម្ម",
      "selectBizDate": "ជ្រើសរើសកាលបរិច្ឆេទ",
      "startPlaceholder": "កាលបរិច្ឆេទចាប់ផ្តើម",
      "endPlaceholder": "កាលបរិច្ឆេទបញ្ចប់",
      "rangeSeparator": "ទៅ",
      "store": "ហាង",
      "selectStore": "សូមជ្រើសរើសហាង",
      "buildingFloor": "អាគារ/ជាន់",
      "selectBuildingFloor": "សូមជ្រើសរើសអាគារ/ជាន់",
      "roomType": "ប្រភេទបន្ទប់",
      "selectRoomType": "សូមជ្រើសរើសប្រភេទបន្ទប់",
      "roomState": "ស្ថានភាពបន្ទប់",
      "selectRoomState": "សូមជ្រើសរើសស្ថានភាពបន្ទប់",
      "operator": "ប្រតិបត្តិករ",
      "selectOperator": "សូមជ្រើសរើស",
      "orderSource": "ប្រភពបញ្ជាទិញ",
      "selectOrderSource": "សូមជ្រើសរើសប្រភពបញ្ជាទិញ",
      "keyword": "ពាក្យគន្លឹះ",
      "keywordPlaceholder": "បញ្ចូលឈ្មោះពេញ/លេខទូរស័ព្ទ",
      "stayType": "ប្រភេទស្នាក់នៅ",
      "all": "ទាំងអស់",
      "inStore": "នៅក្នុងហាង",
      "crossStore": "ឆ្លងហាង",
      "selfOnly": "មានតែខ្លួនឯង",
      "query": "ស្វែងរក"
    },
    "constants": {
      "shortcuts": {
        "today": "ថ្ងៃនេះ",
        "thisWeek": "សប្តាហ៍នេះ",
        "thisMonth": "ខែនេះ"
      }
    }
  }
}
</i18n>

<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { dayjs } from 'element-plus'
import { getDictDataBatch, getShiftTimeList, handoverReport } from '@/api/modules/pms/shiftTimeApi.ts'
import useUserStore from '@/store/modules/user.ts'
import { getFirstDayOfMonth, getMonday } from '@/utils/tool'
import { ReportUtils } from '@/utils/report-utils.ts'

const userStore = useUserStore()
const { t } = useI18n()

const data = ref({
  searchFold: false,
  // 表格是否自适应高度
  tableAutoHeight: true,
  labelState: '1',
  search: {
    gcode: userStore.gcode,
    hcode: userStore.hcode,
    bizDate: [dayjs().subtract(30, 'day').toDate(), dayjs().subtract(1, 'day').toDate()] as any, // 起始时间为30天前，结束时间为前一天
    shiftCode: [] as string[],
    orderSource: '',
    name: '',
    radio: '0',
    checked: false,
  },
})

const storeList = ref([])
const storeProps = { multiple: true }
const shortcuts = [
  {
    text: t('constants.shortcuts.today'),
    value: () => {
      const end = new Date()
      const start = new Date()
      return [start, end]
    },
  },
  {
    text: t('constants.shortcuts.thisWeek'),
    value: () => {
      const end = new Date()
      const start = getMonday()
      return [start, end]
    },
  },
  {
    text: t('constants.shortcuts.thisMonth'),
    value: () => {
      const end = new Date()
      const start = getFirstDayOfMonth()
      return [start, end]
    },
  },
]
/** 订单来源列表 */
const orderSources = ref<{ code: string, label: string }[]>([])
const loading = ref(false)
function onSearch() {
  loading.value = true
  handoverReport({
    gcode: userStore.gcode,
    hcode: userStore.hcode,
    // bizDate: dayjs(data.value.search.bizDate).format('YYYY-MM-DD'),
  }).then((res) => {
    nextTick(() => {
      setJson(res)
      loading.value = false
    })
  }).catch(() => {
    loading.value = false
  })
}

async function setJson(json: any) {
  const viewer = ReportUtils.getViewer('StiViewer')
  const report = ReportUtils.getReportObj('reports/handoverReport.mrt', json)

  ReportUtils.loadData(viewer, report, 'report')
}
/**
 * 交班列表
 */
const shifts = ref<{ shiftCode: string, shiftName: string }[]>([])
async function getShiftTimeData() {
  await getShiftTimeList({ gcode: userStore.gcode, hcode: userStore.hcode, state: '1' }).then((res) => {
    shifts.value = res.data
  })
}

const dictTypes = ['order_source']
async function getConstants() {
  await getDictDataBatch(dictTypes).then((res: any) => {
    orderSources.value = res.data.filter((item: any) => item.dictType === 'order_source')
  })
}

onMounted(async () => {
  await getShiftTimeData()
  getConstants()
  onSearch()
})
</script>

<template>
  <div :class="{ 'absolute-container': data.tableAutoHeight }">
    <page-main>
      <search-bar :fold="data.searchFold" :show-toggle="false">
        <el-form :model="data.search" size="default" label-width="80px" inline-message inline class="search-form">
          <el-form-item :label="t('search.bizDate')" style="width: 360px;">
            <el-date-picker
              v-model="data.search.bizDate" type="daterange" :range-separator="t('search.rangeSeparator')"
              :start-placeholder="t('search.startPlaceholder')"
              :end-placeholder="t('search.endPlaceholder')"
              :shortcuts="shortcuts" :clearable="false"
            />
          </el-form-item>
          <el-form-item :label="t('search.store')" style="width: 300px;">
            <el-cascader
              v-model="data.search.shiftCode" :options="storeList" :placeholder="t('search.selectStore')" :props="storeProps"
              clearable
            />
          </el-form-item>
          <el-form-item :label="t('search.orderSource')" style="width: 300px;">
            <el-select v-model="data.search.orderSource" :placeholder="t('search.selectOrderSource')">
              <el-option v-for="item in orderSources" :key="item.code" :label="item.label" :value="item.code" />
            </el-select>
          </el-form-item>
          <el-form-item :label="t('search.keyword')" style="width: 300px;">
            <el-input v-model="data.search.name" :placeholder="t('search.keywordPlaceholder')" />
          </el-form-item>
          <el-form-item :label="t('search.stayType')">
            <el-radio-group v-model="data.search.radio">
              <el-radio value="0">
                {{ t('search.all') }}
              </el-radio>
              <el-radio value="1">
                {{ t('search.inStore') }}
              </el-radio>
              <el-radio value="2">
                {{ t('search.crossStore') }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item>
            <el-checkbox v-model="data.search.checked" :label="t('search.selfOnly')" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSearch">
              {{ t('search.query') }}
            </el-button>
          </el-form-item>
        </el-form>
      </search-bar>
      <div id="report">
        <el-skeleton :loading="loading" animated :rows="30" />
      </div>
    </page-main>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-radio) {
  margin-right: 14px;
}
</style>
