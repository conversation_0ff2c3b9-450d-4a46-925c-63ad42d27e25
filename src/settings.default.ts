// 该文件为系统默认配置，请勿修改！！！
const globalSettingsDefault: RecursiveRequired<Settings.all> = {
  app: {
    lightTheme: 'classic',
    darkTheme: 'dark',
    colorScheme: 'light',
    enableMournMode: false,
    enableColorAmblyopiaMode: false,
    defaultLang: '',
    enablePermission: false,
    storagePrefix: 'osa_',
    enableWatermark: false,
    enableErrorLog: false,
    direction: 'ltr',
  },
  userPreferences: {
    enable: false,
    storageTo: 'local',
  },
  menu: {
    baseOn: 'frontend',
    mode: 'side',
    style: '',
    isRounded: false,
    switchMainMenuAndOpenWindow: false,
    subMenuUniqueOpened: true,
    subMenuCollapse: false,
    subMenuAutoCollapse: false,
    enableSubMenuCollapseButton: false,
    enableHotkeys: false,
  },
  favorites: {
    storageTo: 'local',
  },
  toolbar: {
    dashboard: true,
    previewWindows: true,
    favorites: false,
    navSearch: true,
    notification: false,
    i18n: false,
    fullscreen: false,
    colorScheme: false,
    layout: ['dashboard', 'previewWindows', 'favorites', '->', 'navSearch', 'notification', 'i18n', 'fullscreen', 'colorScheme'],
  },
  navSearch: {
    enableHotkeys: true,
  },
  window: {
    defaultWidth: 1300,
    autoPosition: '',
    focusMaxNum: 4,
    enableHotkeys: true,
  },
  copyright: {
    enable: false,
    dates: '',
    company: '',
    website: '',
    beian: '',
  },
}

export default globalSettingsDefault
