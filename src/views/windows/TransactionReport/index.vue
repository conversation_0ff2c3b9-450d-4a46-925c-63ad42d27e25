<i18n>
{
  "zh": {
    "search": {
      "store": "门店",
      "bizDate": "营业日",
      "timeType": "时间类型",
      "query": "查询",
      "selectStore": "请选择门店",
      "startDate": "开始日期",
      "endDate": "结束日期",
      "to": "至",
      "today": "今天",
      "thisWeek": "本周",
      "thisMonth": "本月",
      "tradingTime": "交易时间",
      "fee": "交易金额",
      "keyWords": "查询条件",
      "searchPlaceholder": "输入订单号、姓名、房号、交易单号查询"
    }
  },
  "en": {
    "search": {
      "store": "Store",
      "bizDate": "Biz Date",
      "timeType": "Time Type",
      "query": "Search",
      "selectStore": "Select Store",
      "startDate": "Start Date",
      "endDate": "End Date",
      "to": "to",
      "today": "Today",
      "thisWeek": "This Week",
      "thisMonth": "This Month",
      "tradingTime": "Trading Time",
      "fee": "Fee",
      "keyWords": "Search Conditions",
      "searchPlaceholder": "Search by order number, name, room number, or transaction number"
    }
  },
  "km": {
    "search": {
      "store": "ហាង",
      "bizDate": "កាលបរិច្ឆេទអាជីវកម្ម",
      "timeType": "ប្រភេទពេលវេលា",
      "query": "ស្វែងរក",
      "selectStore": "សូមជ្រើសរើសហាង",
      "startDate": "កាលបរិច្ឆេទចាប់ផ្តើម",
      "endDate": "កាលបរិច្ឆេទបញ្ចប់",
      "to": "ទៅ",
      "today": "ថ្ងៃនេះ",
      "thisWeek": "សប្តាហ៍នេះ",
      "thisMonth": "ខែនេះ",
      "tradingTime": "ពេលវេលាធ្វើជំនួញ",
      "fee": "ចំនួនទឹកប្រាក់",
      "keyWords": "លក្ខខណ្ឌស្វែងរក",
      "searchPlaceholder": "ស្វែងរកតាមលេខបញ្ជាទិញ ឈ្មោះ លេខបន្ទប់ ឬលេខប្រតិបត្តិការ"
    }
  }
}
</i18n>

<script setup lang="ts">
import { dayjs } from 'element-plus'
import { useI18n } from 'vue-i18n'
import {
  simpleListApi,
  transactionReportReportApi,
} from '@/api/modules/pms/shiftTimeApi.ts'
import useUserStore from '@/store/modules/user.ts'
import { getFirstDayOfMonth, getMonday } from '@/utils/tool'
import { ReportUtils } from '@/utils/report-utils.ts'

const userStore = useUserStore()
const { t } = useI18n()
const route = useRoute()

const data = ref({
  searchFold: false,
  // 表格是否自适应高度
  tableAutoHeight: true,
  labelState: '1',
  bizDate: [dayjs().subtract(dayjs().daysInMonth(), 'day').toDate(), dayjs().toDate()] as any, // 起始时间为当月默认天数，结束时间为当天
  search: {
    gcode: userStore.gcode,
    hcode: userStore.hcode,
    timeType: '1',
    keyWords: '',
    fee: null,
  },
})

const shortcuts = [
  {
    text: t('search.today'),
    value: () => {
      const end = new Date()
      const start = new Date()
      return [start, end]
    },
  },
  {
    text: t('search.thisWeek'),
    value: () => {
      const end = new Date()
      const start = getMonday()
      return [start, end]
    },
  },
  {
    text: t('search.thisMonth'),
    value: () => {
      const end = new Date()
      const start = getFirstDayOfMonth()
      return [start, end]
    },
  },
]

const timeList = [
  { code: '1', label: t('search.bizDate') },
  { code: '2', label: t('search.tradingTime') },
]
const simpleList = ref<{ hcode: string, hname: string }[]>([])
const loading = ref(false)
function onSearch() {
  loading.value = true
  const timeData = {
    startDate: dayjs(data.value.bizDate[0]).format(data.value.search.timeType === '1' ? 'YYYY-MM-DD' : 'YYYY-MM-DD HH:mm:ss'),
    endDate: dayjs(data.value.bizDate[1]).format(data.value.search.timeType === '1' ? 'YYYY-MM-DD' : 'YYYY-MM-DD HH:mm:ss'),
  }
  transactionReportReportApi({
    ...data.value.search,
    fee: data.value.search.fee === '' || data.value.search.fee === undefined ? null : data.value.search.fee, // 为空时设置为 0
    ...timeData,
  }).then((res) => {
    nextTick(() => {
      setJson(res)
      loading.value = false
    })
  }).catch(() => {
    loading.value = false
  })
}

async function setJson(json: any) {
  const viewer = ReportUtils.getViewer('TransactionReportStiViewer')
  const report = ReportUtils.getReportObj('reports/transactionReport.mrt', json)

  ReportUtils.loadData(viewer, report, 'TransactionReport')
}

function onChangeTime() { }

async function getsimpleList() {
  const params = {
    gcode: userStore.gcode,
    hcode: userStore.hcode,
  }
  await simpleListApi(params).then((res) => {
    simpleList.value = res.data
  })
}

async function getQueryParams() {
  const queryParams = route.query || {}
  if (!queryParams || JSON.stringify(queryParams) === '{}') {
    return
  }
  if (!queryParams.name || queryParams.name != 'StoreCashierPayment') {
    return
  }

  // 开始结束时间
  if (queryParams.startDate && queryParams.endDate) {
    data.value.bizDate = [queryParams.startDate, queryParams.endDate]
  }
  if (queryParams.hcode) {
    data.value.search.hcode = String(queryParams.hcode)
  }
}

onMounted(async () => {
  await getQueryParams()
  await getsimpleList()
  onSearch()
})
// -----------最多只能选择一个月，且只能选昨天之前的-----------------
const startVal = ref(null)
/** 选择第一个时间的当月天数 */
const daysInMonth = ref(0)
function calendarChange(e) {
  if (e[0] && !e[1]) {
    startVal.value = e[0]
    daysInMonth.value = dayjs(e[0]).daysInMonth()
  }
}
/** 选择第一个时间 */
function visibleChange(e) {
  if (e) {
    startVal.value = null
  }
}
/** 禁止选择一个上下自然月之外时间 */
function disabledDate(time: Date) {
  const flag_yestodayAfter = dayjs(time).isAfter(dayjs().subtract(0, 'day'), 'day')
  if (!startVal.value) {
    return flag_yestodayAfter
  }
  const range = [dayjs(startVal.value).subtract(daysInMonth.value, 'day'), dayjs(startVal.value).add(daysInMonth.value, 'day')]
  const cur = dayjs(time)
  return cur < range[0] || cur > range[1] || flag_yestodayAfter
}
// -----------最多只能选择一个月，且只能选今天(包含)之前的-----------------
</script>

<template>
  <div :class="{ 'absolute-container': data.tableAutoHeight }">
    <page-main>
      <search-bar :fold="data.searchFold" :show-toggle="false">
        <el-form :model="data.search" size="default" label-width="80px" inline-message inline class="search-form">
          <el-form-item :label="t('search.store')" style="width: 260px;">
            <el-select v-model="data.search.hcode" :placeholder="t('search.selectStore')">
              <el-option v-for="item in simpleList" :key="item.hcode" :label="item.hname" :value="item.hcode" />
            </el-select>
          </el-form-item>
          <el-form-item :label="t('search.keyWords')" style="width: 400px;">
            <el-input v-model="data.search.keyWords" :placeholder="t('search.searchPlaceholder')" />
          </el-form-item>
          <el-form-item :label="t('search.fee')" style="width: 400px;">
            <el-input-number v-model="data.search.fee" :placeholder="t('search.fee')" />
          </el-form-item>
          <el-form-item>
            <el-select
              v-model="data.search.timeType" style="display: inline-block;width: 100px;"
              @change="onChangeTime"
            >
              <el-option v-for="item in timeList" :key="item.code" :label="item.label" :value="item.code" />
            </el-select>
            <el-date-picker
              v-if="data.search.timeType === '1'" v-model="data.bizDate" type="daterange"
              :range-separator="t('search.to')" :start-placeholder="t('search.startDate')" :end-placeholder="t('search.endDate')" :shortcuts="shortcuts"
              :clearable="false"
              :disabled-date="disabledDate"
              @calendar-change="calendarChange"
              @visible-change="visibleChange"
            />
            <el-date-picker
              v-else v-model="data.bizDate" type="datetimerange" :range-separator="t('search.to')"
              :start-placeholder="t('search.startDate')" :end-placeholder="t('search.endDate')" :shortcuts="shortcuts" :clearable="false"
            />
            <el-button type="primary" @click="onSearch">
              {{ t('search.query') }}
            </el-button>
          </el-form-item>
        </el-form>
      </search-bar>
      <div id="TransactionReport">
        <el-skeleton :loading="loading" animated :rows="30" />
      </div>
    </page-main>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-radio) {
  margin-right: 14px;
}
</style>
