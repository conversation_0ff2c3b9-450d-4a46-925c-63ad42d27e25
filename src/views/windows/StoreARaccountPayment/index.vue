<i18n>
{
  "en": {
    "search": {
      "today": "Today",
      "thisWeek": "This Week",
      "thisMonth": "This Month",
      "thisQuarter": "This Quarter",
      "thisYear": "This Year",
      "bizDate": "Biz Date",
      "store": "Store",
      "arAccount": "Account",
      "operator": "Operator",
      "search": "Search",
      "selectDate": "Select Date",
      "selectStore": "Please select a store",
      "selectAccount": "Please select AR Account",
      "selectOperator": "Please select",
      "startDate": "Start Date",
      "endDate": "End Date",
      "to": "to"
    }
  },
  "zh": {
    "search": {
      "today": "今天",
      "thisWeek": "本周",
      "thisMonth": "本月",
      "thisQuarter": "本季",
      "thisYear": "本年",
      "bizDate": "营业日",
      "store": "门店",
      "arAccount": "AR账户",
      "operator": "操作员",
      "search": "查询",
      "selectDate": "选择日期",
      "selectStore": "请选择门店",
      "selectAccount": "请选择AR账户",
      "selectOperator": "请选择",
      "startDate": "开始日期",
      "endDate": "结束日期",
      "to": "至"
    }
  },
  "km": {
    "search": {
      "today": "ថ្ងៃនេះ",
      "thisWeek": "សប្តាហ៍នេះ",
      "thisMonth": "ខែនេះ",
      "thisQuarter": "ត្រីមាសនេះ",
      "thisYear": "ឆ្នាំនេះ",
      "bizDate": "កាលបរិច្ឆេទអាជីវកម្ម",
      "store": "ហាង",
      "arAccount": "គណនី AR",
      "operator": "ប្រតិបត្តិករ",
      "search": "ស្វែងរក",
      "selectDate": "ជ្រើសរើសកាលបរិច្ឆេទ",
      "selectStore": "សូមជ្រើសរើសហាង",
      "selectAccount": "សូមជ្រើសរើសគណនី AR",
      "selectOperator": "សូមជ្រើសរើស",
      "startDate": "កាលបរិច្ឆេទចាប់ផ្តើម",
      "endDate": "កាលបរិច្ឆេទបញ្ចប់",
      "to": "ទៅ"
    }
  }
}
</i18n>

<script setup lang="ts">
import { dayjs } from 'element-plus'
import { useI18n } from 'vue-i18n'
import { arSetListApi, arentryReportApi, simpleListApi, simpleUserListApi } from '@/api/modules/pms/shiftTimeApi.ts'
import useUserStore from '@/store/modules/user.ts'
import { getFirstDayOfMonth, getFirstDayOfQuarter, getFirstDayOfYear, getMonday } from '@/utils/tool'
import { ReportUtils } from '@/utils/report-utils.ts'

const userStore = useUserStore()
const { t } = useI18n()
const data = ref({
  searchFold: false,
  // 表格是否自适应高度
  tableAutoHeight: true,
  bizDate: [dayjs().subtract(dayjs().daysInMonth(), 'day').toDate(), dayjs().toDate()] as any, // 起始时间为当月默认天数，结束时间为当天
  search: {
    gcode: userStore.gcode,
    hcode: userStore.hcode,
    arSetCodes: [],
    operator: '',
  },
})
const simpleList = ref<{ hcode: string, hname: string }[]>([])
const arSetDataList = ref<{ arSetCode: string, arSetName: string }[]>([])
const operators = ref<{ username: string, nickname: string }[]>([])

// 监听门店变化，重新查询AR账户和操作员
watch(() => data.value.search.hcode, (newHcode) => {
  if (newHcode) {
    // 切换门店时清空已选择的AR账户和操作员
    data.value.search.arSetCodes = []
    data.value.search.operator = ''

    // 重新加载AR账户和操作员数据
    getArSetList()
    getOperatorList()
  }
})

const shortcuts = [
  {
    text: t('search.today'),
    value: () => {
      const end = new Date()
      const start = new Date()
      return [start, end]
    },
  },
  {
    text: t('search.thisWeek'),
    value: () => {
      const end = new Date()
      const start = getMonday()
      return [start, end]
    },
  },
  {
    text: t('search.thisMonth'),
    value: () => {
      const end = new Date()
      const start = getFirstDayOfMonth()
      return [start, end]
    },
  },
  {
    text: t('search.thisQuarter'),
    value: () => {
      const end = new Date()
      const start = getFirstDayOfQuarter()
      return [start, end]
    },
  },
  {
    text: t('search.thisYear'),
    value: () => {
      const end = new Date()
      const start = getFirstDayOfYear()
      return [start, end]
    },
  },
]
const loading = ref(false)
function onSearch() {
  loading.value = true
  arentryReportApi({
    gcode: userStore.gcode,
    hcode: data.value.search.hcode,
    arSetCodes: Array.isArray(data.value.search.arSetCodes) ? data.value.search.arSetCodes.join(',') : data.value.search.arSetCodes, // 处理多选值
    operator: data.value.search.operator,
    startDate: dayjs(data.value.bizDate[0]).format('YYYY-MM-DD'),
    endDate: dayjs(data.value.bizDate[1]).format('YYYY-MM-DD'),
  }).then((res) => {
    nextTick(() => {
      setJson(res)
      loading.value = false
    })
  }).catch(() => {
    loading.value = false
  })
}

async function setJson(json: any) {
  const viewer = ReportUtils.getViewer('StoreARaccountHappenStiViewer')
  const report = ReportUtils.getReportObj('reports/arEntyReport.mrt', json)
  ReportUtils.loadData(viewer, report, 'ARaccountPaymentStiVieweReport')
}

async function getsimpleList() {
  const params = {
    gcode: userStore.gcode,
    hcode: data.value.search.hcode,
  }
  await simpleListApi(params).then((res) => {
    simpleList.value = res.data
  })
}
function getArSetList() {
  const params = {
    gcode: userStore.gcode,
    hcode: data.value.search.hcode,
    isEnable: '1',
  }
  arSetListApi(params).then((res: any) => {
    arSetDataList.value = res.data
  })
}
async function getOperatorList() {
  const params = {
    gcode: userStore.gcode,
    hcode: data.value.search.hcode,
  }
  await simpleUserListApi(params).then((res) => {
    operators.value = res.data
    data.value.search.operator = res.data && res.data.length > 0 ? res.data[0].username : ''
  })
}

onMounted(async () => {
  await getsimpleList()
  await getArSetList()
  await getOperatorList()
  onSearch()
})
// -----------最多只能选择一个月，且只能选昨天之前的-----------------
const startVal = ref(null)
/** 选择第一个时间的当月天数 */
const daysInMonth = ref(0)
function calendarChange(e) {
  if (e[0] && !e[1]) {
    startVal.value = e[0]
    daysInMonth.value = dayjs(e[0]).daysInMonth()
  }
}
/** 选择第一个时间 */
function visibleChange(e) {
  if (e) {
    startVal.value = null
  }
}
/** 禁止选择一个上下自然月之外时间 */
function disabledDate(time: Date) {
  const flag_yestodayAfter = dayjs(time).isAfter(dayjs().subtract(0, 'day'), 'day')
  if (!startVal.value) {
    return flag_yestodayAfter
  }
  const range = [dayjs(startVal.value).subtract(daysInMonth.value, 'day'), dayjs(startVal.value).add(daysInMonth.value, 'day')]
  const cur = dayjs(time)
  return cur < range[0] || cur > range[1] || flag_yestodayAfter
}
// -----------最多只能选择一个月，且只能选今天(包含)之前的-----------------
</script>

<template>
  <div :class="{ 'absolute-container': data.tableAutoHeight }">
    <page-main>
      <search-bar :fold="data.searchFold" :show-toggle="false">
        <el-form :model="data.search" size="default" label-width="80px" inline-message inline class="search-form">
          <el-form-item :label="t('search.bizDate')" style="width: 360px;">
            <el-date-picker
              v-model="data.bizDate" type="daterange" :range-separator="t('search.to')"
              :start-placeholder="t('search.startDate')"
              :end-placeholder="t('search.endDate')" :shortcuts="shortcuts" :clearable="false"
              :disabled-date="disabledDate"
              @calendar-change="calendarChange"
              @visible-change="visibleChange"
            />
          </el-form-item>
          <el-form-item :label="t('search.store')" style="width: 300px;">
            <el-select v-model="data.search.hcode" :placeholder="t('search.selectStore')">
              <el-option v-for="item in simpleList" :key="item.hcode" :label="item.hname" :value="item.hcode" />
            </el-select>
          </el-form-item>
          <el-form-item :label="t('search.arAccount')" style="width: 300px;">
            <el-select
              v-model="data.search.arSetCodes" :placeholder="t('search.selectAccount')" clearable multiple
              collapse-tags
              collapse-tags-tooltip
              style="width: 100%;"
            >
              <el-option
                v-for="item in arSetDataList" :key="item.arSetCode" :label="item.arSetName"
                :value="item.arSetCode"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="t('search.operator')" style="width: 260px;">
            <el-select v-model="data.search.operator" :placeholder="t('search.selectOperator')" style="width: 150px;" clearable>
              <el-option v-for="item in operators" :key="item.username" :label="item.nickname" :value="item.username" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSearch">
              {{ t('search.search') }}
            </el-button>
          </el-form-item>
        </el-form>
      </search-bar>
      <div id="ARaccountPaymentStiVieweReport">
        <el-skeleton :loading="loading" animated :rows="30" />
      </div>
    </page-main>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-radio) {
  margin-right: 14px;
}
</style>
