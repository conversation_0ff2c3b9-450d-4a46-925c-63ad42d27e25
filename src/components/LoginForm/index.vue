<i18n>
{
  "zh-cn": {
    "accountLogin": "账号密码登录",
    "qrcodeLogin": "扫码登录",
    "intro": "欢迎使用 {title} ! 👋🏻",
    "remember": "记住我",
    "forget": "忘记密码了?",
    "noAccount": "还没有帐号?",
    "register": "注册新帐号",
    "wechatQrcode": "请使用微信扫码登录",
    "testLogin": "演示账号一键登录",
    "form": {
      "account": "用户名",
      "password": "密码",
      "login": "登录"
    },
    "rules": {
      "account": "请输入用户名",
      "password": "请输入密码",
      "passwordLength": "密码长度为6到18位"
    }
  },
  "zh-tw": {
    "accountLogin": "帳號密碼登入",
    "qrcodeLogin": "掃碼登入",
    "intro": "歡迎使用 {title} ! 👋🏻",
    "remember": "記住我",
    "forget": "忘記密碼了?",
    "noAccount": "還沒有帳號?",
    "register": "註冊新帳號",
    "wechatQrcode": "請使用微信掃碼登入",
    "testLogin": "演示帳號一键登入",
    "form": {
      "account": "用戶名",
      "password": "密碼",
      "login": "登入"
    },
    "rules": {
      "account": "請輸入用戶名",
      "password": "請輸入密碼",
      "passwordLength": "密碼長度為6到18位"
    }
  },
  "en": {
    "accountLogin": "Account Login",
    "qrcodeLogin": "Scan Login",
    "intro": "Welcome to {title} ! 👋🏻",
    "remember": "Remember Me",
    "forget": "Forget Password?",
    "noAccount": "No Account?",
    "register": "Register New Account",
    "wechatQrcode": "Please use WeChat scan login",
    "testLogin": "Demo Account One-click Login",
    "form": {
      "account": "Account",
      "password": "Password",
      "login": "Login"
    },
    "rules": {
      "account": "Please enter the account",
      "password": "Please enter the password",
      "passwordLength": "Password length is 6 to 18 bits"
    }
  }
}
</i18n>

<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'
import { useI18n } from 'vue-i18n'
import useUserStore from '@/store/modules/user'
import storage from '@/utils/storage'

defineOptions({
  name: 'LoginForm',
})

const props = defineProps<{
  account?: string
}>()

const emits = defineEmits<{
  onLogin: [account: string]
  onRegister: [account: string]
  onResetPassword: [account: string]
}>()

const { t } = useI18n()
const userStore = useUserStore()

const title = import.meta.env.VITE_APP_TITLE
const loading = ref(false)

// 登录方式，default 账号密码登录，qrcode 扫码登录
const type = ref('default')
const formRef = ref<FormInstance>()
const form = ref({
  account: props.account ?? storage.local.get('login_account') ?? '',
  password: '',
  remember: storage.local.has('login_account'),
})
const rules = ref<FormRules>({
  account: [
    { required: true, trigger: 'blur', message: () => t('rules.account') },
  ],
  password: [
    { required: true, trigger: 'blur', message: () => t('rules.password') },
    { min: 6, max: 18, trigger: 'blur', message: () => t('rules.passwordLength') },
  ],
})
function handleLogin() {
  formRef.value?.validate((valid) => {
    if (valid) {
      loading.value = true
      userStore.login(form.value).then(() => {
        if (form.value.remember) {
          storage.local.set('login_account', form.value.account)
        }
        else {
          storage.local.remove('login_account')
        }
        emits('onLogin', form.value.account)
      }).finally(() => {
        loading.value = false
      })
    }
  })
}

function testAccount(account: string) {
  form.value.account = account
  form.value.password = '123456'
  handleLogin()
}
</script>

<template>
  <ElForm ref="formRef" :model="form" :rules="rules" class="min-h-500px w-full flex-col-stretch-center p-12">
    <div class="mb-6">
      <HTabList
        v-model="type" :options="[
          { label: t('accountLogin'), value: 'default' },
          { label: t('qrcodeLogin'), value: 'qrcode' },
        ]"
      />
    </div>
    <template v-if="type === 'default'">
      <h3 class="mb-8 text-xl color-[var(--el-text-color-primary)] font-bold">
        {{ t('intro', { title }) }}
      </h3>
      <div>
        <ElFormItem prop="account">
          <ElInput v-model="form.account" :placeholder="t('form.account')" type="text" tabindex="1">
            <template #prefix>
              <SvgIcon name="i-ri:user-3-fill" />
            </template>
          </ElInput>
        </ElFormItem>
        <ElFormItem prop="password">
          <ElInput v-model="form.password" type="password" :placeholder="t('form.password')" tabindex="2" show-password @keyup.enter="handleLogin">
            <template #prefix>
              <SvgIcon name="i-ri:lock-2-fill" />
            </template>
          </ElInput>
        </ElFormItem>
      </div>
      <div class="mb-4 flex-center-between">
        <ElCheckbox v-model="form.remember">
          {{ t('remember') }}
        </ElCheckbox>
        <ElLink type="primary" :underline="false" @click="emits('onResetPassword', form.account)">
          {{ t('forget') }}
        </ElLink>
      </div>
      <ElButton :loading="loading" type="primary" size="large" style="width: 100%;" @click.prevent="handleLogin">
        {{ t('form.login') }}
      </ElButton>
      <div class="mt-4 flex-center gap-2 text-sm color-[var(--el-text-color-secondary)]">
        {{ t('noAccount') }}
        <ElLink type="primary" :underline="false" @click="emits('onRegister', form.account)">
          {{ t('register') }}
        </ElLink>
      </div>
    </template>
    <template v-else-if="type === 'qrcode'">
      <div class="flex-col-center">
        <el-image src="https://s2.loli.net/2024/04/26/GsahtuIZ9XOg5jr.png" class="h-[250px] w-[250px]" />
        <div class="mt-2 op-50">
          {{ t('wechatQrcode') }}
        </div>
      </div>
    </template>
    <div class="mt-4 text-center -mb-4">
      <ElDivider>{{ t('testLogin') }}</ElDivider>
      <ElButton type="primary" size="small" plain @click="testAccount('admin')">
        admin
      </ElButton>
      <ElButton size="small" plain @click="testAccount('test')">
        test
      </ElButton>
    </div>
  </ElForm>
</template>

<style scoped>
:deep(input[type="password"]::-ms-reveal) {
  display: none;
}

.el-form-item {
  margin-bottom: 24px;

  :deep(.el-input) {
    width: 100%;
    height: 48px;
    line-height: inherit;

    input {
      height: 48px;
    }

    .el-input__prefix,
    .el-input__suffix {
      display: flex;
      align-items: center;
    }

    .el-input__prefix {
      left: 10px;
    }

    .el-input__suffix {
      right: 10px;
    }
  }
}

:deep(.el-divider__text) {
  white-space: nowrap;
  background-color: var(--g-container-bg);
}
</style>
