<i18n>
{
  "zh": {
    "search": {
      "store": "门店",
      "bizDate": "营业日",
      "timeType": "时间类型",
      "statChannel": "统计渠道",
      "guestSource": "客源",
      "consumeSubject": "消费科目",
      "operator": "操作员",
      "shift": "班次",
      "query": "查询",
      "selectStore": "请选择门店",
      "selectChannel": "请选择统计渠道",
      "selectGuestSource": "请选择客源",
      "selectSubject": "请选择消费科目",
      "selectOperator": "请选择操作员",
      "selectShift": "请选择班次",
      "startDate": "开始日期",
      "endDate": "结束日期",
      "to": "至",
      "today": "今天",
      "thisWeek": "本周",
      "thisMonth": "本月",
      "thisQuarter": "本季",
      "thisYear": "本年",
      "operationTime": "操作时间",
      "corporateName": "公司名称",
      "arAccount": "AR账户",
      "selectCorporateName": "请选择公司名称",
      "selectARAccount": "请选择AR账户",
      "accountType": "账户类型",
      "creditAccount": "信用账户",
      "prepaidAccount": "预付账户",
      "all": "全部",
      "idNo": "订单号",
      "partialWriteOff": "部分核销",
      "shortcuts": {
        "today": "今天",
        "thisWeek": "本周",
        "thisMonth": "本月",
        "thisQuarter": "本季",
        "thisYear": "本年"
      }
    }
  },
  "en": {
    "search": {
      "store": "Store",
      "bizDate": "Biz Date",
      "timeType": "Time Type",
      "statChannel": "Stat Channel",
      "guestSource": "Guest Source",
      "consumeSubject": "Consume Subject",
      "operator": "Operator",
      "shift": "Shift",
      "query": "Search",
      "selectStore": "Select Store",
      "selectChannel": "Select Stat Channel",
      "selectGuestSource": "Select Guest Source",
      "selectSubject": "Select Consume Subject",
      "selectOperator": "Select Operator",
      "selectShift": "Select Shift",
      "startDate": "Start Date",
      "endDate": "End Date",
      "to": "to",
      "today": "Today",
      "thisWeek": "This Week",
      "thisMonth": "This Month",
      "thisQuarter": "This Quarter",
      "thisYear": "This Year",
      "operationTime": "Operation Time",
      "corporateName": "Company",
      "arAccount": "Account",
      "selectCorporateName": "Select Company",
      "selectARAccount": "Select AR Account",
      "accountType": "Type",
      "creditAccount": "Credit Account",
      "prepaidAccount": "Prepaid Account",
      "all": "All",
      "idNo": "Order No",
      "partialWriteOff": "Partial Write-off",
      "shortcuts": {
        "today": "Today",
        "thisWeek": "This Week",
        "thisMonth": "This Month",
        "thisQuarter": "This Quarter",
        "thisYear": "This Year"
      }
    }
  },
  "km": {
    "search": {
      "store": "ហាង",
      "bizDate": "កាលបរិច្ឆេទអាជីវកម្ម",
      "timeType": "ប្រភេទពេលវេលា",
      "statChannel": "ឆានែលស្ថិតិ",
      "guestSource": "ប្រភពភ្ញៀវ",
      "consumeSubject": "ប្រធានបទចំណាយ",
      "operator": "ប្រតិបត្តិករ",
      "shift": "វេន",
      "query": "ស្វែងរក",
      "selectStore": "សូមជ្រើសរើសហាង",
      "selectChannel": "សូមជ្រើសរើសឆានែលស្ថិតិ",
      "selectGuestSource": "សូមជ្រើសរើសប្រភពភ្ញៀវ",
      "selectSubject": "សូមជ្រើសរើសប្រធានបទចំណាយ",
      "selectOperator": "សូមជ្រើសរើសប្រតិបត្តិករ",
      "selectShift": "សូមជ្រើសរើសវេន",
      "startDate": "កាលបរិច្ឆេទចាប់ផ្តើម",
      "endDate": "កាលបរិច្ឆេទបញ្ចប់",
      "to": "ទៅ",
      "today": "ថ្ងៃនេះ",
      "thisWeek": "សប្តាហ៍នេះ",
      "thisMonth": "ខែនេះ",
      "thisQuarter": "ត្រីមាសនេះ",
      "thisYear": "ឆ្នាំនេះ",
      "operationTime": "ពេលវេលាប្រតិបត្តិការ",
      "corporateName": "ឈ្មោះក្រុមហ៊ុន",
      "arAccount": "គណនី AR",
      "selectCorporateName": "សូមជ្រើសរើសឈ្មោះក្រុមហ៊ុន",
      "selectARAccount": "សូមជ្រើសរើសគណនី AR",
      "accountType": "ប្រភេទគណនី",
      "creditAccount": "គណនីឥណទាន",
      "prepaidAccount": "គណនីបង់មុន",
      "all": "ទាំងអស់",
      "idNo": "លេខកម្មង៍",
      "partialWriteOff": "ការលុបចោលដោយផ្នែក",
      "shortcuts": {
        "today": "ថ្ងៃនេះ",
        "thisWeek": "សប្តាហ៍នេះ",
        "thisMonth": "ខែនេះ",
        "thisQuarter": "ត្រីមាសនេះ",
        "thisYear": "ឆ្នាំនេះ"
      }
    }
  }
}
</i18n>

<script setup lang="ts">
import { dayjs } from 'element-plus'
import { useI18n } from 'vue-i18n'
import {
  arSellReportApi,
  arSetListApi,
  getShiftTimeList,
  protocolAgentListSimpleApi,
  simpleListApi,
} from '@/api/modules/pms/shiftTimeApi.ts'
import useUserStore from '@/store/modules/user.ts'
import { getFirstDayOfMonth, getFirstDayOfQuarter, getMonday } from '@/utils/tool'
import { ReportUtils } from '@/utils/report-utils.ts'

const userStore = useUserStore()
const { t } = useI18n()

const data = ref({
  searchFold: false,
  // 表格是否自适应高度
  tableAutoHeight: true,
  labelState: '1',
  bizDate: [dayjs().subtract(dayjs().daysInMonth(), 'day').toDate(), dayjs().toDate()] as any, // 起始时间为当月默认天数，结束时间为当天
  search: {
    gcode: userStore.gcode,
    hcode: userStore.hcode,
    shiftNo: [] as string[],
    paCode: '',
    arSetCode: '',
    radio: '',
    unitCode: '',
    orderNo: '',
    checked: false,
  },
})

const simpleList = ref<{ hcode: string, hname: string }[]>([])
const arSetDataList = ref<{ arSetCode: string, arSetName: string }[]>([])
const protocolAgentList = ref<{ paCode: string, paName: string }[]>([])

// 监听门店变化，重新查询AR账户和协议单位
watch(() => data.value.search.hcode, (newHcode) => {
  if (newHcode) {
    // 切换门店时清空已选择的数据
    data.value.search.arSetCode = ''
    data.value.search.paCode = ''

    // 重新加载数据
    getArSetList()
    getProtocolAgentListSimple()
  }
})

const shortcuts = [
  {
    text: t('search.shortcuts.today'),
    value: () => {
      const end = new Date()
      const start = new Date()
      return [start, end]
    },
  },
  {
    text: t('search.shortcuts.thisWeek'),
    value: () => {
      const end = new Date()
      const start = getMonday()
      return [start, end]
    },
  },
  {
    text: t('search.shortcuts.thisMonth'),
    value: () => {
      const end = new Date()
      const start = getFirstDayOfMonth()
      return [start, end]
    },
  },
  {
    text: t('search.shortcuts.thisQuarter'),
    value: () => {
      const end = new Date()
      const start = getFirstDayOfQuarter()
      return [start, end]
    },
  },
]
const loading = ref(false)
function onSearch() {
  loading.value = true
  arSellReportApi({
    gcode: userStore.gcode,
    hcode: data.value.search.hcode,
    arSetCode: data.value.search.arSetCode,
    unitCode: data.value.search.paCode,
    creditAccType: data.value.search.radio,
    orderNo: data.value.search.orderNo,
    startDate: dayjs(data.value.bizDate[0]).format('YYYY-MM-DD'),
    endDate: dayjs(data.value.bizDate[1]).format('YYYY-MM-DD'),
  }).then((res) => {
    nextTick(() => {
      setJson(res)
      loading.value = false
    })
  }).catch(() => {
    loading.value = false
  })
}

async function setJson(json: any) {
  const viewer = ReportUtils.getViewer('settleDetailStiViewer')
  const report = ReportUtils.getReportObj('reports/arReport.mrt', json)
  ReportUtils.loadData(viewer, report, 'StoreARaccountHappen')
}

async function getsimpleList() {
  const params = {
    gcode: userStore.gcode,
    hcode: data.value.search.hcode,
  }
  await simpleListApi(params).then((res) => {
    simpleList.value = res.data
  })
}

function getArSetList() {
  const params = {
    gcode: userStore.gcode,
    hcode: data.value.search.hcode,
    isEnable: '1',
  }
  arSetListApi(params).then((res: any) => {
    arSetDataList.value = res.data
  })
}

function getProtocolAgentListSimple() {
  const params = {
    gcode: userStore.gcode,
    hcode: data.value.search.hcode,
    isEnable: '1',
  }
  protocolAgentListSimpleApi(params).then((res: any) => {
    protocolAgentList.value = res.data
  })
}

/**
 * 交班列表
 */
const shifts = ref<{ shiftCode: string, shiftName: string }[]>([])
async function getShiftTimeData() {
  await getShiftTimeList({ gcode: userStore.gcode, hcode: data.value.search.hcode, state: '1' }).then((res) => {
    shifts.value = res.data
  })
}

onMounted(async () => {
  await getShiftTimeData()
  await getArSetList()
  await getProtocolAgentListSimple()
  await getsimpleList()
  onSearch()
})
// -----------最多只能选择一个月，且只能选昨天之前的-----------------
const startVal = ref(null)
/** 选择第一个时间的当月天数 */
const daysInMonth = ref(0)
function calendarChange(e) {
  if (e[0] && !e[1]) {
    startVal.value = e[0]
    daysInMonth.value = dayjs(e[0]).daysInMonth()
  }
}
/** 选择第一个时间 */
function visibleChange(e) {
  if (e) {
    startVal.value = null
  }
}
/** 禁止选择一个上下自然月之外时间 */
function disabledDate(time: Date) {
  const flag_yestodayAfter = dayjs(time).isAfter(dayjs().subtract(0, 'day'), 'day')
  if (!startVal.value) {
    return flag_yestodayAfter
  }
  const range = [dayjs(startVal.value).subtract(daysInMonth.value, 'day'), dayjs(startVal.value).add(daysInMonth.value, 'day')]
  const cur = dayjs(time)
  return cur < range[0] || cur > range[1] || flag_yestodayAfter
}
// -----------最多只能选择一个月，且只能选今天(包含)之前的-----------------
</script>

<template>
  <div :class="{ 'absolute-container': data.tableAutoHeight }">
    <page-main>
      <search-bar :fold="data.searchFold" :show-toggle="false">
        <el-form :model="data.search" size="default" label-width="80px" inline-message inline class="search-form">
          <!--          <el-form-item :label="t('search.bizDate')">
            <el-date-picker v-model="data.search.bizDate" type="date" :placeholder="t('search.selectDate')" style="margin-left: 10px;"
                            :shortcuts="shortcuts" :clearable="false" />
          </el-form-item> -->
          <el-form-item :label="t('search.bizDate')" style="width: 360px;">
            <el-date-picker
              v-model="data.bizDate" type="daterange" :range-separator="t('search.to')"
              :start-placeholder="t('search.startDate')"
              :end-placeholder="t('search.endDate')" :shortcuts="shortcuts" :clearable="false"
              :disabled-date="disabledDate"
              @calendar-change="calendarChange"
              @visible-change="visibleChange"
            />
          </el-form-item>
          <el-form-item :label="t('search.store')" style="width: 300px;">
            <el-select v-model="data.search.hcode" :placeholder="t('search.selectStore')">
              <el-option v-for="item in simpleList" :key="item.hcode" :label="item.hname" :value="item.hcode" />
            </el-select>
          </el-form-item>
          <el-form-item :label="t('search.corporateName')" style="width: 300px;">
            <el-select v-model="data.search.paCode" :placeholder="t('search.selectCorporateName')" clearable>
              <el-option v-for="item in protocolAgentList" :key="item.paCode" :label="item.paName" :value="item.paCode" />
            </el-select>
          </el-form-item>
          <el-form-item :label="t('search.arAccount')" style="width: 300px;">
            <el-select v-model="data.search.arSetCode" :placeholder="t('search.selectARAccount')" clearable>
              <el-option v-for="item in arSetDataList" :key="item.arSetCode" :label="item.arSetName" :value="item.arSetCode" />
            </el-select>
          </el-form-item>
          <el-form-item :label="t('search.accountType')">
            <el-radio-group v-model="data.search.radio">
              <el-radio value="">
                {{ t('search.all') }}
              </el-radio>
              <el-radio value="0">
                {{ t('search.creditAccount') }}
              </el-radio>
              <el-radio value="1">
                {{ t('search.prepaidAccount') }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item :label="t('search.idNo')" style="width: 300px;">
            <el-input v-model="data.search.orderNo" :placeholder="t('search.idNo')" clearable />
          </el-form-item>
          <!--          <el-form-item>
                      <el-checkbox v-model="data.search.checked" :label="t('search.partialWriteOff')" />
                    </el-form-item> -->
          <el-form-item>
            <el-button type="primary" @click="onSearch">
              {{ t('search.query') }}
            </el-button>
          </el-form-item>
        </el-form>
      </search-bar>
      <div id="StoreARaccountHappen" style="width: 1900px; height: 100%; overflow: auto; white-space: nowrap;">
        <el-skeleton :loading="loading" animated :rows="30" />
      </div>
    </page-main>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-radio) {
  margin-right: 14px;
}
</style>
