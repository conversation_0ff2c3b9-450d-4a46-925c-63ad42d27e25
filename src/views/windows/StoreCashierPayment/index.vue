<i18n>
{
  "zh": {
    "search": {
      "store": "门店",
      "bizDate": "营业日",
      "timeType": "时间类型",
      "statChannel": "统计渠道",
      "guestSource": "客源",
      "paySubject": "付款科目",
      "operator": "操作员",
      "shift": "班次",
      "currentRoomNo": "现房号",
      "currentRoomNoPlaceholder": "输入现房号",
      "originalRoomNo": "原发生房号",
      "originalRoomNoPlaceholder": "输入原发生房号",
      "query": "查询",
      "selectStore": "请选择门店",
      "selectChannel": "请选择统计渠道",
      "selectGuestSource": "请选择客源",
      "selectSubject": "请选择付款科目",
      "selectOperator": "请选择操作员",
      "selectShift": "请选择班次",
      "startDate": "开始日期",
      "endDate": "结束日期",
      "to": "至",
      "today": "今天",
      "thisWeek": "本周",
      "thisMonth": "本月",
      "operationTime": "操作时间"
    }
  },
  "en": {
    "search": {
      "store": "Store",
      "bizDate": "Biz Date",
      "timeType": "Time Type",
      "statChannel": "Channel",
      "guestSource": "Source",
      "paySubject": "Payment Subject",
      "operator": "Operator",
      "shift": "Shift",
      "currentRoomNo": "Room No",
      "currentRoomNoPlaceholder": "Enter Current Room No",
      "originalRoomNo": "Old Room",
      "originalRoomNoPlaceholder": "Enter Original Room No",
      "query": "Search",
      "selectStore": "Select Store",
      "selectChannel": "Select Channel",
      "selectGuestSource": "Select Guest Source",
      "selectSubject": "Select Payment Subject",
      "selectOperator": "Select Operator",
      "selectShift": "Select Shift",
      "startDate": "Start Date",
      "endDate": "End Date",
      "to": "to",
      "today": "Today",
      "thisWeek": "This Week",
      "thisMonth": "This Month",
      "operationTime": "Operation Time"
    }
  },
  "km": {
    "search": {
      "store": "ហាង",
      "bizDate": "កាលបរិច្ឆេទអាជីវកម្ម",
      "timeType": "ប្រភេទពេលវេលា",
      "statChannel": "ឆានែលស្ថិតិ",
      "guestSource": "ប្រភពភ្ញៀវ",
      "paySubject": "ប្រធានបទបង់ប្រាក់",
      "operator": "ប្រតិបត្តិករ",
      "shift": "វេន",
      "currentRoomNo": "លេខបន្ទប់បច្ចុប្បន្ន",
      "currentRoomNoPlaceholder": "បញ្ចូលលេខបន្ទប់បច្ចុប្បន្ន",
      "originalRoomNo": "លេខបន្ទប់ដើម",
      "originalRoomNoPlaceholder": "បញ្ចូលលេខបន្ទប់ដើម",
      "query": "ស្វែងរក",
      "selectStore": "សូមជ្រើសរើសហាង",
      "selectChannel": "សូមជ្រើសរើសឆានែលស្ថិតិ",
      "selectGuestSource": "សូមជ្រើសរើសប្រភពភ្ញៀវ",
      "selectSubject": "សូមជ្រើសរើសប្រធានបទបង់ប្រាក់",
      "selectOperator": "សូមជ្រើសរើសប្រតិបត្តិករ",
      "selectShift": "សូមជ្រើសរើសវេន",
      "startDate": "កាលបរិច្ឆេទចាប់ផ្តើម",
      "endDate": "កាលបរិច្ឆេទបញ្ចប់",
      "to": "ទៅ",
      "today": "ថ្ងៃនេះ",
      "thisWeek": "សប្តាហ៍នេះ",
      "thisMonth": "ខែនេះ",
      "operationTime": "ពេលវេលាប្រតិបត្តិការ"
    }
  }
}
</i18n>

<script setup lang="ts">
import { dayjs } from 'element-plus'
import { useI18n } from 'vue-i18n'
import {
  getDictDataBatch,
  getShiftTimeList,
  listChannelApi,
  listPayAccountrApi,
  payReportApi,
  simpleListApi,
  simpleUserListApi,
} from '@/api/modules/pms/shiftTimeApi.ts'
import useUserStore from '@/store/modules/user.ts'
import { getFirstDayOfMonth, getMonday } from '@/utils/tool'
import { ReportUtils } from '@/utils/report-utils.ts'

const userStore = useUserStore()
const { t } = useI18n()
const route = useRoute()

const data = ref({
  searchFold: false,
  // 表格是否自适应高度
  tableAutoHeight: true,
  labelState: '1',
  bizDate: [dayjs().subtract(0, 'day').toDate(), dayjs().toDate()] as any, // 起始时间为当月默认天数，结束时间为当天
  search: {
    gcode: userStore.gcode,
    hcode: userStore.hcode,
    timeType: '1',
    shiftNos: [] as string[],
    orderSource: undefined,
    orderSource2: undefined,
    guestSrcType: '', // 客源类型
    subCodes: [] as string[], // 消费科目
    operator: '', // 操作员
    statChannel: '',
  },
})

const shortcuts = [
  {
    text: t('search.today'),
    value: () => {
      const end = new Date()
      const start = new Date()
      return [start, end]
    },
  },
  {
    text: t('search.thisWeek'),
    value: () => {
      const end = new Date()
      const start = getMonday()
      return [start, end]
    },
  },
  {
    text: t('search.thisMonth'),
    value: () => {
      const end = new Date()
      const start = getFirstDayOfMonth()
      return [start, end]
    },
  },
]

const timeList = [
  { code: '1', label: t('search.bizDate') },
  { code: '2', label: t('search.operationTime') },
]
const orderSources = ref<{ code: string, label: string }[]>([])
const simpleList = ref<{ hcode: string, hname: string }[]>([])
const srcTypeList = ref<{ code: string, label: string }[]>([])
const subjectList = ref<{ code: string, name: string }[]>([])
const operators = ref<{ username: string, nickname: string }[]>([])
const shifts = ref<{ shiftCode: string, shiftName: string }[]>([])
/** 统计渠道 */
const statChannelList = ref<{ channelCode: string, channelName: string }[]>([])

// 监听门店变化，重新查询相关数据
watch(() => data.value.search.hcode, (newHcode) => {
  if (newHcode) {
    // 切换门店时清空已选择的数据
    data.value.search.operator = ''
    data.value.search.shiftNos = []
    data.value.search.statChannel = ''

    // 重新加载数据
    getOperatorList()
    getShiftTimeData()
    getStatChannelList()
  }
})
const loading = ref(false)
function onSearch() {
  loading.value = true
  const timeData = {
    startDate: dayjs(data.value.bizDate[0]).format(data.value.search.timeType === '1' ? 'YYYY-MM-DD' : 'YYYY-MM-DD HH:mm:ss'),
    endDate: dayjs(data.value.bizDate[1]).format(data.value.search.timeType === '1' ? 'YYYY-MM-DD' : 'YYYY-MM-DD HH:mm:ss'),
  }
  payReportApi({
    ...data.value.search,
    ...timeData,
  }).then((res) => {
    nextTick(() => {
      setJson(res)
      loading.value = false
    })
  }).catch(() => {
    loading.value = false
  })
}

async function setJson(json: any) {
  const viewer = ReportUtils.getViewer('CashierPaymentStiViewer')
  const report = ReportUtils.getReportObj('reports/payReport.mrt', json)

  ReportUtils.loadData(viewer, report, 'StoreCashierPaymentReport')
}

const dictTypes = ['order_source', 'guest_src_type']
async function getConstants() {
  await getDictDataBatch(dictTypes).then((res: any) => {
    orderSources.value = res.data.filter((item: any) => item.dictType === 'order_source')
    srcTypeList.value = res.data.filter((item: any) => item.dictType === 'guest_src_type')
  })
}
function onChangeTime() { }

async function getsimpleList() {
  const params = {
    gcode: userStore.gcode,
    hcode: data.value.search.hcode,
  }
  await simpleListApi(params).then((res) => {
    simpleList.value = res.data
  })
}

async function getPayAccountList() {
  const params = {
    gcode: userStore.gcode,
  }
  await listPayAccountrApi(params).then((res) => {
    subjectList.value = res.data
  })
}

async function getOperatorList() {
  const params = {
    gcode: userStore.gcode,
    hcode: data.value.search.hcode,
  }
  await simpleUserListApi(params).then((res) => {
    operators.value = res.data
    // data.value.search.operator = res.data && res.data.length > 0 ? res.data[0].username : ''
  })
}
async function getShiftTimeData() {
  await getShiftTimeList({ gcode: userStore.gcode, hcode: data.value.search.hcode, state: '1' }).then((res) => {
    shifts.value = res.data
  })
}
async function getStatChannelList() {
  await listChannelApi({
    gcode: userStore.gcode,
    hcode: data.value.search.hcode,
    isEnable: '1',
  }).then((res: any) => {
    statChannelList.value = res.data
  })
}

async function getQueryParams() {
  const queryParams = route.query || {}
  if (!queryParams || JSON.stringify(queryParams) === '{}') {
    return
  }
  if (!queryParams.name || queryParams.name != 'StoreCashierPayment') {
    return
  }

  // 开始结束时间
  if (queryParams.startDate && queryParams.endDate) {
    data.value.bizDate = [queryParams.startDate, queryParams.endDate]
  }
  if (queryParams.hcode) {
    data.value.search.hcode = String(queryParams.hcode)
  }
  if (queryParams.subCode) {
    data.value.search.subCodes.push(String(queryParams.subCode))
  }
  if (queryParams.operator) {
    data.value.search.operator = String(queryParams.operator)
  }
  if (queryParams.shiftNo && queryParams.shiftNo !== '') {
    data.value.search.shiftNos = String(queryParams.shiftNo).split(',')
  }
}

onMounted(async () => {
  await getQueryParams()
  await getShiftTimeData()
  await getConstants()
  await getsimpleList()
  await getOperatorList()
  await getStatChannelList()
  await getPayAccountList()
  onSearch()
})
// -----------最多只能选择一个月，且只能选昨天之前的-----------------
const startVal = ref(null)
/** 选择第一个时间的当月天数 */
const daysInMonth = ref(0)
function calendarChange(e) {
  if (e[0] && !e[1]) {
    startVal.value = e[0]
    daysInMonth.value = dayjs(e[0]).daysInMonth()
  }
}
/** 选择第一个时间 */
function visibleChange(e) {
  if (e) {
    startVal.value = null
  }
}
/** 禁止选择一个上下自然月之外时间 */
function disabledDate(time: Date) {
  const flag_yestodayAfter = dayjs(time).isAfter(dayjs().subtract(0, 'day'), 'day')
  if (!startVal.value) {
    return flag_yestodayAfter
  }
  const range = [dayjs(startVal.value).subtract(daysInMonth.value, 'day'), dayjs(startVal.value).add(daysInMonth.value, 'day')]
  const cur = dayjs(time)
  return cur < range[0] || cur > range[1] || flag_yestodayAfter
}
// -----------最多只能选择一个月，且只能选今天(包含)之前的-----------------
</script>

<template>
  <div :class="{ 'absolute-container': data.tableAutoHeight }">
    <page-main>
      <search-bar :fold="data.searchFold" :show-toggle="false">
        <el-form :model="data.search" size="default" label-width="80px" inline-message inline class="search-form">
          <el-form-item :label="t('search.store')" style="width: 260px;">
            <el-select v-model="data.search.hcode" :placeholder="t('search.selectStore')">
              <el-option v-for="item in simpleList" :key="item.hcode" :label="item.hname" :value="item.hcode" />
            </el-select>
          </el-form-item>
          <el-form-item :label="t('search.bizDate')">
            <el-select
              v-model="data.search.timeType" style="display: inline-block;width: 100px;"
              @change="onChangeTime"
            >
              <el-option v-for="item in timeList" :key="item.code" :label="item.label" :value="item.code" />
            </el-select>
            <el-date-picker
              v-if="data.search.timeType === '1'" v-model="data.bizDate" type="daterange"
              :range-separator="t('search.to')" :start-placeholder="t('search.startDate')" :end-placeholder="t('search.endDate')" :shortcuts="shortcuts"
              :clearable="false"
              :disabled-date="disabledDate"
              @calendar-change="calendarChange"
              @visible-change="visibleChange"
            />
            <el-date-picker
              v-else v-model="data.bizDate" type="datetimerange" :range-separator="t('search.to')"
              :start-placeholder="t('search.startDate')" :end-placeholder="t('search.endDate')" :shortcuts="shortcuts" :clearable="false"
            />
          </el-form-item>
          <el-form-item :label="t('search.statChannel')" style="width: 260px;">
            <el-select v-model="data.search.statChannel" :placeholder="t('search.selectChannel')" clearable>
              <el-option v-for="item in statChannelList" :key="item.channelCode" :label="item.channelName" :value="item.channelCode" />
            </el-select>
          </el-form-item>
          <el-form-item :label="t('search.guestSource')" style="width: 260px;">
            <el-select v-model="data.search.guestSrcType" :placeholder="t('search.selectGuestSource')" clearable>
              <el-option v-for="item in srcTypeList" :key="item.code" :label="item.label" :value="item.code" />
            </el-select>
          </el-form-item>
          <el-form-item :label="t('search.paySubject')" style="width: 260px;">
            <el-select v-model="data.search.subCodes" multiple style="width: 200px;" :placeholder="t('search.selectSubject')" clearable>
              <el-option v-for="item in subjectList" :key="item.code" :label="item.name" :value="item.code" />
            </el-select>
          </el-form-item>
          <el-form-item :label="t('search.operator')" style="width: 260px;">
            <el-select v-model="data.search.operator" :placeholder="t('search.selectOperator')" style="width: 150px;" clearable>
              <el-option v-for="item in operators" :key="item.username" :label="item.nickname" :value="item.username" />
            </el-select>
          </el-form-item>
          <el-form-item v-if="data.search.timeType === '1'" :label="t('search.shift')" style="width: 260px;">
            <el-select v-model="data.search.shiftNos" multiple :placeholder="t('search.selectShift')" style="width: 320px;" clearable>
              <el-option v-for="item in shifts" :key="item.shiftCode" :label="item.shiftName" :value="item.shiftCode" />
            </el-select>
          </el-form-item>
          <!--          <el-form-item :label="t('search.currentRoomNo')" style="width: 300px;">
            <el-input v-model="data.search.name" :placeholder="t('search.currentRoomNoPlaceholder')" />
          </el-form-item>
          <el-form-item :label="t('search.originalRoomNo')" label-width="100px" style="width: 300px;">
            <el-input v-model="data.search.name" :placeholder="t('search.originalRoomNoPlaceholder')" />
          </el-form-item> -->
          <el-form-item>
            <el-button type="primary" @click="onSearch">
              {{ t('search.query') }}
            </el-button>
          </el-form-item>
        </el-form>
      </search-bar>
      <div id="StoreCashierPaymentReport">
        <el-skeleton :loading="loading" animated :rows="30" />
      </div>
    </page-main>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-radio) {
  margin-right: 14px;
}
</style>
