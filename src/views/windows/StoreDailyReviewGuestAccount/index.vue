<i18n>
{
  "zh": {
    "search": {
      "store": "门店",
      "selectStore": "请选择门店",
      "bizDate": "营业日",
      "selectBizDate": "选择日期",
      "operator": "操作员",
      "selectOperator": "请选择",
      "query": "查询",
      "today": "今天",
      "thisWeek": "本周",
      "thisMonth": "本月",
      "operationTime": "操作时间"
    }
  },
  "en": {
    "search": {
      "store": "Store",
      "selectStore": "Select Store",
      "bizDate": "Biz Date",
      "selectBizDate": "Select Date",
      "operator": "Operator",
      "selectOperator": "Select",
      "query": "Search",
      "today": "Today",
      "thisWeek": "This Week",
      "thisMonth": "This Month",
      "operationTime": "Operation Time"
    }
  },
  "km": {
    "search": {
      "store": "ហាង",
      "selectStore": "សូមជ្រើសរើសហាង",
      "bizDate": "កាលបរិច្ឆេទអាជីវកម្ម",
      "selectBizDate": "ជ្រើសរើសកាលបរិច្ឆេទ",
      "operator": "ប្រតិបត្តិករ",
      "selectOperator": "សូមជ្រើសរើស",
      "query": "ស្វែងរក",
      "today": "ថ្ងៃនេះ",
      "thisWeek": "សប្តាហ៍នេះ",
      "thisMonth": "ខែនេះ",
      "operationTime": "ពេលវេលាប្រតិបត្តិការ"
    }
  }
}
</i18n>

<script setup lang="ts">
import { dayjs } from 'element-plus'
import { useI18n } from 'vue-i18n'
import { guestDetailReportApi, simpleListApi } from '@/api/modules/pms/shiftTimeApi.ts'
import useUserStore from '@/store/modules/user.ts'
import { getFirstDayOfMonth, getMonday } from '@/utils/tool'
import { ReportUtils } from '@/utils/report-utils.ts'

const userStore = useUserStore()
const { t } = useI18n()
const route = useRoute()

const data = ref({
  searchFold: false,
  // 表格是否自适应高度
  tableAutoHeight: true,
  labelState: '1',
  search: {
    gcode: userStore.gcode,
    hcode: userStore.hcode,
    bizDate: dayjs().subtract(1, 'day').toDate(), // 设置为前一天
  },
})

const shortcuts = [
  {
    text: t('search.today'),
    value: () => {
      const end = new Date()
      const start = new Date()
      return [start, end]
    },
  },
  {
    text: t('search.thisWeek'),
    value: () => {
      const end = new Date()
      const start = getMonday()
      return [start, end]
    },
  },
  {
    text: t('search.thisMonth'),
    value: () => {
      const end = new Date()
      const start = getFirstDayOfMonth()
      return [start, end]
    },
  },
]
const simpleList = ref<{ hcode: string, hname: string }[]>([])
const loading = ref(false)
function onSearch() {
  loading.value = true
  guestDetailReportApi({
    gcode: userStore.gcode,
    hcode: data.value.search.hcode,
    bizDate: dayjs(data.value.search.bizDate).format('YYYY-MM-DD'),
  }).then((res) => {
    nextTick(() => {
      setJson(res)
      loading.value = false
    })
  }).catch(() => {
    loading.value = false
  })
}

async function setJson(json: any) {
  const viewer = ReportUtils.getViewer('GuestAccountStiViewer')
  const report = ReportUtils.getReportObj('reports/guestDetailReport.mrt', json)

  ReportUtils.loadData(viewer, report, 'StoreDailyReviewGuestAccountReport')
}

async function getsimpleList() {
  const params = {
    gcode: userStore.gcode,
    hcode: userStore.hcode,
  }
  await simpleListApi(params).then((res) => {
    simpleList.value = res.data
  })
}

async function getQueryParams() {
  const queryParams = route.query || {}

  if (!queryParams || JSON.stringify(queryParams) === '{}') {
    return
  }
  if (!queryParams.name || queryParams.name != 'StoreDailyReviewGuestAccount') {
    return
  }

  data.value.search.hcode = String(queryParams.hcode) || userStore.hcode
  data.value.search.bizDate = queryParams.bizDate || Date.now()
}

onMounted(async () => {
  await getQueryParams()
  await getsimpleList()
  onSearch()
})
</script>

<template>
  <div :class="{ 'absolute-container': data.tableAutoHeight }">
    <page-main>
      <search-bar :fold="data.searchFold" :show-toggle="false">
        <el-form :model="data.search" size="default" label-width="80px" inline-message inline class="search-form">
          <el-form-item :label="t('search.store')" style="width: 260px;">
            <el-select v-model="data.search.hcode" :placeholder="t('search.selectStore')">
              <el-option v-for="item in simpleList" :key="item.hcode" :label="item.hname" :value="item.hcode" />
            </el-select>
          </el-form-item>
          <el-form-item :label="t('search.bizDate')">
            <el-date-picker
              v-model="data.search.bizDate" type="date" :placeholder="t('search.selectBizDate')" style="margin-left: 10px;"
              :shortcuts="shortcuts" :clearable="false"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSearch">
              {{ t('search.query') }}
            </el-button>
          </el-form-item>
        </el-form>
      </search-bar>
      <div id="StoreDailyReviewGuestAccountReport">
        <el-skeleton :loading="loading" animated :rows="30" />
      </div>
    </page-main>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-radio) {
  margin-right: 14px;
}
</style>
