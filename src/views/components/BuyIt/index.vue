<script setup lang="ts">
import { ElLoading } from 'element-plus'
import themes from '../../../../themes'
import eventBus from '@/utils/eventBus'
import useSettingsStore from '@/store/modules/settings'

defineOptions({
  name: 'BuyIt',
})

const isActived = ref(true)
setTimeout(() => {
  isActived.value = false
}, 5000)
const settingsStore = useSettingsStore()

onMounted(() => {
  eventBus.on('global-ui-component-switch', () => switchUI())
  // ElNotification({
  //   type: 'info',
  //   title: '「 专业版限时优惠 」',
  //   dangerouslyUseHTMLString: true,
  //   message: `
  //     <p>原价 999.00 元，现价 <b style="font-size: 18px; color: #ff4400;">799.00</b> 元，更有超值优惠合集，立减 <b style="color: #ff4400;">799.00</b> 元，点击<a href="https://one-step-admin.hurui.me/buy.html" target="_blank">查看详情</a>！</p>
  //   `,
  //   position: 'bottom-right',
  //   duration: 0,
  // })
})

function randomTheme() {
  const loading = ElLoading.service({
    lock: true,
    text: '随机风格切换中',
  })
  setTimeout(() => {
    loading.close()
  }, 1000)
  const colorScheme = (['light', 'dark'] as const)[Math.floor(Math.random() * 2)]
  const themeList = Object.keys(themes).map((key) => {
    return {
      label: key,
      value: (themes as any)[key],
    }
  }).filter((item) => {
    return item.value['color-scheme'] === colorScheme
  }).map(item => item.label)
  settingsStore.updateSettings({
    app: {
      ...(colorScheme === 'light' && { lightTheme: themeList[Math.floor(Math.random() * themeList.length)] }),
      ...(colorScheme === 'dark' && { darkTheme: themeList[Math.floor(Math.random() * themeList.length)] }),
      colorScheme: (['light', 'dark'] as const)[Math.floor(Math.random() * 2)],
    },
    menu: {
      mode: (['side', 'head', 'single', 'only-side', 'only-head', 'side-panel', 'head-panel'] as const)[Math.floor(Math.random() * 5)],
      isRounded: Math.random() > 0.5,
      style: (['', 'arrow', 'line', 'dot'] as const)[Math.floor(Math.random() * 4)],
    },
  })
}

const UIdialog = ref(false)
function switchUI() {
  UIdialog.value = true
}

function open(url: string) {
  window.open(url, 'top')
}
</script>

<template>
  <!-- <div class="buy-it" :class="{ actived: isActived }">
    <div class="item" @click="randomTheme">
      <SvgIcon name="i-ion:dice" />
      <span class="title">随机<br>风格</span>
    </div>
    <div class="item" @click="switchUI">
      <SvgIcon name="i-icon-park-twotone:components" />
      <span class="title">切换<br>组件库</span>
      <HDialog v-model="UIdialog" title="切换组件库" overlay>
        <div class="rounded-2 bg-green/20 px-4 py-2 text-sm/6 c-green-6">
          <p class="my-1">
            为了视觉风格统一，同时也能服务于更多开发者，本框架从 v4.0.0 版本开始，与 Element Plus 组件库进行了解耦，意味着可以轻松将 Element Plus 组件库替换成其他 UI 组件库，并且不会影响框架原本的功能。
          </p>
          <p class="my-1">
            演示站挑选了以下 6 款组件库作为示例，你可以点击访问并预览。
          </p>
        </div>
        <div class="mt-4 flex flex-wrap justify-center gap-4">
          <HButton @click="open('https://one-step-admin.hurui.me/antd-example/')">
            访问 Ant Design Vue 演示站
          </HButton>
          <HButton @click="open('https://one-step-admin.hurui.me/arco-example/')">
            访问 Arco Design Vue 演示站
          </HButton>
          <HButton @click="open('https://one-step-admin.hurui.me/naive-example/')">
            访问 Naive UI 演示站
          </HButton>
          <HButton @click="open('https://one-step-admin.hurui.me/tdesign-example/')">
            访问 TDesign 演示站
          </HButton>
          <HButton @click="open('https://one-step-admin.hurui.me/vexip-example/')">
            访问 Vexip UI 演示站
          </HButton>
          <HButton @click="open('https://one-step-admin.hurui.me/idux-example/')">
            访问 iDux 演示站
          </HButton>
        </div>
      </HDialog>
    </div>
    <div class="item" @click="open(`https://one-step-admin.hurui.me/buy.html`)">
      <SvgIcon name="i-ri:money-cny-box-line" />
      <span class="title">购买<br>专业版</span>
    </div>
    <div class="item" @click="open(`https://github.com/one-step-admin/basic`)">
      <SvgIcon name="i-ri:code-s-slash-line" />
      <span class="title">下载<br>基础版</span>
    </div>
    <div class="item" @click="open(`https://one-step-admin.hurui.me`)">
      <SvgIcon name="i-ri:book-read-line" />
      <span class="title">开发<br>文档</span>
    </div>
    <div class="item" @click="open(`https://one-step-admin.hurui.me/support.html`)">
      <SvgIcon name="i-ri:message-2-line" />
      <span class="title">技术<br>支持</span>
    </div>
  </div> -->
</template>

<style scoped>
.buy-it {
  position: fixed;
  inset-inline-end: -58px;
  top: 50%;
  z-index: 10;
  display: flex;
  flex-direction: column;
  width: 70px;
  overflow: hidden;
  border-radius: 5px 0 0 5px;
  transition: inset-inline-end 0.3s;
  transform: translateY(-50%);

  [dir="rtl"] & {
    border-radius: 0 5px 5px 0;
  }

  &.actived,
  &:hover {
    inset-inline-end: 0;
  }

  .item {
    display: flex;
    flex-direction: column;
    gap: 5px;
    justify-content: center;
    height: 70px;
    color: #fff;
    text-align: center;
    cursor: pointer;
    border-bottom: 1px solid #fff;
    opacity: 0.7;
    transition: 0.3s;

    &:hover {
      opacity: 1;
    }

    &:last-child {
      border-bottom: 0;
    }

    &:nth-child(1) {
      --uno: bg-blue;
    }

    &:nth-child(2) {
      --uno: bg-pink;
    }

    &:nth-child(3) {
      --uno: bg-orange;
    }

    &:nth-child(4) {
      --uno: bg-green;
    }

    &:nth-child(5) {
      --uno: bg-red;
    }

    &:nth-child(6) {
      --uno: bg-stone;
    }

    i {
      display: block;
      margin: 0 auto;
      font-size: 20px;
    }

    .title {
      display: inline-block;
      font-size: 12px;
    }
  }
}
</style>
