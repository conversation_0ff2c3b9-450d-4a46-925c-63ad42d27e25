<script setup lang="ts">
import useWindowStore from '@/store/modules/window'

defineOptions({
  name: 'Dashboard',
})

const windowStore = useWindowStore()
</script>

<template>
  <span
    class="flex-center cursor-pointer p-2" :class="{
      'op-30 cursor-not-allowed': windowStore.list.length === 0,
    }" @click="windowStore.list.length !== 0 && windowStore.toggleVisibleAllWindows()"
  >
    <SvgIcon :name="windowStore.isAllWindowsVisible && windowStore.list.length !== 0 ? 'i-ant-design:home-outlined' : 'i-ant-design:home-twotone'" />
  </span>
</template>
