<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <CalculationMode>Interpretation</CalculationMode>
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="1">
      <subjectSummary Ref="2" type="Stimulsoft.Report.Dictionary.StiJsonDatabase" isKey="true">
        <Alias>subjectSummary</Alias>
        <HeadersString />
        <Key />
        <Name>subjectSummary</Name>
        <PathData>C:\Users\<USER>\Desktop\需求文档\2\科目汇总月报\subjectSummary.json</PathData>
      </subjectSummary>
    </Databases>
    <DataSources isList="true" count="6">
      <root Ref="3" type="DataTableSource" isKey="true">
        <Alias>root</Alias>
        <Columns isList="true" count="4">
          <value>code,System.Decimal</value>
          <value>data,System.String</value>
          <value>msg,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>2c33f33f334f47919182eef687bf44f1</Key>
        <Name>root</Name>
        <NameInSource>subjectSummary.root</NameInSource>
      </root>
      <root_data Ref="4" type="DataTableSource" isKey="true">
        <Alias>root_data</Alias>
        <Columns isList="true" count="14">
          <value>hname,System.String</value>
          <value>hcode,System.String</value>
          <value>startDate,System.String</value>
          <value>bizDate,System.String</value>
          <value>lastSelectTime,System.String</value>
          <value>paymentTodayFee,System.Decimal</value>
          <value>paymentMonthFee,System.Decimal</value>
          <value>consumptionTodayFee,System.Decimal</value>
          <value>consumptionMonthFee,System.Decimal</value>
          <value>paymentDetails,System.String</value>
          <value>consumptionDetails,System.String</value>
          <value>memberRechargeConsumptionDetails,System.String</value>
          <value>memberRechargePaymentDetails,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>297804b1148a4a3c8bc8b46c4bd93d63</Key>
        <Name>root_data</Name>
        <NameInSource>subjectSummary.root_data</NameInSource>
      </root_data>
      <root_data_consumptionDetails Ref="5" type="DataTableSource" isKey="true">
        <Alias>root_data_consumptionDetails</Alias>
        <Columns isList="true" count="7">
          <value>subCode,System.String</value>
          <value>subName,System.String</value>
          <value>todayFee,System.Decimal</value>
          <value>monthFee,System.Decimal</value>
          <value>classCode,System.String</value>
          <value>className,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>8ba47604c14249d4bceb8df0f6fc323b</Key>
        <Name>root_data_consumptionDetails</Name>
        <NameInSource>subjectSummary.root_data_consumptionDetails</NameInSource>
      </root_data_consumptionDetails>
      <root_data_memberRechargeConsumptionDetails Ref="6" type="DataTableSource" isKey="true">
        <Alias>root_data_memberRechargeConsumptionDetails</Alias>
        <Columns isList="true" count="7">
          <value>subCode,System.String</value>
          <value>subName,System.String</value>
          <value>todayFee,System.Decimal</value>
          <value>monthFee,System.Decimal</value>
          <value>classCode,System.String</value>
          <value>className,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>8f0e69d3cbaf44cc9e67519aaba42c59</Key>
        <Name>root_data_memberRechargeConsumptionDetails</Name>
        <NameInSource>subjectSummary.root_data_memberRechargeConsumptionDetails</NameInSource>
      </root_data_memberRechargeConsumptionDetails>
      <root_data_memberRechargePaymentDetails Ref="7" type="DataTableSource" isKey="true">
        <Alias>root_data_memberRechargePaymentDetails</Alias>
        <Columns isList="true" count="7">
          <value>subCode,System.String</value>
          <value>subName,System.String</value>
          <value>todayFee,System.Decimal</value>
          <value>monthFee,System.Decimal</value>
          <value>classCode,System.String</value>
          <value>className,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>fe381b1f64194304adcafbb7e5c9b6fd</Key>
        <Name>root_data_memberRechargePaymentDetails</Name>
        <NameInSource>subjectSummary.root_data_memberRechargePaymentDetails</NameInSource>
      </root_data_memberRechargePaymentDetails>
      <root_data_paymentDetails Ref="8" type="DataTableSource" isKey="true">
        <Alias>root_data_paymentDetails</Alias>
        <Columns isList="true" count="7">
          <value>subCode,System.String</value>
          <value>subName,System.String</value>
          <value>todayFee,System.Decimal</value>
          <value>monthFee,System.Decimal</value>
          <value>classCode,System.String</value>
          <value>className,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>b5385b2dbb444471a24d5ecc04e98395</Key>
        <Name>root_data_paymentDetails</Name>
        <NameInSource>subjectSummary.root_data_paymentDetails</NameInSource>
      </root_data_paymentDetails>
    </DataSources>
    <Relations isList="true" count="5">
      <root Ref="9" type="DataRelation" isKey="true">
        <Alias>root</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="4" />
        <Dictionary isRef="1" />
        <Name>root</Name>
        <NameInSource>root_data</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>data</value>
        </ParentColumns>
        <ParentSource isRef="3" />
      </root>
      <root_data Ref="10" type="DataRelation" isKey="true">
        <Alias>root_data</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="5" />
        <Dictionary isRef="1" />
        <Name>root_data</Name>
        <NameInSource>root_data_consumptionDetails</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>consumptionDetails</value>
        </ParentColumns>
        <ParentSource isRef="4" />
      </root_data>
      <root_data Ref="11" type="DataRelation" isKey="true">
        <Alias>root_data</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="6" />
        <Dictionary isRef="1" />
        <Name>root_data</Name>
        <NameInSource>root_data_memberRechargeConsumptionDetails</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>memberRechargeConsumptionDetails</value>
        </ParentColumns>
        <ParentSource isRef="4" />
      </root_data>
      <root_data Ref="12" type="DataRelation" isKey="true">
        <Alias>root_data</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="7" />
        <Dictionary isRef="1" />
        <Name>root_data</Name>
        <NameInSource>root_data_memberRechargePaymentDetails</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>memberRechargePaymentDetails</value>
        </ParentColumns>
        <ParentSource isRef="4" />
      </root_data>
      <root_data Ref="13" type="DataRelation" isKey="true">
        <Alias>root_data</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="8" />
        <Dictionary isRef="1" />
        <Name>root_data</Name>
        <NameInSource>root_data_paymentDetails</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>paymentDetails</value>
        </ParentColumns>
        <ParentSource isRef="4" />
      </root_data>
    </Relations>
    <Report isRef="0" />
    <Resources isList="true" count="0" />
    <UserFunctions isList="true" count="0" />
    <Variables isList="true" count="1">
      <value>,todayConsumpionSum,todayConsumpionSum,,System.Double,_x0030_,False,False,False,False,,_x0031_9dde12b8a9c493386b9e07ebba82381</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <Key>7d9e1fa52c2d4e1a82884c6999ff3602</Key>
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="14" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="26">
        <页眉1 Ref="15" type="PageHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,19,0.8</ClientRectangle>
          <Components isList="true" count="2">
            <Text2 Ref="16" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,7,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Name>Text2</Name>
              <Page isRef="14" />
              <Parent isRef="15" />
              <Text>{root_data.hname}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text2>
            <Text6 Ref="17" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>13.4,0,5.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Name>Text6</Name>
              <Page isRef="14" />
              <Parent isRef="15" />
              <Text>第{PageNumber}页,共{TotalPageCount}页</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text6>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>页眉1</Name>
          <Page isRef="14" />
          <Parent isRef="14" />
        </页眉1>
        <报表标题区1 Ref="18" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,2,19,1.6</ClientRectangle>
          <Components isList="true" count="2">
            <Text5 Ref="19" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1,17.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Name>Text5</Name>
              <Page isRef="14" />
              <Parent isRef="18" />
              <Text>门店:{root_data.hname}  营业日:{root_data.bizDate} 最后查询时间：{root_data.lastSelectTime}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text5>
            <Text1 Ref="20" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6.6,0,5.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,12,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text1</Name>
              <Page isRef="14" />
              <Parent isRef="18" />
              <Text>酒店经营科目汇总月报</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text1>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>报表标题区1</Name>
          <Page isRef="14" />
          <Parent isRef="14" />
        </报表标题区1>
        <页眉2 Ref="21" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,4.4,19,0.8</ClientRectangle>
          <Components isList="true" count="4">
            <Text35 Ref="22" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,2.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>Text35</Name>
              <Page isRef="14" />
              <Parent isRef="21" />
              <Text>序号</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text35>
            <文本1 Ref="23" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>2.8,0,5.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>文本1</Name>
              <Page isRef="14" />
              <Parent isRef="21" />
              <Text>科目名称</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本1>
            <文本3 Ref="24" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>8.2,0,5.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>文本3</Name>
              <Page isRef="14" />
              <Parent isRef="21" />
              <Text>今日发生</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本3>
            <文本4 Ref="25" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>13.6,0,5.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>文本4</Name>
              <Page isRef="14" />
              <Parent isRef="21" />
              <Text>本月累计发生</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本4>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>页眉2</Name>
          <Page isRef="14" />
          <Parent isRef="14" />
        </页眉2>
        <栏首1 Ref="26" type="Stimulsoft.Report.Components.StiColumnHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,6,19,0.8</ClientRectangle>
          <Components isList="true" count="1">
            <文本2 Ref="27" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,19,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <Name>文本2</Name>
              <Page isRef="14" />
              <Parent isRef="26" />
              <Text>消费科目(营业收入)</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本2>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>栏首1</Name>
          <Page isRef="14" />
          <Parent isRef="14" />
        </栏首1>
        <分组页眉1 Ref="28" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,7.6,19,0</ClientRectangle>
          <Components isList="true" count="0" />
          <Condition>{root_data_consumptionDetails.classCode}</Condition>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>分组页眉1</Name>
          <Page isRef="14" />
          <Parent isRef="14" />
        </分组页眉1>
        <数据区1 Ref="29" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,8.4,19,0.8</ClientRectangle>
          <Components isList="true" count="4">
            <Text18 Ref="30" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>2.8,0,5.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,0,0,0</Margins>
              <Name>Text18</Name>
              <Page isRef="14" />
              <Parent isRef="29" />
              <Text>{root_data_consumptionDetails.subName}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text18>
            <Text47 Ref="31" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,2.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>Text47</Name>
              <Page isRef="14" />
              <Parent isRef="29" />
              <Text>{Line}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>SystemVariables</Type>
              <VertAlignment>Center</VertAlignment>
            </Text47>
            <文本5 Ref="32" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>8.2,0,5.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="1">
                <value>TextBrush,SolidBrushValue_x0028__x0022__x0023_00B0F0_x0022__x0029_</value>
              </Expressions>
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Hyperlink>#/?name=StoreCashierConsumption&amp;hcode={root_data.hcode}&amp;startDate={root_data.bizDate}&amp;endDate={root_data.bizDate}
&amp;subCode={root_data_consumptionDetails.subCode}</Hyperlink>
              <Margins>0,5,0,0</Margins>
              <Name>文本5</Name>
              <Page isRef="14" />
              <Parent isRef="29" />
              <Text>{root_data_consumptionDetails.todayFee}</Text>
              <TextBrush>[0:176:240]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本5>
            <文本6 Ref="33" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>13.6,0,5.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Hyperlink>#/?name=StoreCashierConsumption&amp;hcode={root_data.hcode}&amp;startDate={root_data.startDate}&amp;endDate={root_data.bizDate}
&amp;subCode={root_data_consumptionDetails.subCode}</Hyperlink>
              <Margins>0,5,0,0</Margins>
              <Name>文本6</Name>
              <Page isRef="14" />
              <Parent isRef="29" />
              <Text>{root_data_consumptionDetails.monthFee}</Text>
              <TextBrush>[0:176:240]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本6>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>root_data_consumptionDetails</DataSourceName>
          <Expressions isList="true" count="0" />
          <Filters isList="true" count="0" />
          <Name>数据区1</Name>
          <Page isRef="14" />
          <Parent isRef="14" />
          <Sort isList="true" count="0" />
        </数据区1>
        <分组页脚1 Ref="34" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,10,19,0.8</ClientRectangle>
          <Components isList="true" count="3">
            <文本7 Ref="35" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,8.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>文本7</Name>
              <Page isRef="14" />
              <Parent isRef="34" />
              <Text>{root_data_consumptionDetails.className}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本7>
            <文本8 Ref="36" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>8.2,0,5.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本8</Name>
              <Page isRef="14" />
              <Parent isRef="34" />
              <Text>{Sum(root_data_consumptionDetails.todayFee)}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本8>
            <文本9 Ref="37" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>13.6,0,5.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本9</Name>
              <Page isRef="14" />
              <Parent isRef="34" />
              <Text>{Sum(root_data_consumptionDetails.monthFee)}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </文本9>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>分组页脚1</Name>
          <Page isRef="14" />
          <Parent isRef="14" />
        </分组页脚1>
        <栏尾1 Ref="38" type="Stimulsoft.Report.Components.StiColumnFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,11.6,19,0.8</ClientRectangle>
          <Components isList="true" count="3">
            <文本19 Ref="39" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>8.2,0,5.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Hyperlink>#/?name=StoreCashierConsumption&amp;hcode={root_data.hcode}&amp;startDate={root_data.bizDate}&amp;endDate={root_data.bizDate}</Hyperlink>
              <Margins>0,5,0,0</Margins>
              <Name>文本19</Name>
              <Page isRef="14" />
              <Parent isRef="38" />
              <Text>{Sum(root_data_consumptionDetails.todayFee)}</Text>
              <TextBrush>[0:176:240]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </文本19>
            <文本18 Ref="40" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,8.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>文本18</Name>
              <Page isRef="14" />
              <Parent isRef="38" />
              <Text>小计</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本18>
            <文本20 Ref="41" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>13.6,0,5.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Hyperlink>#/?name=StoreCashierConsumption&amp;hcode={root_data.hcode}&amp;startDate={root_data.startDate}&amp;endDate={root_data.bizDate}</Hyperlink>
              <Margins>0,5,0,0</Margins>
              <Name>文本20</Name>
              <Page isRef="14" />
              <Parent isRef="38" />
              <Text>{Sum(root_data_consumptionDetails.monthFee)}</Text>
              <TextBrush>[0:176:240]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </文本20>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>栏尾1</Name>
          <Page isRef="14" />
          <Parent isRef="14" />
        </栏尾1>
        <栏首2 Ref="42" type="Stimulsoft.Report.Components.StiColumnHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,13.2,19,0.8</ClientRectangle>
          <Components isList="true" count="1">
            <文本10 Ref="43" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,19,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <Name>文本10</Name>
              <Page isRef="14" />
              <Parent isRef="42" />
              <Text>消费科目(非营业收入)</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本10>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>栏首2</Name>
          <Page isRef="14" />
          <Parent isRef="14" />
        </栏首2>
        <分组页眉2 Ref="44" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,14.8,19,0</ClientRectangle>
          <Components isList="true" count="0" />
          <Condition>{root_data_memberRechargeConsumptionDetails.classCode}</Condition>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>分组页眉2</Name>
          <Page isRef="14" />
          <Parent isRef="14" />
        </分组页眉2>
        <数据区2 Ref="45" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,15.6,19,0.8</ClientRectangle>
          <Components isList="true" count="4">
            <文本11 Ref="46" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>2.8,0,5.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,0,0,0</Margins>
              <Name>文本11</Name>
              <Page isRef="14" />
              <Parent isRef="45" />
              <Text>{root_data_memberRechargeConsumptionDetails.subName}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本11>
            <文本12 Ref="47" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,2.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>文本12</Name>
              <Page isRef="14" />
              <Parent isRef="45" />
              <Text>{Line}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>SystemVariables</Type>
              <VertAlignment>Center</VertAlignment>
            </文本12>
            <文本13 Ref="48" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>8.2,0,5.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本13</Name>
              <Page isRef="14" />
              <Parent isRef="45" />
              <Text>{root_data_memberRechargeConsumptionDetails.todayFee}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本13>
            <文本14 Ref="49" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>13.6,0,5.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本14</Name>
              <Page isRef="14" />
              <Parent isRef="45" />
              <Text>{root_data_memberRechargeConsumptionDetails.monthFee}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本14>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>root_data_memberRechargeConsumptionDetails</DataSourceName>
          <Expressions isList="true" count="0" />
          <Filters isList="true" count="0" />
          <Name>数据区2</Name>
          <Page isRef="14" />
          <Parent isRef="14" />
          <Sort isList="true" count="0" />
        </数据区2>
        <分组页脚2 Ref="50" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,17.2,19,0.8</ClientRectangle>
          <Components isList="true" count="3">
            <文本15 Ref="51" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,8.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>文本15</Name>
              <Page isRef="14" />
              <Parent isRef="50" />
              <Text>{root_data_memberRechargeConsumptionDetails.className}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本15>
            <文本16 Ref="52" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>8.2,0,5.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本16</Name>
              <Page isRef="14" />
              <Parent isRef="50" />
              <Text>{Sum(root_data_memberRechargeConsumptionDetails.todayFee)}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </文本16>
            <文本17 Ref="53" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>13.6,0,5.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本17</Name>
              <Page isRef="14" />
              <Parent isRef="50" />
              <Text>{Sum(root_data_memberRechargeConsumptionDetails.monthFee)}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </文本17>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>分组页脚2</Name>
          <Page isRef="14" />
          <Parent isRef="14" />
        </分组页脚2>
        <栏尾2 Ref="54" type="Stimulsoft.Report.Components.StiColumnFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,18.8,19,0.8</ClientRectangle>
          <Components isList="true" count="3">
            <文本37 Ref="55" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,8.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>文本37</Name>
              <Page isRef="14" />
              <Parent isRef="54" />
              <Text>小计</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本37>
            <文本38 Ref="56" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>8.2,0,5.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本38</Name>
              <Page isRef="14" />
              <Parent isRef="54" />
              <Text>{Sum(root_data_memberRechargeConsumptionDetails.todayFee)}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </文本38>
            <文本39 Ref="57" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>13.6,0,5.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本39</Name>
              <Page isRef="14" />
              <Parent isRef="54" />
              <Text>{Sum(root_data_memberRechargeConsumptionDetails.monthFee)}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </文本39>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>栏尾2</Name>
          <Page isRef="14" />
          <Parent isRef="14" />
        </栏尾2>
        <页眉3 Ref="58" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,20.4,19,0.8</ClientRectangle>
          <Components isList="true" count="3">
            <文本46 Ref="59" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,8.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>文本46</Name>
              <Page isRef="14" />
              <Parent isRef="58" />
              <Text>消费合计</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本46>
            <文本47 Ref="60" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>8.2,0,5.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本47</Name>
              <Page isRef="14" />
              <Parent isRef="58" />
              <Text>{root_data.consumptionTodayFee}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本47>
            <文本48 Ref="61" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>13.6,0,5.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本48</Name>
              <Page isRef="14" />
              <Parent isRef="58" />
              <Text>{root_data.consumptionMonthFee}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本48>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>页眉3</Name>
          <Page isRef="14" />
          <Parent isRef="14" />
        </页眉3>
        <栏首3 Ref="62" type="Stimulsoft.Report.Components.StiColumnHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,22,19,0.8</ClientRectangle>
          <Components isList="true" count="1">
            <文本21 Ref="63" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,19,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <Name>文本21</Name>
              <Page isRef="14" />
              <Parent isRef="62" />
              <Text>付款科目</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本21>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>栏首3</Name>
          <Page isRef="14" />
          <Parent isRef="14" />
        </栏首3>
        <分组页眉3 Ref="64" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,23.6,19,0</ClientRectangle>
          <Components isList="true" count="0" />
          <Condition>{root_data_paymentDetails.classCode}</Condition>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>分组页眉3</Name>
          <Page isRef="14" />
          <Parent isRef="14" />
        </分组页眉3>
        <数据区3 Ref="65" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,24.4,19,0.8</ClientRectangle>
          <Components isList="true" count="4">
            <文本22 Ref="66" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>2.8,0,5.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,0,0,0</Margins>
              <Name>文本22</Name>
              <Page isRef="14" />
              <Parent isRef="65" />
              <Text>{root_data_paymentDetails.subName}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本22>
            <文本23 Ref="67" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,2.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>文本23</Name>
              <Page isRef="14" />
              <Parent isRef="65" />
              <Text>{Line}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>SystemVariables</Type>
              <VertAlignment>Center</VertAlignment>
            </文本23>
            <文本24 Ref="68" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>8.2,0,5.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Hyperlink>#/?name=StoreCashierPayment&amp;hcode={root_data.hcode}&amp;startDate={root_data.bizDate}&amp;endDate={root_data.bizDate}
&amp;subCode={root_data_paymentDetails.subCode}</Hyperlink>
              <Margins>0,5,0,0</Margins>
              <Name>文本24</Name>
              <Page isRef="14" />
              <Parent isRef="65" />
              <Text>{root_data_paymentDetails.todayFee}</Text>
              <TextBrush>[0:176:240]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本24>
            <文本25 Ref="69" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>13.6,0,5.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Hyperlink>#/?name=StoreCashierPayment&amp;hcode={root_data.hcode}&amp;startDate={root_data.startDate}&amp;endDate={root_data.bizDate}
&amp;subCode={root_data_paymentDetails.subCode}</Hyperlink>
              <Margins>0,5,0,0</Margins>
              <Name>文本25</Name>
              <Page isRef="14" />
              <Parent isRef="65" />
              <Text>{root_data_paymentDetails.monthFee}</Text>
              <TextBrush>[0:176:240]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本25>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>root_data_paymentDetails</DataSourceName>
          <Expressions isList="true" count="0" />
          <Filters isList="true" count="0" />
          <Name>数据区3</Name>
          <Page isRef="14" />
          <Parent isRef="14" />
          <Sort isList="true" count="0" />
        </数据区3>
        <分组页脚3 Ref="70" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,26,19,0.8</ClientRectangle>
          <Components isList="true" count="3">
            <文本26 Ref="71" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,8.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>文本26</Name>
              <Page isRef="14" />
              <Parent isRef="70" />
              <Text>{root_data_paymentDetails.className}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本26>
            <文本27 Ref="72" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>8.2,0,5.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本27</Name>
              <Page isRef="14" />
              <Parent isRef="70" />
              <Text>{Sum(root_data_paymentDetails.todayFee)}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </文本27>
            <文本28 Ref="73" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>13.6,0,5.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本28</Name>
              <Page isRef="14" />
              <Parent isRef="70" />
              <Text>{Sum(root_data_paymentDetails.monthFee)}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </文本28>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>分组页脚3</Name>
          <Page isRef="14" />
          <Parent isRef="14" />
        </分组页脚3>
        <栏尾3 Ref="74" type="Stimulsoft.Report.Components.StiColumnFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,27.6,19,0.8</ClientRectangle>
          <Components isList="true" count="3">
            <文本40 Ref="75" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,8.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>文本40</Name>
              <Page isRef="14" />
              <Parent isRef="74" />
              <Text>小计</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本40>
            <文本41 Ref="76" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>8.2,0,5.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Hyperlink>#/?name=StoreCashierPayment&amp;hcode={root_data.hcode}&amp;startDate={root_data.bizDate}&amp;endDate={root_data.bizDate}</Hyperlink>
              <Margins>0,5,0,0</Margins>
              <Name>文本41</Name>
              <Page isRef="14" />
              <Parent isRef="74" />
              <Text>{Sum(root_data_paymentDetails.todayFee)}</Text>
              <TextBrush>[0:176:240]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </文本41>
            <文本42 Ref="77" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>13.6,0,5.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Hyperlink>#/?name=StoreCashierPayment&amp;hcode={root_data.hcode}&amp;startDate={root_data.startDate}&amp;endDate={root_data.bizDate}</Hyperlink>
              <Margins>0,5,0,0</Margins>
              <Name>文本42</Name>
              <Page isRef="14" />
              <Parent isRef="74" />
              <Text>{Sum(root_data_paymentDetails.monthFee)}</Text>
              <TextBrush>[0:176:240]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </文本42>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>栏尾3</Name>
          <Page isRef="14" />
          <Parent isRef="14" />
        </栏尾3>
        <栏首4 Ref="78" type="Stimulsoft.Report.Components.StiColumnHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,29.2,19,0.8</ClientRectangle>
          <Components isList="true" count="1">
            <文本29 Ref="79" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,19,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <Name>文本29</Name>
              <Page isRef="14" />
              <Parent isRef="78" />
              <Text>付款科目(会员充值付款)</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本29>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>栏首4</Name>
          <Page isRef="14" />
          <Parent isRef="14" />
        </栏首4>
        <分组页眉4 Ref="80" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,30.8,19,0</ClientRectangle>
          <Components isList="true" count="0" />
          <Condition>{root_data_memberRechargePaymentDetails.classCode}</Condition>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>分组页眉4</Name>
          <Page isRef="14" />
          <Parent isRef="14" />
        </分组页眉4>
        <数据区4 Ref="81" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,31.6,19,0.8</ClientRectangle>
          <Components isList="true" count="4">
            <文本30 Ref="82" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>2.8,0,5.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,0,0,0</Margins>
              <Name>文本30</Name>
              <Page isRef="14" />
              <Parent isRef="81" />
              <Text>{root_data_memberRechargePaymentDetails.subName}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本30>
            <文本31 Ref="83" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,2.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>文本31</Name>
              <Page isRef="14" />
              <Parent isRef="81" />
              <Text>{Line}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>SystemVariables</Type>
              <VertAlignment>Center</VertAlignment>
            </文本31>
            <文本32 Ref="84" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>8.2,0,5.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本32</Name>
              <Page isRef="14" />
              <Parent isRef="81" />
              <Text>{root_data_memberRechargePaymentDetails.todayFee}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本32>
            <文本33 Ref="85" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>13.6,0,5.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本33</Name>
              <Page isRef="14" />
              <Parent isRef="81" />
              <Text>{root_data_memberRechargePaymentDetails.monthFee}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本33>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>root_data_memberRechargePaymentDetails</DataSourceName>
          <Expressions isList="true" count="0" />
          <Filters isList="true" count="0" />
          <Name>数据区4</Name>
          <Page isRef="14" />
          <Parent isRef="14" />
          <Sort isList="true" count="0" />
        </数据区4>
        <分组页脚4 Ref="86" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,33.2,19,0.8</ClientRectangle>
          <Components isList="true" count="3">
            <文本34 Ref="87" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,8.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>文本34</Name>
              <Page isRef="14" />
              <Parent isRef="86" />
              <Text>{root_data_memberRechargePaymentDetails.className}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本34>
            <文本35 Ref="88" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>8.2,0,5.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本35</Name>
              <Page isRef="14" />
              <Parent isRef="86" />
              <Text>{Sum(root_data_memberRechargePaymentDetails.todayFee)}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </文本35>
            <文本36 Ref="89" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>13.6,0,5.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本36</Name>
              <Page isRef="14" />
              <Parent isRef="86" />
              <Text>{Sum(root_data_memberRechargePaymentDetails.monthFee)}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </文本36>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>分组页脚4</Name>
          <Page isRef="14" />
          <Parent isRef="14" />
        </分组页脚4>
        <栏尾4 Ref="90" type="Stimulsoft.Report.Components.StiColumnFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,34.8,19,0.8</ClientRectangle>
          <Components isList="true" count="3">
            <文本43 Ref="91" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,8.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>文本43</Name>
              <Page isRef="14" />
              <Parent isRef="90" />
              <Text>小计</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本43>
            <文本44 Ref="92" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>8.2,0,5.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本44</Name>
              <Page isRef="14" />
              <Parent isRef="90" />
              <Text>{Sum(root_data_memberRechargePaymentDetails.todayFee)}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </文本44>
            <文本45 Ref="93" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>13.6,0,5.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本45</Name>
              <Page isRef="14" />
              <Parent isRef="90" />
              <Text>{Sum(root_data_memberRechargePaymentDetails.monthFee)}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </文本45>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>栏尾4</Name>
          <Page isRef="14" />
          <Parent isRef="14" />
        </栏尾4>
        <页眉4 Ref="94" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,36.4,19,0.8</ClientRectangle>
          <Components isList="true" count="3">
            <文本49 Ref="95" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,8.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>文本49</Name>
              <Page isRef="14" />
              <Parent isRef="94" />
              <Text>付款合计</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本49>
            <文本50 Ref="96" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>8.2,0,5.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本50</Name>
              <Page isRef="14" />
              <Parent isRef="94" />
              <Text>{root_data.paymentTodayFee}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本50>
            <文本51 Ref="97" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>13.6,0,5.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>文本51</Name>
              <Page isRef="14" />
              <Parent isRef="94" />
              <Text>{root_data.paymentMonthFee}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本51>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>页眉4</Name>
          <Page isRef="14" />
          <Parent isRef="14" />
        </页眉4>
        <数据区5 Ref="98" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,38,19,0</ClientRectangle>
          <Components isList="true" count="0" />
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>root_data_paymentDetails</DataSourceName>
          <Expressions isList="true" count="0" />
          <Filters isList="true" count="0" />
          <Name>数据区5</Name>
          <Page isRef="14" />
          <Parent isRef="14" />
          <Sort isList="true" count="0" />
        </数据区5>
      </Components>
      <Conditions isList="true" count="0" />
      <Expressions isList="true" count="0" />
      <Guid>be3c1f4638d5436a8634a6f5db5dca3b</Guid>
      <Margins>1,1,1,1</Margins>
      <Name>Page1</Name>
      <PageHeight>41.7</PageHeight>
      <PageWidth>21</PageWidth>
      <Report isRef="0" />
    </Page1>
  </Pages>
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>报表</ReportAlias>
  <ReportAuthor>xiao lin</ReportAuthor>
  <ReportChanged>7/1/2025 2:46:06 PM</ReportChanged>
  <ReportCreated>3/19/2025 5:46:01 PM</ReportCreated>
  <ReportFile>D:\11\2\pms-report\public\reports\subjectSummaryReport.mrt</ReportFile>
  <ReportGuid>e2a69bb289f442f493d49e6aba3f5b34</ReportGuid>
  <ReportName>报表</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2024.3.5.0</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class 报表 : Stimulsoft.Report.StiReport
    {
        public 报表()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
		#endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>